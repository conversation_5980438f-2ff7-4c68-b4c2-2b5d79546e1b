REACT_APP_FAKE_KEY=DEV_KEY
REACT_APP_API_BASE_URL=http://dev
REACT_APP_USER_MICRO_URL=usercomponents@https://autotow.redi3.dev/microfrontenduser/usercomponents.js
REACT_APP_SIDEMENU_MICRO_URL=sidemenu@https://autotow.redi3.dev/microfrontendsidemenu/remoteEntry.js
REACT_APP_CRM_MICRO_URL=crmcomponents@https://autotow.redi3.dev/microfrontendcrm/crmcomponents.js
REACT_APP_JOB_MICRO_URL=jobcomponents@https://autotow.redi3.dev/microfrontendjob/jobcomponents.js
REACT_APP_COMMON_MICRO_URL=commoncomponents@https://autotow.redi3.dev/microfrontendcommon/commoncomponents.js
REACT_APP_FORM_MICRO_URL=formcomponents@https://autotow.redi3.dev/microfrontendform/formcomponents.js
REACT_APP_LOGIN_MICRO_URL=logincomponents@https://autotow.redi3.dev/microfrontendlogin/logincomponents.js
REACT_APP_LISTINGMANAGEMENT_MICRO_URL=listingmanagementcomponents@https://autotow.redi3.dev/microfrontendlistingmanagement/listingmanagementcomponents.js
DOCKERHUB_USERNAME=jadredi
DOCKERHUB_NAMESPACE=redisoftware
DOCKER_REPO=autotow-microfrontend-container
IMAGETAGVERSION=dev-latest
AZURE_GLOBAL_RESOURCE_GROUP=test_redi
AZURE_RESOURCE_GROUP=test-autotow
AZURE_TENANT_ID=ee84645d-1f03-4d0e-8e54-377dbf082ab2
AZURE_SERVICE_PRINCIPAL_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
AZURE_APP_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
AZURE_ACCOUNT_STORAGE=testredi1storage
AZURE_STORAGE_VOLUME=autotow-traefik-files-volume
SYSTEM_ENVIRONMENT=development
AZURE_ENVIRONMENT_NAME=test-autotow
AZURE_IDENTITY_NAME=test-redi-managed-identity