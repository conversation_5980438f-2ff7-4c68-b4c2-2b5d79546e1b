REACT_APP_FAKE_KEY=LOCAL_KEY
# REACT_APP_API_BASE_URL=http://localhost:5103

# Local
#REACT_APP_SIDEMENU_MICRO_URL=sidemenu@http://localhost:3001/remoteEntry.js
#REACT_APP_USER_MICRO_URL=usercomponents@http://localhost:3002/usercomponents.js
#REACT_APP_CRM_MICRO_URL=crmcomponents@http://localhost:3003/crmcomponents.js
#REACT_APP_LOGIN_MICRO_URL=logincomponents@http://localhost:3004/logincomponents.js
REACT_APP_JOB_MICRO_URL=jobcomponents@http://localhost:3006/jobcomponents.js
REACT_APP_COMMON_MICRO_URL=commoncomponents@http://localhost:3007/commoncomponents.js
REACT_APP_LISTINGMANAGEMENT_MICRO_URL=listingmanagementcomponents@http://localhost:3012/listingmanagementcomponents.js

# Test

REACT_APP_SIDEMENU_MICRO_URL=sidemenu@https://autotow.redi3.dev/microfrontendsidemenu/remoteEntry.js
REACT_APP_USER_MICRO_URL=usercomponents@https://autotow.redi3.dev/microfrontenduser/usercomponents.js
REACT_APP_CRM_MICRO_URL=crmcomponents@https://autotow.redi3.dev/microfrontendcrm/crmcomponents.js
REACT_APP_LOGIN_MICRO_URL=logincomponents@https://autotow.redi3.dev/microfrontendlogin/logincomponents.js
#REACT_APP_JOB_MICRO_URL=jobcomponents@https://autotow.redi3.dev/microfrontendjob/jobcomponents.js
#REACT_APP_COMMON_MICRO_URL=commoncomponents@https://autotow.redi3.dev/microfrontendcommon/commoncomponents.js
#REACT_APP_LISTINGMANAGEMENT_MICRO_URL=listingmanagementcomponents@https://autotow.redi3.dev/microfrontendlistingmanagement/listingmanagementcomponents.js

