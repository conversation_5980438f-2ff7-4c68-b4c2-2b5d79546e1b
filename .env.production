REACT_APP_FAKE_KEY=PROD_KEY
REACT_APP_API_BASE_URL=http://prod
REACT_APP_USER_MICRO_URL=usercomponents@https://autotow.redissprod.com.au/microfrontenduser/usercomponents.js
REACT_APP_SIDEMENU_MICRO_URL=sidemenu@https://autotow.redissprod.com.au/microfrontendsidemenu/remoteEntry.js
REACT_APP_CRM_MICRO_URL=crmcomponents@https://autotow.redissprod.com.au/microfrontendcrm/crmcomponents.js
REACT_APP_JOB_MICRO_URL=jobcomponents@https://autotow.redissprod.com.au/microfrontendjob/jobcomponents.js
REACT_APP_COMMON_MICRO_URL=commoncomponents@https://autotow.redissprod.com.au/microfrontendcommon/commoncomponents.js
REACT_APP_LOGIN_MICRO_URL=logincomponents@https://autotow.redissprod.com.au/microfrontendlogin/logincomponents.js
REACT_APP_LISTINGMANAGEMENT_MICRO_URL=listingmanagementcomponents@https://autotow.redissprod.com.au/microfrontendlistingmanagement/listingmanagementcomponents.js
DOCKERHUB_USERNAME=jadredi
DOCKERHUB_NAMESPACE=redisoftware
DOCKER_REPO=redi-reacthost-base
IMAGETAGVERSION=dev-latest
AZURE_GLOBAL_RESOURCE_GROUP=test_redi
AZURE_RESOURCE_GROUP=test-base
AZURE_TENANT_ID=ee84645d-1f03-4d0e-8e54-377dbf082ab2
AZURE_APP_ID=ddbc6916-4198-479d-bed6-c0f3fa23a294
AZURE_ACCOUNT_STORAGE=testredi1storage
AZURE_STORAGE_VOLUME=traefik-files-volume
SYSTEM_ENVIRONMENT=development
AZURE_ENVIRONMENT_NAME=test-base
AZURE_IDENTITY_NAME=test-redi-managed-identity