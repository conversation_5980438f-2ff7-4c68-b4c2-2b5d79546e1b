# Guide to becoming a React Wizard 2.0
- **!Important!** Change the name field in `package.json` to be unique and in the format of `company-microfrontend-name`, 
- Use `yarn start` to start the project

# Environment Variables
- Give your project a unique port in .env.local
- Example local url: `REACT_APP_USER_MICRO_URL=usercomponents@http://localhost:3002/usercomponents.js`
- Example Test url:  `REACT_APP_USER_MICRO_URL=usercomponents@https://base.redi3.dev/microfrontenduser/usercomponents.js`
- .env.local            - Local development
- .env.production.local - Test server
- .env.production       - Production server

# Project structure
- Shared components that are designed to be reusable should be in `src/components` ie. `src/components/YesNoDialog/YesNoDialog.tsx`
- Nest components that are used by a component or are a sub page ie. `UserManagement/UserForm/UserForm.tsx`
- Component files should be capitalized and inside a folder with the same name ie. `UserForm/UserForm.tsx`
- Files that are not components should not be capitalized
- Dtos are located in `@types/dtos` and must be filetype `.d.ts'

# React
- In our new micro frontends, we are switching to React Functional Components. Look at the `pages/demo/BaseFunctionalComponent` component in the demo folder for a basic example.
- The Demo page contains a bunch of common material components as well as examples of commonly used elements

# Routing
- `routes.tsx` contains any routes that need to be exported to the main container, these will appear in the SideMenu unless 'excludeFromMenu' is specified
- `router.tsx` contains routes that are only local to this project and aren't exported to the main container
- Example of url parameters and routing state:
```javascript
// Navigate and set route parameters or state
import { useNavigate } from 'react-router-dom';

const navigate = useNavigate();
navigate(`/UserManagement/${props.rowData.userId}`, { state: { initialEditMode: true } });

// Retrieve route parameters or state
import { useLocation, useParams } from 'react-router-dom';

const { id } = useParams(); 		// Url Params
const { state } = useLocation(); 	// Route state
const myStateVar = state?.initialEditMode;
```

# Icons
- The icon library we are using is FontAwesome and located in `config/fa_icons.ts` which has more detailed information
- Icons are exported from this project to the Micro UI Container from the same `config/fa_icons.ts` file

# Data Table
- The `components/DataTable/DataTable.tsx` component is the main table component that will be used for all major lists
- ie. `pages/UserManagement/User/UserAudit/UserAudit.tsx`

# Component Export to Container via Webpack Module Federation

- Update the `ModuleFederationPlugin` to match your component
- Located in `Webpack.config.dev.js` and `webpack.config.prod.js`
- Add any components you want to export to the `exposes` property
```javascript
// redi-microfrontend-user - webpack.config.dev.js
new ModuleFederationPlugin({
  name: "usercomponents",
  filename: "usercomponents.js",
  exposes: {
    "./UserManagement": './src/pages/UserManagement/UserManagement.tsx',
    "./routes": './src/config/routes.tsx',
    "./icons": './src/config/fa_icons.ts'
  },
  shared: {...}
});
```
- In the Host Container Import these components
```javascript
// reactjs-base (v2 branch) - webpack.config.dev.js
new ModuleFederationPlugin({
  name: "host",
  filename: "hostEntry.js",
  exposes: {
    //'./Service': './src/Service', //An example for below issue
  },
  remotes: {
    usercomponents: process.env.REACT_APP_USER_MICRO_URL
  },
  shared: {...}
});
```
- Fix hot reload breaking by adding your project to the `excludeChunks` array
- This is also required in webpack.config.prod.js so that we can hot reload micro frontends that are on the test server when running the container locally
```javascript
// redi-microfrontend-user - webpack.config.dev.js and webpack.config.prod.js
plugins: [
  // Generates an `index.html` file with the <script> injected.
  new HtmlWebpackPlugin(
    Object.assign(
      {},
      {
        inject: true,
        template: paths.appHtml,
        excludeChunks: ['usercomponents'] // Add here
      },
      undefined
    )
  ),
  ...
];
```

# Redi-Formik-Material dependencies that could eventually be remove once it has been updated

- redi-component-utils
- react-css-modules
- memoize-one
- @mui/icons-material