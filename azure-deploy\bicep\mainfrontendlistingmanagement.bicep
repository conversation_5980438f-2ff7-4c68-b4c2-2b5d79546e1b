param location string = resourceGroup().location
param enviornmentName string
param identityName string
param globalResourceGroupName string
param imageTagVersion string
param dockerRepoName string
param dockerUserName string
@secure()
param dockerPassword string

module identityModule 'modules/container-apps/identity.bicep' = {
  name: '${deployment().name}--identity'
  scope: resourceGroup(globalResourceGroupName)
  params: {
    location: location
    identityName: identityName
  }
}

module microfrontendlistingmanagementModule 'modules/container-apps/microfrontendlistingmanagement.bicep' = {
  name: '${deployment().name}--microfrontendlistingmanagement'
  params: {
    imageTagVersion:imageTagVersion
    dockerUserName: dockerUserName
    enviornmentName: enviornmentName
    location: location
    dockerPasswordRef: dockerPassword
    managedIdentityObjectId: identityModule.outputs.identityId
    dockerRepoName: dockerRepoName
  }
}
