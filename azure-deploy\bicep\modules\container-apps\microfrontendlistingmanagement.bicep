param location string
param enviornmentName string
param dockerUserName string
@secure()
param dockerPasswordRef string
param managedIdentityObjectId string
param imageTagVersion string
param dockerRepoName string

resource enviornment 'Microsoft.App/managedEnvironments@2022-10-01' existing = {
  name: enviornmentName
}

resource microfrontendlistingmanagement 'Microsoft.App/containerApps@2024-10-02-preview' = {
  name: dockerRepoName
  location: location
  identity: {
     type:'UserAssigned'
     userAssignedIdentities: {
      '${managedIdentityObjectId}': {}
     }
  }
  properties: {
    environmentId: enviornment.id
    template: { 
      containers: [
        {
          name: dockerRepoName
          image: 'redisoftware/${dockerRepoName}:${imageTagVersion}'
          resources: {
            cpu: json('0.25')
            memory: '.5Gi'
          }
        }
      ]
      scale: {
        minReplicas: 0
        maxReplicas: 1
        cooldownPeriod: 10800
      }
    }
    configuration: {
      secrets: [
        {
          name: 'dockerpasswordref'
          value: dockerPasswordRef
        }
      ]
      registries: [
        {
          server: 'index.docker.io'
          username: dockerUserName
          passwordSecretRef: 'dockerpasswordref'
        }
      ]
      ingress: {
        external: false
        targetPort: 80
      }
    }
  }
}
