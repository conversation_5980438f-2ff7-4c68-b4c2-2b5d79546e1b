param location string
param enviornmentName string
param dockerUserName string
@secure()
param dockerPasswordRef string
param managedIdentityObjectId string
param imageTagVersion string
param dockerRepoName string

resource enviornment 'Microsoft.App/managedEnvironments@2022-10-01' existing = {
  name: enviornmentName
}

resource testapiservice 'Microsoft.App/containerApps@2022-10-01' = {
  name: dockerRepoName
  location: location
  identity: {
     type:'UserAssigned'
     userAssignedIdentities: {
      '${managedIdentityObjectId}': {}
     }
  }
  properties: {
    environmentId: enviornment.id
    template: { 
      containers: [
        {
          name: dockerRepoName
          image: 'redisoftware/${dockerRepoName}:${imageTagVersion}'
          resources: {
            cpu: json('0.25')
            memory: '.5Gi'
          }
        }
      ]
      scale: {
        minReplicas: 0
      }
    }
    configuration: {
      secrets: [
        {
          name: 'dockerpasswordref'
          value: dockerPasswordRef
        }
      ]
      registries: [
        {
          server: 'index.docker.io'
          username: dockerUser<PERSON>ame
          passwordSecretRef: 'dockerpasswordref'
        }
      ]
      ingress: {
        external: false
        targetPort: 80
      }
    }
  }
}
