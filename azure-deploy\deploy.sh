#Run main bicep
az deployment group create --resource-group $AZURE_RESOURCE_GROUP \
--template-file azure-deploy/bicep/mainfrontendlistingmanagement.bicep \
--parameters dockerUserName=$DOCKERHUB_USERNAME dockerPassword=$DOCKERHUB_PASSWORD \
globalResourceGroupName=$AZURE_GLOBAL_RESOURCE_GROUP \
enviornmentName=$AZURE_ENVIRONMENT_NAME \
identityName=$AZURE_IDENTITY_NAME \
imageTagVersion=$IMAGETAGVERSION \
dockerRepoName=$DOCKER_REPO

echo 'Restart Container'
# Restart Container from latest revision
az config set extension.use_dynamic_install=yes_without_prompt
latestRevision=$(az containerapp revision list --resource-group $AZURE_RESOURCE_GROUP --name $DOCKER_REPO --query '[].name | max(@)' | tr -cd '[:alnum:]._-')
echo "Latest revision Name: $latestRevision"

if [[ -n "$latestRevision" ]]; then
    az containerapp revision restart --resource-group $AZURE_RESOURCE_GROUP --name $DOCKER_REPO --revision $latestRevision
else
    az containerapp revision restart --resource-group $AZURE_RESOURCE_GROUP --name $DOCKER_REPO
fi
echo 'Done restart'