http:
  services:
    redi-microfrontend-common:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: 'https://DOCKER_REPO.internal.{{ env "CONTAINER_APP_ENV_DNS_SUFFIX" }}'
  middlewares:
    redirecthttps:
      redirectScheme:
        scheme: https
    microfrontendcommon-stripprefix:
      stripPrefix:
        prefixes:
          - "/microfrontendcommon"
        forceSlash: false
  routers:
    microfrontendcommonsecure:
      rule: "PathPrefix(`/microfrontendcommon`)"
      middlewares:
        - "microfrontendcommon-stripprefix"
      service: redi-microfrontend-common

