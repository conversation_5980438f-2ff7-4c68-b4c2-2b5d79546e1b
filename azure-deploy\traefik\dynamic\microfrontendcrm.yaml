http:
  services:
    redi-microfrontend-crm:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: 'https://DOCKER_REPO.internal.{{ env "CONTAINER_APP_ENV_DNS_SUFFIX" }}'
  middlewares:
    redirecthttps:
      redirectScheme:
        scheme: https
    microfrontendcrm-stripprefix:
      stripPrefix:
        prefixes:
          - "/microfrontendcrm"
        forceSlash: false
  routers:
    microfrontendcrmsecure:
      rule: "PathPrefix(`/microfrontendcrm`)"
      middlewares:
        - "microfrontendcrm-stripprefix"
      service: redi-microfrontend-crm

