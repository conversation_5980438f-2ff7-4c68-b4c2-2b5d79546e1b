http:
  services:
    redi-microfrontend-listingmanagement:
      loadBalancer:
        passHostHeader: false
        servers:
          - url: 'https://DOCKER_REPO.internal.{{ env "CONTAINER_APP_ENV_DNS_SUFFIX" }}'
  middlewares:
    redirecthttps:
      redirectScheme:
        scheme: https
    microfrontendlistingmanagement-stripprefix:
      stripPrefix:
        prefixes:
          - "/microfrontendlistingmanagement"
        forceSlash: false
  routers:
    microfrontendlistingmanagementsecure:
      rule: "PathPrefix(`/microfrontendlistingmanagement`)"
      middlewares:
        - "microfrontendlistingmanagement-stripprefix"
      service: redi-microfrontend-listingmanagement

