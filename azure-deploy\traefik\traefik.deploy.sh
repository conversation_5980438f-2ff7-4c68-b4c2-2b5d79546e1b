#Upload Traefik dynamic configuration file for service
#Replace DOCKER_REPO temp value with environment $DOCKER_REPO variable
sed -i 's/DOCKER_REPO/'"$DOCKER_REPO"'/g' azure-deploy/traefik/dynamic/microfrontendcommon.yaml;

restartTraefik=false

#Get MD5 value of uploading file
uploadFileMd5Val=$(md5sum azure-deploy/traefik/dynamic/microfrontendcommon.yaml | cut -d ' ' -f 1)

existFileMd5Val=null

echo 'Start dynamic directory exist check...'
directoryExists==$(az storage directory exists --name dynamic --account-name $AZURE_ACCOUNT_STORAGE --share-name $AZURE_STORAGE_VOLUME --query properties --query "exists" | tr -cd '[:alnum:]._-')

if [[ "$directoryExists" != "true" ]]; then
    #Create missing directory
    echo 'Start dynamic directory create...'
    az storage directory create --name dynamic --account-name $AZURE_ACCOUNT_STORAGE --share-name $AZURE_STORAGE_VOLUME
fi

fileExists=$(az storage file exists --path dynamic/microfrontendcommon.yaml --account-name $AZURE_ACCOUNT_STORAGE --share-name $AZURE_STORAGE_VOLUME --query properties --query "exists" | tr -cd '[:alnum:]._-')

if [[ "$fileExists" == "true" ]]; then
    #Get MD5 value of existing file in Azure storage
    echo 'Start service.yaml MD5 fetch...'
    existFileMd5Val=$(az storage file show --account-name $AZURE_ACCOUNT_STORAGE --share-name $AZURE_STORAGE_VOLUME --path dynamic/microfrontendcommon.yaml --query properties.contentSettings.contentMd5 -o tsv | tr -cd '[:alnum:]._-')
fi

if [[ "$uploadFileMd5Val" != "$existFileMd5Val" ]]; then
    echo 'Start upload service.yaml file...'
    restartTraefik=true
    az storage file upload --share-name $AZURE_STORAGE_VOLUME --path dynamic --source azure-deploy/traefik/dynamic/microfrontendcommon.yaml --account-name $AZURE_ACCOUNT_STORAGE --content-md5 $uploadFileMd5Val
fi

if [[ $restartTraefik = true ]]; then
    echo 'Restart Traefik'
    # Restart Traefik from latest revision
    az config set extension.use_dynamic_install=yes_without_prompt
    latestRevision=$(az containerapp revision list --resource-group $AZURE_RESOURCE_GROUP --name traefik --query '[].name | max(@)' | tr -cd '[:alnum:]._-')
    echo "Latest revision Name: $latestRevision"

    az containerapp revision restart --resource-group $AZURE_RESOURCE_GROUP --name traefik --revision $latestRevision
    echo 'Done restart'
fi