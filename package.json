{"name": "autotow-container", "version": "0.1.0", "private": true, "license": "MIT", "scripts": {"analyze": "source-map-explorer build/static/js/main.*", "start": "yarn install && node --max-old-space-size=4096 scripts/start.js", "staging": "set NODE_ENV=development&& set BABEL_ENV=development&& node scripts/build.js", "build": "set NODE_ENV=production&& set BABEL_ENV=production&& node scripts/build.js"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.0-placeholder-for-preset-env.2", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/preset-env": "^7.15.8", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.10.4", "@dr.pogodin/babel-plugin-react-css-modules": "^6.9.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "@types/node": "^16.18.12", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "babel-loader": "8.3.0", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "browserslist": "^4.18.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.4.5", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "postcss-scss": "^4.0.6", "resolve-url-loader": "^5.0.0", "sass-loader": "^12.3.0", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.2.5", "typescript": "^4.9.5", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@fortawesome/fontawesome-svg-core": "^6.3.0", "@fortawesome/pro-duotone-svg-icons": "^6.3.0", "@fortawesome/pro-light-svg-icons": "^6.3.0", "@fortawesome/pro-regular-svg-icons": "^6.3.0", "@fortawesome/pro-solid-svg-icons": "^6.3.0", "@fortawesome/pro-thin-svg-icons": "^6.3.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fortawesome/sharp-regular-svg-icons": "^6.3.0", "@fortawesome/sharp-solid-svg-icons": "^6.3.0", "@mui/material": "^5.11.13", "@mui/x-date-pickers": "^5.0.19", "@mui/icons-material": "^5.11.11", "@mui/lab": "^5.0.0-alpha.123", "date-fns": "^2.8.0", "dayjs": "^1.11.8", "memoize-one": "^6.0.0", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-css-modules": "^4.7.11", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-refresh": "^0.11.0", "react-router-dom": "^6.8.2", "redi-component-utils": "^1.0.21", "redi-http": "^2.2.10", "redi-security-components": "1.0.7", "redi-formik-material": "^2.0.10", "redi-ui-utils": "^2.0.0", "resolve": "^1.20.0", "sass": "^1.58.3", "workbox-webpack-plugin": "6.4.1", "yup": "^1.0.2", "zustand": "^4.3.5"}}