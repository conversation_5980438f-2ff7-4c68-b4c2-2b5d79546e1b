declare module "redi-types" {
    export interface BaseAddressCDto extends DtoBase{
        addressId: string;
        lines?: string;
        city?: string;
        stateOrProvince?: string;
        postalCode?: string;
        country?: string;
        countryCode?: string;
        addressName?: string;
        addressTypeCode?: string;
        fullAddress?: string;
        latitude?: number;
        longitude?: number;
        isPreferredAddress: boolean;
        parentEntityId: string;
        parentEntityType: string;
        sortOrder: number;
    }

    export interface GetAddressCDto extends BaseAddressCDto {

    }

    export interface GetListAddressDto {
        [x: string]: any;
        
    }

    export interface GetListAddressCDto extends BaseAddressCDto {

    }
}