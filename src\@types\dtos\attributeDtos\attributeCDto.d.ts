declare module "redi-types" {
    export interface AttributeCDto {
        label?: string;
        inputTypeCode?: string;
        description?: string;
        sortOrder: number;
        isEnabled: boolean;

    }
    export interface GetAttributeCDto extends AttributeCDto {
        attributeGroupCode?: string;
        dataTypeLabel?: string;
        dropdownValues?: GetDropDownValueCDto[];
        attributeValueTypeCode?: string;
        attributeCode?: string;
        icon?: string;
        readAccessClaim?: string;
        writeAccessClaim?: string;
        isManyAllowed: boolean;
    }
    export interface GetListAttributesDto extends GetAttributeCDto {
        attributeGroupIsEnabled: boolean;
        attributeInputTypeIsEnabled: boolean;
    }
    export interface AttributeByGroupListCDto {
        groupLabel?: string;
        groupDescription?: string;
        groupIsEnabled: boolean;
        sortOrder: number;
        attributes?: AttributeCDto[];
    }

    export interface FilterExcludeAttributesDto {
        [key: string]: { values: string[], label: string, operator?: string };

    }
}
