declare module "redi-types" {
    export interface BaseContactMethodCDto extends DtoBase {
        contactMethodId: string;
        contactMethodTypeCode: string;
        contactMethodSubTypeCode?: string;
        value: string;
        isPreferredContactMethod: boolean;
        isPrimaryForMethodType: boolean;
        note?: string;
        parentEntityId: string;
        parentEntityType: string;
        sortOrder: number;
    }

    export interface GetContactMethodCDto extends BaseContactMethodCDto {

    }

    export interface ContactMethodDto extends BaseContactMethodCDto {
        
    }

    export interface GetListContactMethodCDto extends BaseContactMethodCDto {
        contactMethodTypeIsEnabled?: boolean;
        contactMethodSubTypeIsEnabled?: boolean;
        contactMethodTypeLabel?: string;
        contactMethodSubTypeLabel?: string;
    }
}