declare module "redi-types" {
	export interface StandardListParameters {
    /* Columns available to sort are in the backend controllers -> canBeSortedBy StringDictionary */

    // Number of records to return
    limit: number;
    // Return records from offset count row.
    // Offset = 100 would return results from the 100th row
    offset: number;
    // List of Sort parameters in the format "column1,column2,-column3"
    // - prefix indicates descending order
    sortBy: string;
    // Return deleted
    isDeleted: boolean;
    // Force null values to the bottom when sorting
    forceNullBottom: boolean;
}
}
