import { InputTypeCodeEnum } from "../../../enum/InputTypeCodeEnum";

declare module "redi-types" {
    export interface ListingDisplayContainerAttributesCDto {
        displayContainerAttributeId?: string;
        attributeCode?: string;
        displayContainerCode?: string;
        label?: string;
        attributeGroupCode?: string;
        attributeValueTypeCode?: string;
        inputTypeCode?: InputTypeCodeEnum;
        isManyAllowed?: boolean;
        sortOrder: number;
        isEnabled: boolean;
        isReadOnly: boolean;
        icon?: string;
        readAccessClaim?: string;
        writeAccessClaim?: string;
        availableValues?: AvailableValuesWithStats[];
    }
    
}

