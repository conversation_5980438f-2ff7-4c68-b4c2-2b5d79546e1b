declare module "redi-types" {
    export interface ListTypeCardOptions {
        hideHeroImage?: boolean;
        hideTitle?: boolean;
        titleAlign?: 'left' | 'center' | 'right';
        hideFavouriteButton?: boolean;
        hideAttributes?: boolean;
        hideAttributeLabel?: boolean;
        attributesCols?: number;
        hideViewDetailsButton?: boolean;
        hideContactButton?: boolean;
        searchBar?: boolean;
        showSorting?: boolean;
        hideDescription?: boolean;
        onClick?: (dto: ListingWithDataAndMediaCDto) => void;
        // expandedAttributes?: boolean
    }

}
