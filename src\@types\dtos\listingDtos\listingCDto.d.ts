declare module "redi-types" {
    export interface ListingListCDto {
        /**
         * The Internal database ListingId.
         * This is not returned to public facing systems, and cannot be queried on via the Listing Api.
         * Use the ReferenceNo to fetch a listing
         */
        listingId: number;
        /**
         * Unique Id for Listing that is used by public facing front-ends to identify the listing.
         */
        referenceNo: string;
        /**
         * Short description of the listing, that should be shown in lists and page titles.
         */
        subject?: string;
        /**
         * Status of the listing: Draft, Pending, Active, Expired, Paused
         */
        statusCode?: string;
        /**
         * The Lat/Long of the listing
         */
        gpsLocation?: GpslocationCDto;
        /**
         * When the listing is Active from
         */
        fromDate: Date;
        /**
         * When the listing is active to. After this date it will be Expired (status) automatically.
         * No value indicates no expiry
         */
        toDate?: Date;
        /**
         * Default media to be used when displaying listing
         */
        profileListingMediaId?: number;
        /**
         * Default media url to be used when displaying listing
         */
        profileListingMediaUrl?: string;
        parentEntityIntId?: number;
        parentEntityId?: string;
        parentEntityType?: string;
        parentEntityId2?: string;
        parentEntityType2?: string;
        createdOn: string;
        createdByName?: string;
        modifiedOn?: string;
        modifiedByName?: string;
        //todo on backend, will return if its on the users favourite list
        favourite?: boolean;
        /**
         * Distance away from requested location. Calculated on listing call.
         */
        distanceAwayInKm?: number;
        icon?: string;
        sortOrder?: number;
    }

    export interface ListingCDto extends ListingListCDto {
        /**
         * Detail description for the Listing
         */
        description?: string;
    }

    export interface ListingWithDataAndMediaCDto extends ListingCDto {
        /**
         * List of media records associated with a listing
         */
        media?: ListingMediaCDto[];
        /**
         * List of attributes associated with a listing
         */
        attributes?: ListingDisplayContainerCDto[];
        /**
         * Simple name value pairs for all the returned attributes.
         */
        fields?: { [key: string]: any };
        /**
         * 
         */
        relationshipFields?: { [key: string]: string };
    }

    export interface ListingMediaCDto {
        /**
         * Id that uniquely identifies a media record
         */
        listingMediaId: number;
        /**
         * The ListingId the media record belongs to.
         */
        listingId: number;
        /**
         * Full URL that can be used to download / display media
         */
        mediaUrl?: string;
        /**
         * Title to be displayed for media item
         */
        title?: string;
        /**
         * The type of media. Image, Video, Pdf
         */
        mediaTypeCode?: string;
        /**
         * The media category code.
         * This can be used to control display of media
         */
        mediaCategoryCode?: string;
        /**
         * The media type display label
         */
        mediaTypeLabel?: string;
        /**
         * The media category display label
         */
        mediaCategoryLabel?: string;
    }

    export interface ListingDisplayContainerCDto {
        displayContainerCode: string;
        /**
         * The display container title that can be displayed to users
         */
        title?: string;
        /**
         * Show title to users when True
         */
        isShowTitle?: boolean;
        /**
         * Icon to display next to container Title
         */
        icon?: string;
        /**
         * Is this Display Container enabled for use. \
         * When False Display Container is not returned from Listing Api
         */
        enabled?: boolean;
        /**
         * Help text that can be displayed to user (tooltip or help button)
         */
        helpText?: string;
        /**
         * List of Attributes that make up the Display Container
         */
        attributes?: ListingDataCDto[];
    }

    export interface ListingDataCDto {
        listingAttributeId: number;
        attributeCode: string;
        valueString?: string;
        valueStringMax?: string;
        valueNumeric?: number;
        valueDateTime?: Date;
        valueGeography?: GpslocationCDto;
        displayContainerAttributeId: number;
        displayContainerCode?: string;
        isReadOnly: boolean;
        label?: string;
        sortOrder?: string;
        attributeGroupCode?: string;
        attributeValueTypeCode?: string;
        icon?: string;
        readAccessClaim?: string;
        writeAccessClaim?: string;
        isManyAllowed: boolean;
        inputTypeCode?: string;
    }

    export interface ListingSortOptionCDto {
        /**
         * The Sort Option Id.\
         */
        sortOptionId: number;
        /**
         * The sort label. This can be displayed to users.
         */
        label: string;
        /**
         * The Display Container Code, this option is available for
         */
        displayContainerCode: string;
    }
}
