declare module "redi-types" {
    export interface ListingQueryModelDto {
        filter?: string;
        exclude?: string;
        displayContainerCodesArray?: string[];
        parentEntityIntId?: number;
        parentEntityId?: string;
        parentEntityType?: string;
        statusCode?: string;
        subject?: string;
        description?: string;
        fromLocation?: GpslocationCDto;
        searchDistanceKm?: number;
        beforeDate?: string;
        afterDate?: string;
        visibility?: number;
        standardListParameters?: StandardListParameters;
    }

}
