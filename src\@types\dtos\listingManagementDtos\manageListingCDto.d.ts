declare module "redi-types" {
    /**
     * Represents the ManageListingListCDto object.
     */
    export interface ManageListingListCDto {
        /**
         * The ID of the listing.
         */
        listingId?: number;
        /**
         * Unique ID for the listing used by public facing front-ends to identify the listing.
         */
        referenceNo?: string;
        /**
         * The subject of the listing.
         */
        subject?: string;
        /**
         * The status code of the listing: Draft, Pending, Active, Expired, Paused.
         */
        statusCode?: string;
        /**
         * Identifies the type of listing. 0 is default.
         */
        listingTypeId?: number;
        /**
         * The parent listing this listing belongs to. Optional.
         * Allows listings to have child listings.The child listings could serve other purposes.
         */
        parentListingId?: number;
        /**
         * Optional sort order for adjusting order of small sets of listings
         */
        sortOrder?: number;
        /**
         * The GPS location of the listing.
         */
        gpsLocation?: GpslocationCDto;
        /**
         * The date when the listing is active from.
         */
        fromDate: Date;
        /**
         * The date when the listing is active to. After this date, it will be automatically expired.
         * No value indicates no expiry.
         */
        toDate?: Date;
        /**
         * The ID of the default media to be used when displaying the listing.
         */
        profileListingMediaId?: number;
        /**
         * The URL of the default media to be used when displaying the listing.
         */
        profileListingMediaUrl?: string;
        /**
         * The ID of the parent entity.
         */
        parentEntityIntId?: number;
        /**
         * The ID of the parent entity.
         */
        parentEntityId?: string;
        /**
         * The type of the parent entity.
         */
        parentEntityType?: string;
        /**
         * The second ID of the parent entity.
         */
        parentEntityId2?: string;
        /**
         * The second type of the parent entity.
         */
        parentEntityType2?: string;
        /**
         * The distance away from the requested location. Calculated on listing call.
         */
        distanceAwayInKm?: number;
        /**
         * Indicates who can view the listing:
         * 0 - public, everyone (default)
         * 1 - private, only the owner
         * 2 - members
         * 3 - DashboardAccess
         */
        visibilityId: number;
        /**
         * Listing Icon
         */
        icon?: string;
    }

    /**
     * Represents the ManageListingCDto object.
     */
    export interface ManageListingCDto extends ManageListingListCDto {
        /**
         * The description of the listing.
         */
        description?: string;
    }

    export interface ManageListingAttributeDto {
        valueString?:  string;
        valueNumeric?: number;
        valueDateTime?: Date;
        valueStringMax?: string;
        attributeCode: string;
        listingAttributeId?: number;
    }

    /**
     * Represents the ManageListingWithDataAndMediaCDto object.
     */
    export interface ManageListingWithDataAndMediaCDto extends ManageListingCDto {
        /**
         * The media associated with the listing.
         */
        media?: ManageListingMediaCDto[];
        /**
         * The attributes associated with the listing.
         */
        attributes?: ManageListingAttributeDto[];
        /**
         * Simple name value pairs for all the returned attributes.
         */
        fields?: { [key: string]: any };
    }

    /**
     * Represents the ManageListingWithDisplayGroupedDataAndMediaDto object.
     */
    export interface ManageListingWithDisplayGroupedDataAndMediaDto extends ManageListingCDto {
        /**
         * Parent listing dto
         */
        parentListing?: ListingDto | null;
        /**
         * The media associated with the listing.
         */
        media?: ManageListingMediaCDto[];
        /**
         * The attributes associated with the listing.
         */
        attributes?: ManageListingDisplayContainerCDto[];
        /**
         * Simple name value pairs for all the returned attributes.
         */
        fields?: { [key: string]: any };
        /** 
         * Relationship with Party records 
         */
        partyRelationships?: ListingPartyRelationshipListDto[];
    }

    /**
     * Represents the ManageListingMediaCDto object.
     */
    export interface ManageListingMediaCDto extends DtoBase {
        /**
         * The ID of the listing media.
         */
        listingMediaId: number;
        /**
         * The ID of the listing.
         */
        listingId: number;
        /**
         * The URL of the media.
         */
        mediaUrl: string;
        /**
         * The title of the media.
         */
        title?: string;
        /**
         * The media type code.
         */
        mediaTypeCode: string;
        /**
         * The media category code.
         */
        mediaCategoryCode?: string;
        /**
         * The media type label.
         */
        mediaTypeLabel?: string;
        /**
         * The media category label.
         */
        mediaCategoryLabel?: string;
        /**
         * The sort order of the media.
         */
        sortOrder: number;
    }

    /**
     * Represents the ManageListingDisplayContainerCDto object.
     */
    export interface ManageListingDisplayContainerCDto {
        /**
         * The code of the display container.
         */
        displayContainerCode?: string;
        /**
         * The title of the display container.
         */
        title?: string;
        /**
         * Indicates whether to show the title.
         */
        isShowTitle?: string;
        /**
         * The icon of the display container.
         */
        icon?: string;
        /**
         * Indicates whether the display container is enabled.
         */
        enabled?: boolean;
        /**
         * The help text of the display container.
         */
        helpText?: string;
        /**
         * The attributes associated with the listing.
         */
        attributes?: ManageListingAttributeDto[];
    }
}
