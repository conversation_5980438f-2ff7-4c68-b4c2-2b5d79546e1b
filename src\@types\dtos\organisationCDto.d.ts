declare module "redi-types" {
    export interface BaseOrganisationCDto {
        organisationId?: string;
        name: string;
        organisationReference?: string;
        description?: string;
        organisationTypeCode: string;
        organisationTypeDescription?: string;
        mergedIntoOrganisationId?: string;
        isMerged: string;
    }

    export interface GetOrganisationCDto extends BaseOrganisationCDto {
        partyId: string;
        partyType: string;
        statusCode: string;
        avatarImageId?: string;
        avatarImageUrl?: string;
        userId?: string;
        addresses?: GetListAddressCDto[];
        primaryEmail?: string;
        primaryPhone?: string;
        primaryAddress?: string;
        contactMethods?: GetListContactMethodCDto[];
    }

    export interface GetListOrganisationCDto extends BaseOrganisationCDto {
        partyId: string;
        partyType: string;
        statusCode: string;
        avatarImageId?: string;
        avatarImageUrl?: string;
        userId?: string;
        addresses?: GetListAddressCDto[];
        primaryEmail?: string;
        primaryPhone?: string;
        primaryAddress?: string;
        contactMethods?: GetListContactMethodCDto[];
    }
}