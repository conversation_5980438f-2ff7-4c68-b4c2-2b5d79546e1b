declare module "redi-types" {
    export interface PartyAttributeCDto extends DtoBase {
        partyAttributeId: number;
        partyId: string;
        attributeCode: string;
        value: string;
        label: string,
        inputTypeCode: string,
        options?:AttributeDropdownValue[];
        attributeValueTypeCode: string;
    }

    export interface GetPartyAttributeCDto extends PartyAttributeCDto {

    }
  
    export interface GetListPartyAttributesCDto extends PartyAttributeCDto {
    }
    
}