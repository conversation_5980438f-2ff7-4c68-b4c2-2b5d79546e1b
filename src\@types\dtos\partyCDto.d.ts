declare module "redi-types" {
    export interface BasePartyCDto extends DtoBase {
        partyId: string;
        organisationId?: string;
        personId?: string;
        statusCode: string;
        avatarImageId?: string;
        userId?: string;
        displayName?: string;
    }

    export interface InsurancePartyCDto extends BasePartyCDto {
        name: string;
        partyType: string;
        organisation?: OrganisationCDto;
        roleTypeCode?: string;
    }

    export interface GetPartyCDto extends BasePartyCDto {
        name: string;
        partyType: string;
        avatarImageUrl?: string;
        roleTypeCode?: string;
        deductions: Array<GetDeductionsCDto>;
        contactMethods: Array<GetListContactMethodCDto>;
        addresses: Array<GetListAddressCDto>;
    }

    export interface GetExtendedPartyCDto extends GetPartyCDto
    {
        fields?: { [key: string]: string };
        addresses: Array<GetListAddressCDto>;
        person?: BasePersonCDto;
        organisation?: OrganisationCDto;
        contactMethods: Array<GetListContactMethodCDto>;
        partyAttributes?:Array<GetListPartyAttributesCDto>;
        deductions: Array<GetDeductionsCDto>;
        primaryEmail?: string;
        primaryPhone?: string;
        primaryAddress?: string;
        note?: string;
        /** 
         * Relationship with Party records 
         */
        partyRelationships?: ListingPartyRelationshipListDto[];
        relationshipFields?: { [key: string]: string };
    }
    
    export interface GetListPartyCDto extends BasePartyCDto {
        fields?: { [key: string]: string };
        name: string;
        partyType: string;
        avatarImageUrl?: string;
        roleTypeCode: string;
        contactMethods: Array<GetListContactMethodCDto>;   
        addresses: Array<GetListAddressCDto>;
        partyAttributes?:Array<GetListPartyAttributesCDto>;
        deductions: Array<GetDeductionsCDto>;
        primaryEmail?: string;
        primaryPhone?: string;
        primaryAddress?: string;
        note?: string;
    }

    export interface GetExtendedListPartyCDto extends GetListPartyCDto {

    }

    export interface PartyCDto extends GetExtendedPartyCDto {

    }

    export interface BaseListCDto extends GetExtendedListPartyCDto {
        
    }
    
}