declare module "redi-types" {
    export interface PartyRelationshipCDto extends DtoBase {
        partyRelationshipId: string;
        partyRelationshipTypeCode: string;
        partyRoleToId: string;
        partyRoleFromId: string;
        fromDate?: Date;
        toDate?: Date;
    }

    export interface GetPartyRelationshipCDto extends PartyRelationshipCDto {
        partyRelationshipTypeLabel: string;
    }

    export interface GetListPartyRelationshipCDto extends PartyRelationshipCDto {
        partyRelationshipTypeLabel: string;
        toPartyId: string;
        fromPartyId: string;
        toPartyName: string;
        fromPartyName: string;
        toRoleTypeCode: string;
        fromRoleTypeCode: string;
        toRoleTypeLabel: string;
        fromRoleTypeLabel: string;
        relatedParty?: BaseListCDto;
    }
}