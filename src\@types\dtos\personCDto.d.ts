import { string } from "yargs";

declare module "redi-types" {
    export interface BasePersonCDto {
        personId?: string;
        firstName?: string;
        familyName?: string;
        fullName?: string;
        dateOfBirth?: string;
        yearOfBirth?: number;
        age?: number;
        prefix?: string;
        suffix?: string;
        mergedIntoPersonId?: string;
        isMerged?: boolean;
    }

    export interface GetPersonCDto extends BasePersonCDto{
        partyId?: string;
    }

    export interface GetListPersonCDto extends BasePersonCDto{
        partyId: string;
        statusCode: string;
        partyType: string;
        name: string;
        avatarImageUrl: string;
        avatarImageUrl: string;
        userId: string;
        primaryEmail?: string;
        primaryPhone?: string;
    }

    export interface PartyPersonListCDto extends GetListPartyCDto {
        person: PersonCDto;
    }

    export interface PartyOrganisationListCDto extends GetListPartyCDto {
        organisation: OrganisationCDto;
    }

    export interface FinalOrgList {
        listOfOrgs: List<PartyOrganisationListCDto>;
        totalNumOfRows: number;
    }

    export interface FinalPersList {
        listOfPers: List<PartyPersonListCDto>;
        totalNumOfRows: number;
    }
}