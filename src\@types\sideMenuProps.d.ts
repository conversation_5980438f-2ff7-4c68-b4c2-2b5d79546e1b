import {FontAwesomeIconProps} from '@fortawesome/react-fontawesome';

declare module "redi-types" {
	export interface SideMenuProps {
    routes: MenuItem[];
    projectName?: string;
    requireLogin?: boolean;
    hide?: boolean;
    companyLogo?: string;
    showRedi?: boolean;
    children?: React.ReactNode;
  }

  export interface MenuItem {
    path: string;
    name: string;
    element: JSX.Element;
    icon?: FontAwesomeIconProps["icon"];
    excludeFromMenu?: boolean;
    requiredClaims?: string[];
  }
}