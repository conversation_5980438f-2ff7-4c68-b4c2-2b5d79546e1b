import React from 'react';
import useAuthenticate from './useAuthenticate';

function Authenticate(props: Props) {
    const { children, options } = props;
    const { isAuthenticated } = useAuthenticate(options.requiredClaims ?? []);

    if (!isAuthenticated && options.navToDefaultRoute) {
        //TODO: useRouter nav to login
        
    }

    return (
        <>
            {isAuthenticated ?
            children :
            <div>
                403 Unauthorized
            </div>}
        </>
    );

};

interface Props {
    children: React.ReactNode;
    options: Options;
};

export interface Options {
    requiredClaims?: string[];
    navToDefaultRoute?: boolean;
    /** Default route: '/Login' */
    defaultRoute?: string;
};

export default Authenticate;