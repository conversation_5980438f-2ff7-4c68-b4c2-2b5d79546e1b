import { shallow } from 'zustand/shallow';
import useSecurityStore from '../Security/useSecurityStore';

function useAuthenticate(requiredClaims: string[] = []) {

    const { isLoggedIn, getClaims } = useSecurityStore(
        (state) => ({ isLoggedIn: state.isLoggedIn, claims: state.claims, defaultClaims: state.defaultClaims, getClaims: state.getClaims }),
        shallow
    );

    const claims = getClaims();
    const hasClaims = requiredClaims.length === 0 || requiredClaims.every(x => !claims.some((claim: string) => claim === x));
    const isAuthenticated = isLoggedIn && hasClaims;

    return { isAuthenticated };
}

export default useAuthenticate;