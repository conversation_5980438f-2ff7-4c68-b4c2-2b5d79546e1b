import * as React from "react";
import "./styles.scss";
import { IconButton, Button, Tooltip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faRotate, faFolderOpen } from "@fortawesome/pro-duotone-svg-icons";

import DraggableDialog from "../../components/DraggableDialog/DraggableDialog";
import { useState } from "react";
import Dropzone, { Accept } from "react-dropzone";
import fileManagerService from "../../services/fileManager";
import { FileCDto } from "redi-types";
import { HttpResult } from "redi-http";

interface Props<T, K extends keyof T> {
  parentEntityId: string;
  parentEntityType: string; // I don't know what this should actually be, so using Submission
  //parentEntityDescription: string;
  //imageId: string
  imageUrl?: string;
  onSave?: (newImage: HttpResult<FileCDto> | undefined) => void;
  cancelOnClick?: () => void;
  buttonVariant?: "outlined" | "contained" | "text";
  isOpen: boolean;
}

AvatarEditor.defaultProps = {
  //for testing
  //imageUrl: "https://th.bing.com/th/id/R.********************************?rik=pOsTg5KBoLuNvw&riu=http%3a%2f%2fwww.snut.fr%2fwp-content%2fuploads%2f2015%2f08%2fimage-de-paysage.jpg&ehk=1O5SWKkGpZ8yU%2b%2fAnLXG1v8k6BKxgyiXgHbOWBW1ir0%3d&risl=1&pid=ImgRaw&r=0",
  buttonVariant: "contained", // The filled-in style
  partyEntityType: "Submission", // I don't know what this should actually be, so using Submission
};

function AvatarEditor<T, K extends keyof T>(props: Props<T, K>) {
  let imgEditor: any | null;
  const [isSending, setIsSending] = useState<boolean>(false);
  const [imageUrl, setImageUrl] = useState<string | undefined>(props.imageUrl);

  //When imageEditor is created open initial image if provided instead of displaying the drop zone
  function imageEditorCreated() {
    //console.log("imageEditorCreated")
    if (imgEditor && props.imageUrl) {
      imgEditor.open(props.imageUrl);
    } else if (imgEditor && imageUrl) {
      imgEditor.open(imageUrl);
    }
  }

  //when initial image opens or a new image is opened
  function fileOpened() {
    //console.log("File Opened")
    if (imgEditor) {
      //display crop circle
      imgEditor.select("circle");
    }
  }

  //rotates 90 degrees clockwise
  function rotate() {
    if (imgEditor) {
      imgEditor.rotate(90);
      //ensure circle select not square
      imgEditor.select("circle");
    }
  }

  /**
   * When a user uploads a file with the "Open New Image" button, this handler sets the image being edited to the new image
   * @param event Captures the file upload event
   */
  async function handleFileChange(event: React.ChangeEvent<HTMLInputElement>) {
    const files = event.target.files;
    if (files && files.length > 0) {
      const newImageUrl = URL.createObjectURL(files[0]);
      if (imgEditor) {
        imgEditor.open(newImageUrl);
      } else {
        setImageUrl(newImageUrl); // If the image editor has not been created (no starting image supplied), then we need to reload this component to create it.
      }
    }
  }
  /**
   * Opens a file selector for the user to select a new image to insert
   */
  function openNewImage() {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";
    fileInput.addEventListener("change", handleFileChange as unknown as EventListener);
    fileInput.click();
  }

  function cancel() {
    setImageUrl(undefined); // Reset image if drag and drop is used
    props.cancelOnClick && props.cancelOnClick();
  }
  /**
   * Captures the file dropped event from the drop zone and sets the image being edited to this file.
   * @param droppedFiles Files dropped into the drop zone
   */
  const onDrop = (droppedFiles: File[]) => {
    if (droppedFiles.length === 1) {
      let file = droppedFiles[0];

      const newImageUrl = URL.createObjectURL(file);
      setImageUrl(newImageUrl);
    }
  };
  /**
   * Responsible for rendering the modal
   * @returns The Modal to render
   */
  function renderModal() {
    // https://github.com/react-dropzone/react-dropzone/tree/master/examples/accept
    var acceptedFileFormats: Accept = { "image/*": [".png", ".jpeg", ".svg"] };

    return (
      <>
        <DraggableDialog title="Edit Profile Picture" isOpen={props.isOpen} onCancel={cancel} maxWidth="sm">
          <div id="avatar-editor-body" styleName="card-body">
            {props.imageUrl || imageUrl ? (
              <div />
              // <ImageEditorComponent
              //   ref={(img) => {
              //     imgEditor = img;
              //   }}
              //   created={imageEditorCreated}
              //   toolbar={[]} // Prevent rendering of the default toolbar
              //   fileOpened={fileOpened} // Catch new file being loaded into the editor to make it a circle crop
              // />
            ) : (
              // Dropzone is only visible if the image editor has no image to load
              // Can only have one image to edit at a time, so only allow one.
              <Dropzone onDrop={onDrop} maxFiles={1} accept={acceptedFileFormats}>
                {({ getRootProps, getInputProps, isDragActive }) => (
                  <section>
                    <div {...getRootProps()} styleName="drop-area">
                      <input {...getInputProps()} />
                      {/* Changes the message in the drop zone based on if a file is being hovered over the drop zone */}
                      {isDragActive ? <p>Drop that file here ...</p> : <p>Drag and drop a file here, or click to select file (png, jpeg, svg)</p>}
                    </div>
                  </section>
                )}
              </Dropzone>
            )}
          </div>
          {/* Bottom control menu */}
          <div styleName="card-footer">
            <Tooltip title="Open New Image" placement="bottom">
              <IconButton id="openImage" onClick={() => openNewImage()} disabled={isSending}>
                <FontAwesomeIcon styleName="row-menu-icon" icon={faFolderOpen} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Rotate" placement="bottom">
              <IconButton id="rotate" onClick={() => rotate()} disabled={isSending}>
                <FontAwesomeIcon styleName="row-menu-icon" icon={faRotate} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Accept" placement="bottom">
              <Button variant={props.buttonVariant} onClick={() => acceptImage()} disabled={isSending}>
                Accept
              </Button>
            </Tooltip>
          </div>
        </DraggableDialog>
      </>
    );
  }

  /**
   * Takes the ImageData from the Image Editor's getImageData() method and returns a file that can be sent
   * @param imgData Image Data from the Image Editor component
   * @returns A File created with the ImageData
   */
  async function convertToImage(imgData: ImageData, fileName: string, mimeType: string) {
    // First create an image with canvas
    var canvas = document.createElement("canvas");
    canvas.width = imgData.width;
    canvas.height = imgData.height;
    var ctx = canvas.getContext("2d");
    if (ctx != null) {
      ctx.putImageData(imgData, 0, 0);
    }
    var img = document.createElement("img");
    img.src = canvas.toDataURL(mimeType); // creates a base64 encoded image
    //document.body.appendChild(img); // This data can be added anywhere in the page

    // Create image File
    let test: File = await fetch(img.src)
      .then(function (res) {
        return res.arrayBuffer(); // Get a buffered array from the image source (base64 encoded image) created just prior
      })
      .then(function (buf) {
        return new File([buf], fileName, { type: mimeType }); // Creates the actuall File
      });

    return test;
  }
  /**
   * Resposible for actually saving the image, calls the backend and returns the result to the function passed in onSave.
   * The function will do nothing if no image is loaded.
   * If the partyId or partyEntityType is not set, it will return undefined.
   */
  async function acceptImage() {
    setIsSending(true);
    if (imgEditor) {
      imgEditor.crop();
      if (props.parentEntityId && props.parentEntityType) {
        let croppedImage: ImageData = imgEditor.getImageData();

        let fileName = "avatar.png"; // File Name to save the image as
        let fileType = "image/png"; // Mime type for the file

        let image: File = await convertToImage(croppedImage, fileName, fileType);

        const result = await fileManagerService.UploadFile(image, fileName, "Image", props.parentEntityId, props.parentEntityType, true);
        props.onSave && props.onSave(result);
      } else {
        props.onSave && props.onSave(undefined);
      }
    }
    setIsSending(false);
  }

  return renderModal();
}

export default AvatarEditor;
