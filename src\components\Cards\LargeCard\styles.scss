@import "../../../config/theme/vars.scss";

.listing-card {
    margin: 10px;
    // height: 600px;
    // width: 800px;
    max-width: 650px;

    >button {
        width: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
    }
}

.fake-image {
    background-color: aqua;
}

.text-box {
    padding: 0 30px;
}

.camera-box {
    display: flex;
    justify-content: space-evenly;
    // padding-left: 20px;
    width: 62px;
    height: 46px;
    align-items: center;
    cursor: pointer;
}

.header-container {
    margin: 0px 30px 0;
    padding-top: 30px;
}

.listing-subject {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 0;
    line-height: 1.3;
    color: #2a2a2a;
    margin-top: 10px;
}

.key-details {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-left: 0px;
    overflow: hidden;
}

.key-details-value {
    width: 50%;
    padding-bottom: 5px;
    list-style: none;
    white-space: nowrap;
    overflow: hidden;
    padding-left: 5px;
}

.key-details-value::before {
    content: "";
    width: 3px;
    height: 3px;
    border-radius: 100%;
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
    position: relative;
    background: #4a4a4a;
}

.favourites {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    cursor: pointer;
    align-items: center;
}

.camera-favourites {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
}

.button-container {
    display: flex;
    justify-content: space-between;
    padding: 30px;

}

.spacer {
    border: 0;
    height: 1px;
    margin: 0 30px;
    background-color: rgba(211, 211, 211, 0.712);
}