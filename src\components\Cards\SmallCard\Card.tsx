import { ListTypeCardOptions, ListingWithDataAndMediaCDto } from 'redi-types';
import './styles.scss';
import { Button, Card as MuiCard, } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import ImageCarousel from '../../ImageCarousel/ImageCarousel';
import KeyDetailsValue from '../../KeyDetailsValue/KeyDetailsValue';
import LightBoxWrapper from '../../LightBox/LightBox';




interface Props {
    data: ListingWithDataAndMediaCDto
    cardOptions?: ListTypeCardOptions
}

function SmallCard(props: Props) {
    const { cardOptions } = props;
    const navigate = useNavigate();
    const [openLightbox, setOpenLightbox] = useState(false);

    function handleFavouritesAdd() {
        console.log('Favourites Added');
    }

    const [imageVisibility, setImageVisibility] = useState<boolean>(false);
    function onLoad() {
        setImageVisibility(true);
    }

    return (
        <>
            <MuiCard styleName="listing-card">
                <div styleName='header-container'>
                    {cardOptions?.hideHeroImage !== true && (
                        <>
                            {props.data.media?.length ? <ImageCarousel onLoad={onLoad} height="100px" mediaArray={props.data.media} /> : <div styleName='fake-image'></div>}

                        </>
                    )}

                    <div styleName="camera-favourites">
                        {cardOptions?.hideFavouriteButton === false && (
                            <div styleName="favourites">
                                <FontAwesomeIcon
                                    size="lg"
                                    icon={['fad', 'heart']}
                                    onClick={handleFavouritesAdd}
                                />
                                <span>Save</span>
                            </div>
                        )}
                    </div>
                </div>
                {cardOptions?.hideTitle !== true && (
                    <div styleName='text-box' >
                        <h3 styleName="listing-subject">{props.data.subject}</h3>
                    </div>
                )}
                {cardOptions?.hideAttributes === false && (
                    <div styleName="text-box">
                        <ul styleName="key-details">
                            {props.data.attributes?.map((attribute, index) => {
                                if (
                                    !attribute.attributes?.length ||
                                    !attribute.attributes[0] ||
                                    !attribute.attributes[0].attributeValueTypeCode
                                ) {
                                    return null;
                                }
                                return (
                                    <KeyDetailsValue
                                        key={index}
                                        attributeValueTypeCode={
                                            attribute.attributes[0].attributeValueTypeCode
                                        }
                                        value={attribute.attributes[0]}
                                    />
                                );
                            })}
                        </ul>
                    </div>
                )}

                {cardOptions?.hideContactButton === true && (
                    <div styleName='button-container'>
                        <Button variant="outlined" color="primary" onClick={() => navigate(`/listing/${props.data.referenceNo}`)}>
                            View Details
                        </Button>
                        <Button variant="contained" color="primary">
                            Contact
                        </Button>
                    </div>
                )}


            </MuiCard>
            <LightBoxWrapper open={openLightbox} setOpen={setOpenLightbox} mediaArray={props.data.media} />
        </>
    );
}

export default SmallCard;