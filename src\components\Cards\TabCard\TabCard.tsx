import { ListTypeCardOptions, ListingWithDataAndMediaCDto } from "redi-types";
import "./styles.scss";
import { <PERSON><PERSON>, <PERSON>ton, Card as MuiCard } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface Props {
  data: ListingWithDataAndMediaCDto;
  cardOptions?: ListTypeCardOptions;
}

function TabCard(props: Props) {
  const { data } = props;

  //todo restructure this to one loop

  const listingDetailsDisplayContainer = data.attributes?.find(
    (attr) => attr.displayContainerCode === "ListingDetail"
  );
  const databaseAttribute = listingDetailsDisplayContainer?.attributes?.find(
    (attr) => attr.attributeCode === "Database"
  );
  const tabCornerIndicatorAttribute =
    listingDetailsDisplayContainer?.attributes?.find(
      (attr) => attr.attributeCode === "TabCornerIndicator"
    );
  const cardPillAttributes = listingDetailsDisplayContainer?.attributes?.filter(
    (attr) => attr.attributeCode === "CardPill"
  );

  // //Test data
  // const databaseAttribute = { valueString: 'Database' }
  // const cardPillAttributes = [{ valueString: 'New' }]
  // const tabCornerIndicatorAttribute = { valueString: 'Demo' }

  //Creates a random color based on the string passed in
  //todo replace with chosen colors at some point
  function stringToColor(string: string) {
    let hash = 1;
    let i;

    for (i = 0; i < string.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = "#";

    for (i = 0; i < 3; i += 1) {
      const value = (hash >> (i * 8)) & 0xff;
      color += `00${value.toString(16)}`.slice(-2);
    }
    /* eslint-enable no-bitwise */

    return color;
  }
  function stringAvatar(name: string) {
    return {
      sx: {
        bgcolor: stringToColor(name),
      },
      children: `${name.split(" ")[0][0]}${name.split(" ")[1][0]}`,
    };
  }

  return (
    <MuiCard styleName="listing-card">
      <div styleName="image-title-container">
        {props.data.profileListingMediaUrl ? (
          <Avatar
            styleName="image-circle"
            alt={props.data.subject}
            src={props.data.profileListingMediaUrl}
          />
        ) : (
          <Avatar
            styleName="image-circle"
            alt={props.data.subject}
            {...stringAvatar(props.data.subject!)}
          />
        )}
        {props.data.subject && (
          <div styleName="listing-subject">{props.data.subject}</div>
        )}
      </div>
      {databaseAttribute && (
        <div>
          <FontAwesomeIcon
            styleName="additional-icon"
            size="sm"
            icon={["fad", "database"]}
          />
        </div>
      )}
      {tabCornerIndicatorAttribute && (
        <div styleName="ribbon">{tabCornerIndicatorAttribute.valueString}</div>
      )}
      <div styleName="pills">
        {cardPillAttributes &&
          cardPillAttributes.map((attr, index) => (
            <div
              key={index}
              style={{ color: stringToColor(attr.valueString || "") }}
            >
              {attr.valueString}
            </div>
          ))}
      </div>
    </MuiCard>
  );
}
export default TabCard;
