@import "../../../config/theme/vars.scss";

.listing-card {
    display: flex;
    align-items: center;
    margin: 10px;
    width: 300px;
    height: 60px;
    position: relative;
}

.image-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 0px;
    margin-left: 20px;
}

.listing-subject {
    font-size: 14px;
    line-height: 24px;
    margin-left: 8px;
    margin-right: 8px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

}

.image-title-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

.additional-icon {
    margin-right: 25px;
}

.ribbon {
    margin: 0;
    display: flex;
    padding: 0;
    background: $secondaryColor;
    color: white;
    position: absolute;
    top: 0;
    width: 79px;
    right: 0;
    height: 36px;
    text-align: center;
    -webkit-transform: translateX(30%) translateY(0%) rotate(45deg);
    transform: translateX(74%) translateY(-93%) rotate(45deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    align-items: flex-end;
    font-size: 10px;
    justify-content: center;
}

.pills {
    display: flex;
    position: absolute;
    right: 7px;
    bottom: 1px;

    >div {
        font-size: 12px;
        width: 41px;
        border: 1px solid;
        text-align: center;
        border-radius: 7px;
        margin-right: 5px;
    }
}