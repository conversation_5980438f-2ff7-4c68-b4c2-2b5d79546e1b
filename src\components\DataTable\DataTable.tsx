import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, ButtonGroup, CircularProgress, InputAdornment, MenuItem, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TableSortLabel, TextField, Tooltip } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { HttpResult } from "redi-http";
import { ListResponseDto, StandardListParameters } from "redi-types";
import './styles.scss';

interface Props<T, K extends keyof T> {
  primaryKeyProperty: K;
  title: string;
  tableId: string;                // Unique table prefix for localstorage ie: 'base-userManagement-'
  pageSize: number;               // One page must be bigger than table
  searchDebounceDelayMs: number;
  initialSortColumn: string;      // Prepend with '-' for descending
  refreshTableTrigger?: number;   // Triggers a table refresh every time this variable is incremented
  tableHeight?: number;           // Height of the infinite scroll table (must be a fixed amount)
  addButtonLabel?: string;
  addButtonOnClick?: () => void;
  tableHeaders: TableHeader[];
  renderTableRow: (data: T) => any;
  onRowClick?: (row: T) => void;
  callService: (standardListParameters?: StandardListParameters,
                search?: string,
                filter?: string,
                ...params: any[]) => Promise<HttpResult<ListResponseDto<T>>>;
  initialFilter?: string;        // Will be overridden by a session storage value if it exists
  filterOptions?: string[];
}

DataTable.defaultProps = {
  pageSize: 12,
  searchDebounceDelayMs: 750,
  showButtonFilters: true,
  tableHeight: 600
};

function DataTable<T, K extends keyof T>(props: Props<T, K>) {
  const scrollId = props.tableId + 'infinite-scroll';

  // Table
  const [tableData, setTableData] = useState<T[]>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [tableInitiallyLoaded, setTableInitiallyLoaded] = useState(false);
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState(props?.initialFilter ?? 'All');
  const [lastClickedId, setLastClickedId] = useState<T[K]>();
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  // Paging / Infinite Scroll
  const [listParameters, setListParameters] = useState<StandardListParameters>({
    limit: props.pageSize,
    offset: 0,
    sortBy: props.initialSortColumn,
    isDeleted: false
  });
  const [hasMore, setHasMore] = useState(true);
  const [fetching, setFetching] = useState(false);
  let scrollPosition = useRef(0);
  const handleScroll = (event: MouseEvent) => {
      const scrollTop = (event.target as HTMLInputElement).scrollTop; 
      scrollPosition.current = scrollTop;
      localStorage.setItem(props.tableId + 'scrollOffset', scrollTop.toString());
  };

  useEffect(() => {
    (async () => {
      const { _search, _listParameters, _filter} = loadLocalStorageData();
      loadLocalStorageScrollOffset();

      if (scrollPosition.current === 0) {
        await refreshTableData(_search, _listParameters, _filter);
      } else {
        await getInitialTableData(_search, _listParameters, _filter);
      }
      setTableInitiallyLoaded(true);
      scrollToInitialPosition();
    })();

    return () => {
      // Clear the debounce timeout on unmount
      debounceTimeout.current && clearTimeout(debounceTimeout.current);
    };
  }, []);

  // Refresh table data from parent (every time 'props.refreshTableTrigger' changes)
  useEffect(() => {
    (async () => {
      if (props.refreshTableTrigger) {
        await getInitialTableData();
      }
    })();
  }, [props.refreshTableTrigger]);

  function loadLocalStorageData() {
    let _search = search;
    let _listParameters = listParameters;
    let _filter = filter;
    let _lastClickedId = lastClickedId;
    
    const searchLS = localStorage.getItem(props.tableId + 'search');
    if (searchLS) {
      _search = searchLS;
      setSearch(_search);
    }

    const listParametersLS = localStorage.getItem(props.tableId + 'listParameters');
    if (listParametersLS) {
      const params: StandardListParameters = JSON.parse(listParametersLS);
      _listParameters = {
        ...params,
        limit: props.pageSize // Stop localstorage overriding the variable default
      };
      setListParameters(_listParameters);
    }

    if (props.filterOptions) {
      const filterLS = localStorage.getItem(props.tableId + 'filter');
      if (filterLS) {
          _filter = filterLS;
          setFilter(_filter);
      }
    } else {
      // Clear the local storage value
      localStorage.removeItem(props.tableId + 'filter');
    }

    // Session storage instead of local storage
    const lastClickedIdLS = sessionStorage.getItem(props.tableId + 'lastClickedId');
    if (lastClickedIdLS) {
        _lastClickedId = lastClickedIdLS as T[K];
        setLastClickedId(_lastClickedId);
    }

    return { _search, _listParameters, _filter, _lastClickedId };
  }

  function loadLocalStorageScrollOffset() {
    const scrollOffset = localStorage.getItem(props.tableId + 'scrollOffset');
    if (scrollOffset) {
      scrollPosition.current = Number(scrollOffset);
    }
  }

  function scrollToInitialPosition() {
    if (scrollPosition.current) {
      // Requires a timeout of 0 to trigger
      setTimeout(async () => {
        const div = document.getElementsByClassName(scrollId)[0];
        if (div) {
          div.scrollTop = scrollPosition.current;
        }
      }, 0);      
    }
  }

  function rowClicked(row: T) {
    const primaryKey = (row)[props.primaryKeyProperty];
    if (primaryKey) {
      setLastClickedId(primaryKey);
      sessionStorage.setItem(props.tableId + 'lastClickedId', primaryKey as string);
    }
    props.onRowClick && props.onRowClick(row);
  }

  async function resetSearch() {
    setSearch('');
    await refreshTableSearch('');
  }

  async function searchChanged(event: React.ChangeEvent<HTMLInputElement>) {
    const value = event.target.value;
    setSearch(value);

    // Clear existing debounce timeout
    debounceTimeout.current && clearTimeout(debounceTimeout.current);

    // Set a new debounce timeout
    debounceTimeout.current = setTimeout(async () => {
      await refreshTableSearch(value);
    }, props.searchDebounceDelayMs);
  }

  async function filterChanged(filter: string) {
    setFilter(filter);
    setSearchLoading(true);

    // Clear existing debounce timeout if exists
    debounceTimeout.current && clearTimeout(debounceTimeout.current);
    await refreshTableData(undefined, undefined, filter);
    setSearchLoading(false);

  }

  async function refreshTableSearch(value: string) {
    setSearchLoading(true);
    await refreshTableData(value);
    setSearchLoading(false);
  }

  async function handleSortRequest(headerId: string) {

    // Existing sort values
    let _listParameters = listParameters;
    let sortHeader = _listParameters.sortBy;
    let isAscending = true;

    if (sortHeader.startsWith('-')) {
      sortHeader = sortHeader.substring(1);
      isAscending = false;
    }

    // New sort values
    let newIsAscending = true;
    let newSortHeader = headerId;

    if (sortHeader === headerId) {
      // Same header clicked
      if (isAscending) {
        newIsAscending = false; // Toggle orderBy
      } else { // Desc
        newSortHeader = ''; // Clear sort header
      }
    } else {
      // Set new header
      newSortHeader = headerId;
    }

    const orderChar = newIsAscending ? '' : '-';
    _listParameters.sortBy = orderChar + newSortHeader;

    setListParameters(_listParameters);

    await refreshTableData(search, _listParameters);
  };

  async function getInitialTableData(inSearch = search, inListParameters = listParameters, inFilter = filter) {
    // Initial data pregets all data they last scrolled to
    setTableLoading(true);

    // Need to get all the data we previously had (make limit = offset)
    // If offset is 0 default to page size
    const limit = inListParameters.offset < props.pageSize ? props.pageSize : inListParameters.offset;
    const tempListParameters = {
      limit: limit,
      offset: 0,
      sortBy: inListParameters.sortBy,
      isDeleted: inListParameters.isDeleted
    };

    const response = await props.callService(tempListParameters, inSearch, inFilter);
    if (!response.error) {
      const data = response.data?.list ?? [];
      setTableData(data);
      setHasMore(data.length === listParameters.limit);
    } else {
      setHasMore(false);
    }

    // As loading in data from existing listParameters, they are already up to date.
    setTableLoading(false);
  }

  async function refreshTableData(inSearch = search, inListParameters = listParameters, inFilter = filter) {
    // Refresh resets everything, starts fresh
    setTableLoading(true);

    // Reset offset
    inListParameters.offset = 0;

    const response = await props.callService(inListParameters, inSearch, inFilter);
    if (!response.error) {
      const data = response.data?.list ?? [];
      setTableData(data);

      // Add new data's length to current offset count
      setListParameters(prev => ({
        ...prev,
        offset: data.length // offset was 0
      }));

      setHasMore(data.length === listParameters.limit);

    } else {
      setTableData([]);
      setHasMore(false);
    }
    
    // Scroll to top
    const div = document.getElementsByClassName(scrollId)[0];
    if (div) {
      div.scrollTop = 0;
    }    

    // Save to local storage
    localStorage.setItem(props.tableId + 'search', inSearch);
    localStorage.setItem(props.tableId + 'listParameters', JSON.stringify(inListParameters));
    if (props.filterOptions) {
      localStorage.setItem(props.tableId + 'filter', inFilter);
    }

    setTableLoading(false);
  }

  async function fetchMoreData() {
    if (fetching || tableLoading) {
      return;
    }

    setFetching(true);

    try {
      const _listParameters = listParameters;
      const response = await props.callService(_listParameters, search, filter);
      if (!response.error) {
        const newData = response.data?.list ?? [];     

        if (newData && newData.length > 0) {
          setTableData([...tableData, ...newData]);

          // Add new data's length to current offset count
          _listParameters.offset = _listParameters.offset + newData.length;
          setListParameters(_listParameters);
          setHasMore(newData.length === _listParameters.limit);
          // Save to local storage
          localStorage.setItem(props.tableId + 'listParameters', JSON.stringify(_listParameters));
        } else {
          setHasMore(false);
        }
      }
    } finally {
      localStorage.setItem(props.tableId + 'scrollOffset', scrollPosition.current.toString() ?? '0');
      setFetching(false);
    }
  }

  /* Render */

  const LoadingOverlay = () => {
    return (
      <div styleName="loading-overlay">
        <CircularProgress color="primary" size={50} />
      </div>
    );
  }

  const CustomTable = () => {
    return (
      <Table aria-label="table" stickyHeader>
        <TableHead>
          <TableRow>
            {
              props.tableHeaders.map(header => {
                const sortBy = listParameters.sortBy;
                const isActive = sortBy === header.id || sortBy === `-${header.id}`;
                const sortDir =  sortBy && sortBy.startsWith('-') ? Order.Desc : Order.Asc;
                if (header.isSortable) {
                  return (
                    <TableCell key={header.id} onClick={() => handleSortRequest(header.id)} align={header.align}>
                      <TableSortLabel active={isActive} direction={isActive ? sortDir : Order.Asc}>
                        { header.label }
                      </TableSortLabel>
                    </TableCell>
                  );
                } else {
                  return (
                    <TableCell key={header.id} align={header.align}>
                      { header.label }
                    </TableCell>
                  )
                }
              })
            }
          </TableRow>
        </TableHead>
        <TableBody>
        {
          tableData.map(row => {
            const rowId = row[props.primaryKeyProperty] as string;
            const activeClass = lastClickedId === rowId ? 'active' : '';
            const clickableClass = props.onRowClick ? 'clickable' : '';
            return (
              <TableRow key={rowId}
                styleName={`table-row ${activeClass} ${clickableClass}`} 
                onClick={() => props.onRowClick && rowClicked(row)}
              >
                {props.renderTableRow(row)}
              </TableRow>
            );
          })
        }
        </TableBody>
    </Table>
    );
  }

  return (
    <div>
      <div styleName="card-header">
        <div styleName="title-row">
          <div styleName="table-title">{props.title}</div>
          {
            props.addButtonLabel &&
            <Tooltip title="Add a new item" placement="bottom">
              <div styleName="action-button" onClick={() => props.addButtonOnClick && props.addButtonOnClick()}>
                <FontAwesomeIcon icon="circle-plus"/>
                <div>{props.addButtonLabel}</div>
              </div>
            </Tooltip>
          }
        </div>
        <div styleName="search-grid">
          <div styleName="search-bar-row">
            <TextField
              styleName="search-input"
              size="small"
              variant="outlined"
              placeholder="Search..."
              value={search}
              onChange={searchChanged}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    {
                      search.length > 0 &&
                      <FontAwesomeIcon styleName="close-icon" icon="close" onClick={resetSearch} />
                    }
                  </InputAdornment>
                ),
              }}
            />
            {
              searchLoading
              ?
                <CircularProgress color="primary" size={25} thickness={4} />
              :
                <div style={{width: 25, height: 25}}></div>
            }
          </div>
          {
            props.filterOptions
            ?
            <ButtonGroup variant="contained" color="primary" aria-label="search filters">
              {
                props.filterOptions.map((_filter) => (
                  <Button
                    key={_filter}
                    color={filter === _filter ? "secondary" : "primary"}
                    onClick={() => filterChanged(_filter)}
                  >
                    {_filter}
                  </Button>
                ))
              }
            </ButtonGroup>
            :
            <div></div>
          }
          <div styleName="refresh-row">
            <Tooltip title="Refresh" placement="bottom">
              <FontAwesomeIcon icon="rotate-right" onClick={() => refreshTableData()} />
            </Tooltip>
            <div>{tableData.length} rows</div>
          </div>
        </div>
      </div>
      <div styleName="card-body">
        <div styleName="relative">
          { tableLoading && <LoadingOverlay /> }
          <TableContainer component={Paper} styleName="table">
            <InfiniteScroll
              className={scrollId} // Needs to be unhashed so use 'className'
              onScroll={handleScroll}
              dataLength={tableData.length}
              next={fetchMoreData}
              hasMore={hasMore}
              loader={
                tableInitiallyLoaded &&
                <div styleName="fetch-more-loader">
                  <CircularProgress color="primary" size={30} />
                </div>
              }
              height={props.tableHeight}
              endMessage={
                <p styleName="center">
                  <b>No more items</b>
                </p>
              }
            >
              <CustomTable />
            </InfiniteScroll>
          </TableContainer>
        </div>
      </div>
    </div>
  );
}

export default DataTable;

export interface TableHeader {
  id: string;
  label: string;
  isSortable?: boolean;
  align?: "left" | "center" | "right" | "justify" | "inherit" | undefined;
}

enum Order {
  Asc = 'asc',
  Desc = 'desc'
}