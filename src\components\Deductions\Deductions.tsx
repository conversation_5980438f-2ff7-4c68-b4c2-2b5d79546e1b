import { GetDeductionsCDto } from "redi-types";
import { PartyTypeEnum } from "../../enum/PartyTypeEnum";
import { Field, FieldArray, FieldArrayRenderProps, FormikErrors, FormikTouched, getIn } from "formik";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import { InputAdornment, MenuItem, TextField, Tooltip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import './styles.scss';

interface Props {
  touchedList: FormikTouched<GetDeductionsCDto>[] | undefined;
  errorList: string | string[] | FormikErrors<GetDeductionsCDto>[] | undefined;
  deductions: GetDeductionsCDto[] | undefined;
  limit?: number;
  showDelete?: boolean;
  setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void;
  deductionTypes: string[];
}

function addDeduction(fields: FieldArrayRenderProps) {
  const dto = {
    deductionId: NIL_UUID,
    parentEntityId: NIL_UUID,
    type: "Radio Fee",
    amount: 0,
  }
  fields.push(dto);
}

function Deductions(props: Props) {
  const { deductions, touchedList, errorList } = props;
  return (
    <FieldArray name="deductions">
      {(fields: FieldArrayRenderProps) => (
        <>
          <div>
            {deductions &&
              deductions.map((dto, index) => (
                <div styleName="row address-row-container" key={index}>
                  <div
                    styleName="column field-container size-5"
                    key={`deduction_${dto.deductionId}`}
                  >
                    <div styleName="row">
                      <div styleName="field-container size-2">
                        <Field
                          select
                          variant="standard"
                          id={`deductions_type${index}`}
                          name={`deductions[${index}].type`}
                          label={"Deduction Type"}
                          as={TextField}
                          error={touchedList && touchedList[index]?.type && Boolean(getIn(errorList?.[index], `type`))}
                          helperText={touchedList && touchedList[index]?.type && getIn(errorList?.[index], `type`)}
                        >
                          {props.deductionTypes.map((type) => (
                            <MenuItem key={type} value={type}>
                              {type}
                            </MenuItem>
                          ))}
                        </Field>
                        {deductions && deductions[index] && deductions[index].type === "Other" && (
                          <Field
                            variant="standard"
                            id={`otherDeduction${index}`}
                            name={`deductions[${index}].otherType`}
                            label={"Other Deduction Name"}
                            as={TextField}

                          // error={touchedList && touchedList[index]?.other && Boolean(getIn(errorList?.[index], `other`))}
                          // helperText={touchedList && touchedList[index]?.other && getIn(errorList?.[index], `other`)}
                          />
                        )}
                      </div>
                      <div styleName="field-container size-1">
                        <Field
                          type="number"
                          variant="standard"
                          id={`deductions_amount${index}`}
                          name={`deductions[${index}].amount`}
                          label={"Amount"}
                          as={TextField}
                          error={touchedList && touchedList[index]?.amount && Boolean(getIn(errorList?.[index], `amount`))}
                          helperText={touchedList && touchedList[index]?.amount && getIn(errorList?.[index], `amount`)}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                $
                              </InputAdornment>
                            ),
                          }} />
                      </div>
                      <div styleName="trashicon">
                        <Tooltip
                          title="Remove deduction"
                          placement="bottom"
                        >
                          <div
                            styleName="column field-container-action-button center-justify center-align"
                            onClick={() => fields.remove(index)}
                          >
                            <FontAwesomeIcon icon="trash" />
                          </div>
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
          <div styleName="row center-justify center-align">
            <Tooltip title="Add a new deduction" placement="bottom">
              <div
                styleName="action-button"
                onClick={() => addDeduction(fields)}
              >
                <FontAwesomeIcon icon="circle-plus" />
                <div>Add Deduction</div>
              </div>
            </Tooltip>
          </div></>
      )}
    </FieldArray>
  )
}

export default Deductions;