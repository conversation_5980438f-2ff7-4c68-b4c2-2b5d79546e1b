import { Button, Dialog, DialogTitle, Paper, PaperProps } from '@mui/material';
import React from 'react';
import Draggable from 'react-draggable';
import './styles.scss';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

// Empty ref must be passed in to fix error in 'react-draggable': 'finddomnode-is-deprecated-in-strictmode'
// https://stackoverflow.com/questions/63603902/finddomnode-is-deprecated-in-strictmode-finddomnode-was-passed-an-instance-of-d
function PaperComponent(props: PaperProps) {
  const nodeRef = React.useRef(null);
  return (
    <Draggable
      nodeRef={nodeRef}
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper ref={nodeRef} {...props} />
    </Draggable>
  );
}

interface Props {
  title: string;
  bodyText: string;
  isOpen: boolean;
  onNo: () => void;
  onYes: () => void;
}

function YesNoDialog(props: Props) {

  const handleNo = () => props.onNo();
  const handleYes = props.onYes;
  
  return (
    <>
      <Dialog
        sx={{ height: '60%'}}
        open={props.isOpen}
        onClose={handleNo}
        PaperComponent={PaperComponent}
        aria-labelledby="draggable-dialog-title"
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle style={{ cursor: 'move' }} id="draggable-dialog-title">
          <div styleName="row">
            { props.title }
            <FontAwesomeIcon styleName="close-icon" icon="close" onClick={handleNo}/>
          </div>          
        </DialogTitle>
        <div styleName="custom-dialog-content">
          {props.bodyText}
          <div styleName="row">
            <div styleName="button-row">
              <Button variant="outlined" onClick={handleNo}>
                No
              </Button>
              <Button variant="contained" onClick={handleYes}>
                Yes
              </Button>
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
}

export default YesNoDialog;