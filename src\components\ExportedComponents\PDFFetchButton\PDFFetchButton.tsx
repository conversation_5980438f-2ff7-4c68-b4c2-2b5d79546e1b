import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, IconButton, Snackbar } from '@mui/material';
import React, { useState } from 'react';
import './styles.scss';
import fileManagerService from '../../../services/fileManager';
import { faSpinner } from '@fortawesome/pro-regular-svg-icons';

interface Props {
  paySlipId: string
}

function PDFFetchButton(props: Props) {
  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false); // added loading state

  const handleSnackbarClose = () => {
    setSnackbarMessage(null);
  };

  async function viewPdf(paySlipId: string) {
    let response = await fileManagerService.GetAuthenticatedUrlByParentId(paySlipId);

    if (!response) {
      console.log('No PDF found');
      setSnackbarMessage('No PDF found');
      return;
    }

    if (!response.data) {
      console.log('No PDF found');
      setSnackbarMessage('No PDF found');
      return;
    }

    const anchor = document.createElement('a');
    anchor.href = response.data;
    anchor.download = response.data;
    anchor.click();
  }

  async function buttonClick(event: React.MouseEvent, paySlipId: string) {
    event.stopPropagation();
    setLoading(true); // set loading to true

    try {
      viewPdf(paySlipId);
      setSnackbarMessage('Downloading PDF');
      setShowSnackbar(true);
    } catch (error) {
      setSnackbarMessage('Error Downloading PDF');
      setShowSnackbar(true);
    } finally {
    }
    //set a ten second timer
    setTimeout(() => {
      setLoading(false);
    }, 10000);

  }

  return (
    <>
      <IconButton onClick={(e) => buttonClick(e, props.paySlipId)}>
        {loading ?
          <FontAwesomeIcon title='Download PDF' styleName="row-menu-icon" icon={faSpinner} spin />
          :
          <FontAwesomeIcon title='Download PDF' styleName="row-menu-icon" icon="file-pdf" />
        }
      </IconButton>
      <Snackbar open={snackbarMessage !== null}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
        <Alert onClose={handleSnackbarClose} severity={snackbarMessage?.includes('Error') ? "error" : "success"}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}
export default PDFFetchButton;

