import { AlertColor, MenuItem, TextField } from "@mui/material";
import { Field, FormikProps } from "formik";
import { ChangeEvent, useEffect, useState } from "react";
import "./styles.scss";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DraggableDialog from "../DraggableDialog/DraggableDialog";
import MultipleAddressFieldModal from "../MultipleEditField/MultipleAddressFieldModal";
import { GetListAddressCDto } from "redi-types";
import addressService from "../../services/address";

interface Props {
  label: string;
  listItems: any[];
  form: FormikProps<any>;
  propertyKey: string;
  parentEntityType: string;
  parentEntityId: string;
  name?:string;
  initialValue?:string;
  onChange?: (addressId: string) => any;
}

function FormDropdownSelectOnTabAddress(props: Props) {
  const [highlightedValue, setHighlightedValue] = useState('');
  const [listItems, setlistItems] = useState<any[]>(props.listItems);

  const handleKeyDown = (event: KeyboardEvent) => {
    // Capture the highlighted value when arrow keys are pressed
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      const selectedIndex = props.listItems.findIndex(item => item[props.propertyKey] === highlightedValue);
      const newIndex = (event.key === 'ArrowDown') ? selectedIndex + 1 : selectedIndex - 1;

      if (newIndex >= 0 && newIndex < props.listItems.length) {
        setHighlightedValue(props.listItems[newIndex][props.propertyKey]);
      }
    }
  };

  //pull addresses if props.list is empty
  useEffect(()=> {
    if(props.listItems != null && props.listItems?.length > 0 ){
      setlistItems(props.listItems);
    }
    else if(props.parentEntityId != "" && props.parentEntityId != null){
      addressService.ListForParentEntityId(props.parentEntityId,props.parentEntityType).then((data) =>{
        if(data.data != null){
          setlistItems(data.data.list);
        }
      });
    }
    else{
      setlistItems([]);
    }
  },[props.parentEntityId, props.listItems]);

  const handleMenuClose = (event: any) => {
    // Callback function for when the dropdown menu closes

    // Set the field value to the highlighted value when leaving the dropdown
    const selectedValue = highlightedValue || event.target.value;
    const listItems = props.listItems.map(item => item[props.propertyKey]);

    if (listItems.includes(selectedValue)) {
      props.form.setFieldValue(props.propertyKey, selectedValue);
    }

    // TODO: Currenty unable to automatically navigate to the next tab target
  };

  const [IsAddAddressDialogOpen, setIsAddAddressDialogOpen] = useState(false);
  const isDisabled = !(props.parentEntityId && props.parentEntityType);

  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

  function openAddAddressDialog() {
    if (props.parentEntityId && props.parentEntityType) {
      setIsAddAddressDialogOpen(true);
    }
  }

  function closeAddAddressDialog() {
    setIsAddAddressDialogOpen(false);
  }

  async function onAddAddressDialogSave(message: string, severity: AlertColor) {
    closeAddAddressDialog();
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
  }

  const Dialogs = () => {
    return (
      <>
        <DraggableDialog title="Add New Address" isOpen={IsAddAddressDialogOpen} onCancel={closeAddAddressDialog}>
          <MultipleAddressFieldModal
            open={IsAddAddressDialogOpen}
            addresses={[]}
            useSubmit={true}
            parentEntityId={props.parentEntityId}
            parentEntityType={props.parentEntityType}
            onSubmit={onAddAddressDialogSave}
            onCancel={closeAddAddressDialog}
            onSave={(data) => {
              var newList : GetListAddressCDto[] = [...data, ...listItems];
              setlistItems(newList);
            }}
          />
        </DraggableDialog>
      </>
    );
  }

  return (
    <div styleName="title-row">
      <div styleName="search-field">
        <Field
          select
          as={TextField}
          id={props.name ?? props.propertyKey}
          name={props.name ?? props.propertyKey}
          initialValue={props.initialValue}
          label={props.label}
          onChange={(event: ChangeEvent<HTMLInputElement>) => {
            props.form.setFieldValue(props.propertyKey, event.target.value);
            if(props?.onChange != null){
              props?.onChange(event.target.value);
            }
          }}
          onKeyDown={handleKeyDown}
          error={Boolean(props.form.errors[props.propertyKey])}
          helperText={props.form.errors[props.propertyKey]}
          SelectProps={{ onClose: handleMenuClose }}
        >
          {listItems.map(item => (
            <MenuItem
              key={item[props.propertyKey]}
              value={item[props.propertyKey]}
              selected={item[props.propertyKey] === highlightedValue}
            >
              {item.fullAddress}
            </MenuItem>
          ))}
        </Field>
      </div>
      {/* Disable if parentEntityId doesnt exist */}
      <div styleName={`action-button ${isDisabled ? "disabled" : ""}`} onClick={openAddAddressDialog}>
        <FontAwesomeIcon icon="circle-plus" />
        <div>Add New Address</div>
      </div>

      <Dialogs />
    </div>
  );
}

export default FormDropdownSelectOnTabAddress;