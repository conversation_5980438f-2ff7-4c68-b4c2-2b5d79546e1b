@import "../../config/theme/vars.scss";

.title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-field {
        flex: 5;
        >div{
            min-width:100% !important;
        }

    }

    .action-button {
        flex: 2;
        display: flex;
        align-items: center;
        color: $primaryColor;
        cursor: pointer;
        margin-left:2rem;

        svg {
            font-size: 1.2rem;
            margin-right: 0.5rem;
        }

        div {
            font-weight: 500;
            font-size: 1rem;
        }
        
    }
}

.disabled {
    pointer-events: none;
    color: rgba(208, 206, 206, 0.694) !important;
  }