import { ListingMediaCDto, ManageListingMediaCDto } from "redi-types";
import "./styles.scss";
import "react-responsive-carousel/lib/styles/carousel.min.css"; // requires a loader
import { Carousel } from "react-responsive-carousel";
import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import YesNoDialog from "../YesNoDialog/YesNoDialog";
import ManageService from "../../services/manage";
import { Checkbox, IconButton, Tooltip } from "@mui/material";
import { ListingMediaDetails } from "../../pages/ListingManagement/Media/ListingMediaDetails/ListingMediaDetails";
interface Props {
  profileImageId?: number;
  onLoad?: any;
  mediaArray: ListingMediaCDto[];
  showThumbs?: boolean;
  showStatus?: boolean;
  showIndicators?: boolean;
  onSave?: () => void;
  updateProfile?: (profileMedia?: ListingMediaCDto) => void;
  isEdit?: boolean;
  height?: string;
  width?: string;
}

function ImageCarousel(props: Props) {
  const {
    showThumbs = false,
    showStatus = false,
    showIndicators = false,
  } = props;
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [listingId, setListingId] = useState<number>();
  const [mediaListingId, setMediaListingId] = useState<number>();
  const [profileImageId, setProfileImageId] = useState(props.profileImageId);

  async function handleDelete(listingId: number, listingMediaId: number) {
    await ManageService.deleteMedia(listingId, [listingMediaId]);
    props.onSave && props.onSave();
    setIsDeleteOpen(false);
  }

  function triggerDelete(listId: number, mediaId: number) {
    setListingId(listId);
    setMediaListingId(mediaId);
    setIsDeleteOpen(true);
  }
  console.log(props.mediaArray);

  return (
    <div styleName="">
      <Carousel
        showIndicators={showIndicators}
        showThumbs={showThumbs}
        showStatus={false}
        //width={700}
        renderArrowPrev={(onClickHandler, hasPrev, label) =>
          hasPrev && (
            <div
              style={{ left: 15 }}
              styleName="arrow-div"
              onClick={onClickHandler}
            >
              <FontAwesomeIcon
                size="xl"
                styleName="arrow-styles "
                icon={["fas", "arrow-left"]}
              />
            </div>
          )
        }
        renderArrowNext={(onClickHandler, hasNext, label) =>
          hasNext && (
            <div
              style={{ right: 15 }}
              styleName="arrow-div"
              onClick={onClickHandler}
            >
              <FontAwesomeIcon
                size="xl"
                styleName="arrow-styles "
                icon={["fas", "arrow-right"]}
              />
            </div>
          )
        }
      >
        {props.mediaArray.map((media, index) => {
          // console.log(media);
          switch (media.mediaTypeCode?.toLowerCase()) {
            case "image":
              return (
                <div key={index} styleName="column">
                  {props.isEdit && (
                    <ListingMediaDetails
                      list={props.mediaArray}
                      media={media}
                      onSave={() => {
                        props.onSave && props.onSave();
                      }}
                    />
                  )}
                  {props.isEdit && (
                    <div styleName="button-container">
                      <Tooltip title="Delete">
                        <IconButton
                          styleName="delete-button"
                          // onClick={() =>
                          //   triggerDelete(media.listingId, media.listingMediaId)
                          // }
                        >
                          <FontAwesomeIcon icon="close" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Profile Image">
                        <Checkbox
                          styleName="profile-button"
                          checked={media.listingMediaId === profileImageId}
                          onChange={(e) => {
                            if (e.target.checked === true) {
                              setProfileImageId(media.listingMediaId);
                              props.updateProfile && props.updateProfile(media);
                            } else {
                              setProfileImageId(undefined);
                              props.updateProfile &&
                                props.updateProfile(undefined);
                            }
                          }}
                        />
                      </Tooltip>
                    </div>
                  )}
                  <div styleName="image-container">
                    <img
                      style={{ height: props.height ? props.height : '265px', width: props.width ? props.width : '470px', maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                      key={index}
                      src={media.mediaUrl}
                      alt={media.title}
                      onLoad={props.onLoad}
                    />
                  </div>
                </div>
              );
            // case "youtube":
            //   return (
            //     <React.Fragment key={index}>
            //       Youtube Video
            //       <iframe
            //         allowFullScreen
            //         src={media.mediaUrl}
            //         key={index}
            //         height={"100%"}
            //         title="YouTube video player"
            //         allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            //       ></iframe>
            //     </React.Fragment>
            //   );
            // case "video":
            //   return (
            //     <React.Fragment key={index}>
            //       <div key={index} styleName="column">
            //         <Tooltip title="Delete">
            //           <IconButton
            //             styleName="delete-button"
            //             onClick={() =>
            //               triggerDelete(media.listingId, media.listingMediaId)
            //             }
            //           >
            //             <FontAwesomeIcon icon="close" />
            //           </IconButton>
            //         </Tooltip>
            //         <iframe
            //           allowFullScreen
            //           src={media.mediaUrl}
            //           height={"100%"}
            //           title="YouTube video player"
            //           allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            //         ></iframe>
            //       </div>
            //     </React.Fragment>
            //   );
            // case "pdf":
            //   console.log(media.mediaUrl);
            //   return (
            //     <React.Fragment key={index}>
            //       <div key={index} styleName="column">
            //         <Tooltip title="Delete">
            //           <IconButton
            //             styleName="delete-button"
            //             onClick={() =>
            //               triggerDelete(media.listingId, media.listingMediaId)
            //             }
            //           >
            //             <FontAwesomeIcon icon="close" />
            //           </IconButton>
            //         </Tooltip>
            //         <iframe
            //           src={media.mediaUrl}
            //           key={index}
            //           height={"100%"}
            //           title="PDF Display"
            //         ></iframe>
            //       </div>
            //     </React.Fragment>
            //   );
            default:
              return (
                <div key={index} styleName="column">
                  <Tooltip title="Delete">
                    <IconButton
                      styleName="delete-button"
                      // onClick={() =>
                      //   triggerDelete(media.listingId, media.listingMediaId)
                      // }
                    >
                      <FontAwesomeIcon icon="close" />
                    </IconButton>
                  </Tooltip>
                  <img
                    style={{ maxHeight: "400px", maxWidth: "650px" }}
                    key={index}
                    src={media.mediaUrl}
                    alt={media.title}
                  />
                </div>
              );
          }
        })}
      </Carousel>
      {isDeleteOpen ? (
        <YesNoDialog
          title="Delete Image"
          bodyText={`Are you sure? This cannot be reversed`}
          isOpen={true}
          onNo={() => setIsDeleteOpen(false)}
          onYes={() =>
            listingId && mediaListingId
              ? handleDelete(listingId, mediaListingId)
              : setIsDeleteOpen(false)
          }
        />
      ) : null}
    </div>
  );
}

export default ImageCarousel;
