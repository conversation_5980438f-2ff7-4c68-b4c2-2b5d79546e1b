@import "../../config/theme/vars.scss";

.light-box {
  // position: fixed;
  // top: 0;
  // left: 0;
  // width: 100%;
  // height: 100%;
  // background: rgba(0, 0, 0, 0.8);
  // z-index: 9999;
  // display: flex;
  // justify-content: center;
  // align-items: center;

  // .light-box-content {
  //   position: relative;
  //   max-width: 100%;
  //   max-height: 100%;

  //   .light-box-close {
  //     position: absolute;
  //     top: 10px;
  //     right: 10px;
  //     font-size: 30px;
  //     color: $secondaryColor;
  //     cursor: pointer;
  //     z-index: 9999;
  //   }
  // }

  // .light-box-content img {
  //   max-width: 100%;
  //   max-height: 100%;
  // }
}
.delete-button {
  width: 2rem;
}
.profile-button {
  width: 2rem;
}

.button-container {
  display: flex;
  justify-content: space-between;
}

.image-container {
  display: flex;
  justify-content: center;
}

.column {
  display: flex;
  flex-direction: column;
}

.arrow-div {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.349);
  z-index: 2;
  top: calc(50% - 15px);
  width: 50px;
  height: 50px;
  cursor: pointer;

  >.arrow-styles {
    color: white;
  }
}