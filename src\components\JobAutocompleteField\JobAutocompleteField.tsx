import JobService from "../../services/job";
import RediAutocompleteField, { AutocompleteProps } from "../RediField/Autocomplete/RediAutocompleteField";
import "./styles.scss";

const standardListParameters = {
    limit: 50,
    offset: 0,
    sortBy: 'jobReference',
    isDeleted: false,
    forceNullBottom: true
};

function JobAutocompleteField(props: Props) {

    const { value, fieldDisplayText = "jobReference", fieldValue = "jobId", ...other } = props;

    const handleCall = (query?: string) => {
        return JobService.getList(
            standardListParameters,
            query
        );
    };
    
    return (
        <RediAutocompleteField 
            {...other}
            fieldDisplayText={fieldDisplayText}
            fieldValue={fieldValue}
            value={value}
            callService={handleCall}
        />
    );
}

interface Props extends Omit<AutocompleteProps, "callService" | "fieldDisplayText" | "fieldValue"> {
    value:  | null;
    fieldDisplayText?: string;
    fieldValue?: string;
}

export default JobAutocompleteField;