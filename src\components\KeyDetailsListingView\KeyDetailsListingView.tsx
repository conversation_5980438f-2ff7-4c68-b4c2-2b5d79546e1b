import { ListingDataCDto } from 'redi-types';
import './styles.scss';
import { AttributeValueTypeCode } from '../../enum/AttributeValueTypeCode';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconName } from '@fortawesome/fontawesome-svg-core';

interface Props {
  attribute: ListingDataCDto;
}

function KeyDetailsListingView(props: Props) {
  switch (props.attribute.attributeValueTypeCode) {
    case AttributeValueTypeCode.ValueString:
      return (
        <div styleName='row'>
          {props.attribute.icon ? <FontAwesomeIcon size='xl' styleName='icon-styles' icon={["fad", props.attribute.icon as IconName]} /> : <FontAwesomeIcon size='xl' styleName='icon-styles' icon={["fad", 'hashtag']} />}
          <div styleName='col'>
            <p styleName='attribute-value' >{props.attribute.valueString}</p>
            <p styleName='attribute'>{props.attribute.label}</p>
          </div>
        </div>

      );
    case AttributeValueTypeCode.ValueNumeric:
      return (
        <div styleName='row'>
          {props.attribute.icon ? <FontAwesomeIcon size='xl' styleName='icon-styles' icon={["fad", props.attribute.icon as IconName]} /> : <FontAwesomeIcon size='xl' styleName='icon-styles' icon={["fad", 'hashtag']} />}
          <div styleName='col'>
            <p styleName='attribute-value'>{props.attribute.valueNumeric}</p>
            <p styleName='attribute'> {props.attribute.label}</p>
          </div>
        </div>
      );
    case AttributeValueTypeCode.ValueDateTime:
      return (
        <li styleName='key-details-value'>  {props.attribute.valueDateTime?.toLocaleDateString('en-au')} </li>
      );
    case AttributeValueTypeCode.ValueStringMax:
      return (
        <li styleName='key-details-value'>  {props.attribute.valueStringMax} </li>
      );
    default:
      return (
        <li styleName='key-details-value'>  {props.attribute.valueString} </li>
      );

  }
}

export default KeyDetailsListingView;
