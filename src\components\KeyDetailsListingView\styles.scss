@import "../../config/theme/vars.scss";

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.col {
  display: flex;
  flex-direction: column;
}

.no-overflow {
  overflow: hidden;
  width: 100%;
}

.card {
  background: $white;
  margin: 2rem 2rem 0.5rem 2rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.icon-styles {
  color: $secondaryColor;
  margin-right: 10px;
}

.key-details-value {}

.attribute {
  margin: 0;
}

.attribute-value {
  margin: 0;
  font-weight: bold;
}