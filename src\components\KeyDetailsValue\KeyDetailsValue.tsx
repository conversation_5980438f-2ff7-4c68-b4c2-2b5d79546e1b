import { ListingDataCDto } from 'redi-types';
import './styles.scss';
import { AttributeValueTypeCode } from '../../enum/AttributeValueTypeCode';


interface Props {
  attributeValueTypeCode: string;
  value: ListingDataCDto;
}

function KeyDetailsValue(props: Props) {

  switch (props.attributeValueTypeCode) {
    case AttributeValueTypeCode.ValueString:
      return (
        <li styleName='key-details-value'>  {props.value.valueString} </li>
      );
    case AttributeValueTypeCode.ValueNumeric:
      return (
        <li styleName='key-details-value'>  {props.value.valueNumeric} </li>
      );
    case AttributeValueTypeCode.ValueDateTime:
      return (
        <li styleName='key-details-value'>  {props.value.valueDateTime?.toLocaleDateString('en-au')} </li>
      );
    case AttributeValueTypeCode.ValueStringMax:
      return (
        <li styleName='key-details-value'>  {props.value.valueStringMax} </li>
      );
    default:
      return (
        <li styleName='key-details-value'>  {props.value.valueString} </li>
      );

  }
}

export default KeyDetailsValue;
