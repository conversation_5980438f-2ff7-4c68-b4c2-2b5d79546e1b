@import "../../config/theme/vars.scss";

.listing-subject {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0;
  line-height: 1.3;
  color: #2a2a2a;
}

.key-details {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-left: 0px;
  overflow: hidden;
}

.key-details-value {
  width: 50%;
  padding-bottom: 5px;
  list-style: none;
  white-space: nowrap;
  overflow: hidden;
  padding-left: 5px;
}

.key-details-value::before {
  content: "";
  width: 3px;
  height: 3px;
  border-radius: 100%;
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
  position: relative;
  background: #4a4a4a;
}