import { ListingMediaCDto } from "redi-types";
import "./styles.scss";
import Lightbox, {
  Slide,
  SlideImage,
  SlideVideo,
  YoutubeSlide,
} from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import { useEffect, useState } from "react";
import Video from "yet-another-react-lightbox/plugins/video";

interface Props {
  open?: boolean;
  mediaArray?: ListingMediaCDto[];
  setOpen: (open: boolean) => void;
}

import { GenericSlide } from "yet-another-react-lightbox";

declare module "yet-another-react-lightbox" {
  export interface YoutubeSlide extends GenericSlide {
    src: string;
    title: string;
  }

  interface SlideTypes {
    youtube: YoutubeSlide;
  }
}

function LightBoxWrapper(props: Props) {
  if (!props.mediaArray) {
    return null;
  }

  const [controls, setControls] = useState(true);
  const [playsInline, setPlaysInline] = useState(true);
  const [autoPlay, setAutoPlay] = useState(false);
  const [loop, setLoop] = useState(false);
  const [muted, setMuted] = useState(false);
  const [disablePictureInPicture, setDisablePictureInPicture] = useState(false);
  const [disableRemotePlayback, setDisableRemotePlayback] = useState(false);
  const [controlsList, setControlsList] = useState<
    ("nodownload" | "nofullscreen" | "noremoteplayback")[]
  >([]);
  const [crossOrigin, setCrossOrigin] = useState("");
  const [preload, setPreload] = useState("");

  const transformData: Slide[] = props.mediaArray.map((media, index) => {
    switch (media.mediaTypeCode?.toLowerCase()) {
      case "image":
        return {
          type: "image",
          src: media.mediaUrl || "",
          title: media.title || "",
          alt: media.title || "",
        } as SlideImage; // Cast the object to SlideImage
      case "video":
        return {
          type: "video",
          sources: [{ src: media.mediaUrl || "", type: "video/mp4" }],
          title: media.title || "",
          alt: media.title || "",
        } as SlideVideo; // Cast the object to SlideVideo
      case "youtube":
        return {
          type: "youtube",
          src: media.mediaUrl,
          title: media.title, // Corrected: Added missing closing parenthesis
        } as YoutubeSlide; // Cast the object to YoutubeSlide
      default:
        return {
          type: "image",
          src: media.mediaUrl || "",
          title: media.title || "",
          alt: media.title || "",
        } as SlideImage; // Cast the object to SlideImage
    }
  });

  return (
    <Lightbox
      plugins={[Video]}
      slides={transformData}
      open={props.open}
      close={() => props.setOpen(false)}
      video={{
        controls,
        playsInline,
        autoPlay,
        loop,
        muted,
        disablePictureInPicture,
        disableRemotePlayback,
        controlsList: controlsList.join(" "),
        crossOrigin,
        preload,
      }}
      render={{
        slide: ({ slide, rect }) =>
          slide.type === "youtube" ? (
            <iframe
              width={"100%"}
              height={"100%"}
              src={slide.src}
              // title={slide.title}
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          ) : null,
      }}
    ></Lightbox>
  );
}

export default LightBoxWrapper;
