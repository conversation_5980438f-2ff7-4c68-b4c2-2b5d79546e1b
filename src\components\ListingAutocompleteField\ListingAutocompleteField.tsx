import { FilterExcludeAttributesDto, ListingWithDataAndMediaCDto, ManageListingWithDisplayGroupedDataAndMediaDto, StandardListParameters } from "redi-types";
import RediAutocompleteField, { AutocompleteProps } from "../RediField/Autocomplete/RediAutocompleteField";
import ListingService from "../../services/listing";
import DraggableDialog from "../DraggableDialog/DraggableDialog";
import { TruckForm } from "../../pages/Truck/TruckForm/TruckForm";
import { ListingTypeEnum } from "../../enum/listingTypeEnum";
import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "./styles.scss";

function ListingAutocompleteField(props: Props) {

    const { value, params, ...other } = props;
    const [ openDialog, setOpenDialog ] = useState(false);

    const standardListParameters = params?.standardListParameters ?? {
        limit: 50,
        offset: 0,
        sortBy: '1',
        isDeleted: false,
        forceNullBottom: true
    };

    const handleCall = (query?: string) => {
        return ListingService.getListQuery(
            params?.filtersObject,
            params?.excludesObject,
            params?.displayContainerCodes,
            params?.parentEntityIntId,
            params?.parentEntityId,
            params?.parentEntityType,
            params?.parentListingId,
            params?.statusCode,
            params?.visibility,
            params?.listingTypeId,
            other.fieldDisplayText === "subject" ? query : params?.subject,
            other.fieldDisplayText === "description" ? query : params?.description,
            params?.beforeDate,
            params?.afterDate,
            standardListParameters,
            params?.includeMedia,
            params?.mediaCategoryCodes,
            params?.includeAttributes,
            params?.fromLocationLat,
            params?.fromLocationLong,
            params?.searchDistanceKm
        );
    };

    const handleOpenDialog = () => {
        setOpenDialog((prev) => !prev);
    };

    const handleOnSave = (value?: ManageListingWithDisplayGroupedDataAndMediaDto) => {
        if (value && value.listingId) {
            other.onChange(value.listingId.toString(), value);
        }
    };
    
    return (
        <>
            <div styleName="title-row">
                <div styleName="search-flex">
                    <div styleName="search-field">
                        <RediAutocompleteField 
                            {...other}
                            value={value}
                            callService={handleCall}
                        />
                    </div>
                </div>
            {props.showAddNewButton ?
            <div styleName={`action-button ${props.disableAddButton ? "disabled" : ""} `} onClick={handleOpenDialog}>
            <FontAwesomeIcon icon="circle-plus" />
            <div>Add New {props.buttonLabel}</div>
            </div> : undefined}
            </div>
            <DraggableDialog title={`Add New ${props.buttonLabel}`} isOpen={openDialog} onCancel={handleOpenDialog}>
            {params?.listingTypeId === ListingTypeEnum.Truck ?
                <TruckForm
                    onCancel={handleOpenDialog}
                    onSave={handleOnSave}
                    showHeader={false}
                    showAddDriver={false}
                /> : null}
            </DraggableDialog>
        </>
    );
}

interface Props extends Omit<AutocompleteProps, "callService"> {
    value: ListingWithDataAndMediaCDto | null;
    fieldDisplayText: "subject" | "description";
    buttonLabel?: string;
    disableAddButton?: boolean;
    showAddNewButton?: boolean;
    params?: {
        filtersObject?: FilterExcludeAttributesDto;
        excludesObject?: FilterExcludeAttributesDto;
        displayContainerCodes?: string;
        parentEntityIntId?: number | undefined;
        parentEntityId?: string | undefined;
        parentEntityType?: string | undefined;
        parentListingId?: number;
        statusCode?: string | undefined;
        visibility?: number | undefined;
        listingTypeId?: number | undefined;
        subject?: string | undefined;
        description?: string | undefined;
        beforeDate?: Date | undefined;
        afterDate?: Date | undefined;
        standardListParameters?: StandardListParameters;
        includeMedia?: boolean;
        mediaCategoryCodes?: string | undefined;
        includeAttributes?: boolean;
        fromLocationLat?: number | undefined;
        fromLocationLong?: number | undefined;
        searchDistanceKm?: number | undefined;
    }
}

export default ListingAutocompleteField;