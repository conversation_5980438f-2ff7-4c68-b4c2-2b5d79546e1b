import { FilterExcludeAttributesDto, ListingWithDataAndMediaCDto, StandardListParameters } from "redi-types";
import ListingService from "../../services/listing";
import RediAutocompleteMultiselectField, { AutocompleteMultiselectProps } from "../RediField/AutocompleteMultiselect/RediAutocompleteMultiselectField";

function ListingAutocompleteMultiselectField(props: Props) {

    const { value, params, ...other } = props;

    const standardListParameters = params?.standardListParameters ?? {
        limit: 50,
        offset: 0,
        sortBy: '1',
        isDeleted: false,
        forceNullBottom: true
    };

    const handleCall = (query?: string) => {
        return ListingService.getListQuery(
            params?.filtersObject,
            params?.excludesObject,
            params?.displayContainerCodes,
            params?.parentEntityIntId,
            params?.parentEntityId,
            params?.parentEntityType,
            params?.parentListingId,
            params?.statusCode,
            params?.visibility,
            params?.listingTypeId,
            other.fieldDisplayText === "subject" ? query : params?.subject,
            other.fieldDisplayText === "description" ? query : params?.description,
            params?.beforeDate,
            params?.afterDate,
            standardListParameters,
            params?.includeMedia,
            params?.mediaCategoryCodes,
            params?.includeAttributes,
            params?.fromLocationLat,
            params?.fromLocationLong,
            params?.searchDistanceKm
        );
    };
    
    return (
        <RediAutocompleteMultiselectField 
            {...other}
            value={value}
            callService={handleCall}
        />
    );
}

interface Props extends Omit<AutocompleteMultiselectProps, "callService"> {
    value: ListingWithDataAndMediaCDto[] | [];
    fieldDisplayText: "subject" | "description";
    params?: {
        filtersObject?: FilterExcludeAttributesDto;
        excludesObject?: FilterExcludeAttributesDto;
        displayContainerCodes?: string;
        parentEntityIntId?: number | undefined;
        parentEntityId?: string | undefined;
        parentEntityType?: string | undefined;
        parentListingId?: number;
        statusCode?: string | undefined;
        visibility?: number | undefined;
        listingTypeId?: number | undefined;
        subject?: string | undefined;
        description?: string | undefined;
        beforeDate?: Date | undefined;
        afterDate?: Date | undefined;
        standardListParameters?: StandardListParameters;
        includeMedia?: boolean;
        mediaCategoryCodes?: string | undefined;
        includeAttributes?: boolean;
        fromLocationLat?: number | undefined;
        fromLocationLong?: number | undefined;
        searchDistanceKm?: number | undefined;
    }
}

export default ListingAutocompleteMultiselectField;