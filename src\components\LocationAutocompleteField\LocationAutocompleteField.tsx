import LocationService from "../../services/location";
import RediAutocompleteField, { AutocompleteProps } from "../RediField/Autocomplete/RediAutocompleteField";
import "./styles.scss";

const standardListParameters = {
    limit: 50,
    offset: 0,
    sortBy: 'name',
    isDeleted: false,
    forceNullBottom: true
};

function ListingAutocompleteField(props: Props) {

    const { value, params, ...other } = props;

    const handleCall = (query?: string) => {
        return LocationService.getList(
            standardListParameters,
            query,
            params?.type
        );
    };
    
    return (
        <RediAutocompleteField 
            {...other}
            fieldDisplayText="name"
            fieldValue="locationId"
            value={value}
            callService={handleCall}
        />
    );
}

interface Props extends Omit<AutocompleteProps, "callService"> {
    value:  | null;
    params?: {
        type?: string;
    }
}

export default ListingAutocompleteField;