import { useEffect, useRef, useState } from "react";
import LocationService from "../../services/location";
import "./styles.scss";
import { HttpPromise } from "redi-http";
import { ListResponseDto, LocationCDto } from "redi-types";
import { Field, getIn, useFormikContext } from "formik";
import { MenuItem, TextField } from "@mui/material";

const standardListParameters = {
    limit: 50,
    offset: 0,
    sortBy: 'name',
    isDeleted: false,
    forceNullBottom: true
};

function ListingAutocompleteField(props: Props) {

    const { params, fieldValue = "locationId", name, id, label, query } = props;
    const { touched, errors } = useFormikContext();
    const promiseRef = useRef<HttpPromise<ListResponseDto<LocationCDto>>>();
    const [ list, setList ] = useState<LocationCDto[] | null>(null);
    
    const cancel = () => {
        if (promiseRef.current?.cancel) {
            promiseRef.current.cancel();
        }
    };

    const handleCall = async () => {
        cancel();
        promiseRef.current = LocationService.getList(
            standardListParameters,
            query,
            params?.type
        );
        const response = await promiseRef.current;
        if (!response.error && response.data && response.data.list) {
            setList(response.data.list);
        }     
    };

    useEffect(() => {
        handleCall();
        return () => {
            cancel();
        };
    }, []);
    
    return (
        list ?
        <Field
            select
            fullWidth
            id={id}
            name={name}
            label={label}
            as={TextField}
            error={Boolean(getIn(touched, name) && getIn(errors, name))}
            helperText={getIn(touched, name) &&getIn(errors, name)}
        >
            {list.map((item) => (
                <MenuItem key={item.locationId} value={item[fieldValue]}>
                    {item.name}
                </MenuItem>
            ))}
        </Field> : null
    );
}

interface Props {
    id: string;
    name: string;
    /** Pre-filter list */
    query?: string;
    label?: string | undefined;
    fieldValue?: "locationId" | "name"
    params?: {
        type?: string;
    }
}

export default ListingAutocompleteField;