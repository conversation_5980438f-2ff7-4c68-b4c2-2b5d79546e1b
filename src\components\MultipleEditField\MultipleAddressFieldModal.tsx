import * as React from 'react';

import styles from './styles.scss';
import CSSModules from 'react-css-modules';
import { AlertColor, Button, ButtonBase, Dialog, DialogContent, DialogTitle, Grid, MenuItem, Radio, TextField } from '@mui/material';
import { Field, FieldArray, Formik, FormikProps, getIn } from 'formik';
import * as yup from 'yup';
import { AddressTypeCDto, GetListAddressCDto, } from 'redi-types';
import addressservice from '../../services/address';
//import countryservice from '../../services/country'; // TODO - Add lookup call from common 
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import AddressTypeService from '../../services/addressType';
import AddressService from '../../services/address';

export default class MultipleAddressFieldModal extends React.PureComponent<Props, State> {
    schema: object;
    multipleAddressField: IAddressField;
    constructor(props: Props) {
        super(props);
        this.multipleAddressField = {
            addresses: this.props.addresses
        };
        this.schema = yup.object().shape({
            addresses: yup.array()
                .of(
                    yup.object().shape({
                        lines: yup.string().min(4, 'too short').required('Required'),
                        stateOrProvince: yup.string().min(2, 'too short').required('Required'),
                        postalCode: yup.string().min(3, 'too short').max(9, "too many characters").required('Required'),
                        country: yup.string().required("Required")
                    })
                )
                .required('Must have addresses')
                .min(1, 'Minimum have at least 1 address')
        });
        this.state = {
            //countryList: []
            addressTypeList: []
        };
    }

    componentDidMount() {
        //countryservice.GetList().then((data) => {
        //    if (!data.error) {
        //        this.setState({ countryList: data.data });
        //    }
        //});
        AddressTypeService.GetList().then((data) => {
            if (!data.error) {
                this.setState({ addressTypeList: data.data ?? [] });
            }
        });
    }

    async optionalOnSave(data: GetListAddressCDto[]) {
        let allRequestsSuccessful = true;
        for (const item of data) {
            item.parentEntityType = this.props.parentEntityType as string;
            item.parentEntityId = this.props.parentEntityId as string;

            try {
                const response = await AddressService.Create(item);
                if (!response.data) {
                    allRequestsSuccessful = false;
                }
            } catch (error) {
                allRequestsSuccessful = false;
            }

            if (allRequestsSuccessful) {
                this.props.onSubmit && this.props.onSubmit('Successfully Created Address', 'success');
            } else {
                this.props.onSubmit && this.props.onSubmit('Failed to Create Address', 'error');
            }
        }
    }

    renderFieldArray(form: FormikProps<IAddressField>) {
        return (
            <FieldArray name={"addresses"} validateOnChange={true}>
                {CSSModules((fields: any) => {
                    return (
                        <React.Fragment>
                            <Grid container>
                                <Grid item xs={form.values.addresses.length > 0 ? 10 : 5}>Address</Grid>
                                <Grid item xs={form.values.addresses.length > 0 ? 1 : 3}><div style={{ textAlign: "center" }}>Primary</div></Grid>
                                <Grid item xs={form.values.addresses.length > 0 ? 1 : 2}>
                                    <div styleName="add-button-container">
                                        <div styleName="add-icon" onClick={() => {
                                            fields.push(addressservice.GetDefault());
                                        }}>
                                            <ButtonBase>
                                                <div styleName="inner-icon">
                                                    <FontAwesomeIcon icon="plus" />
                                                </div>
                                            </ButtonBase>
                                        </div>
                                    </div>
                                </Grid>
                            </Grid>
                            <Grid container>
                                {form.values.addresses.map((dto: GetListAddressCDto, index: number) => {
                                    return (
                                        <React.Fragment key={`_addresses_"+${dto.addressId}_${index}`}>
                                            <Grid item xs={10}>
                                                <div styleName="address-container">
                                                    <div styleName="row-item-large">
                                                        <Field
                                                            id={`addresses_"${dto.addressId}_${index}_lines`}
                                                            name={`addresses[${index}].lines`}
                                                            label={"Address Lines"}
                                                            as={TextField}
                                                            error={form.touched.addresses && form.touched.addresses[index]?.lines && Boolean(getIn(form.errors.addresses?.[index], `lines`))}
                                                            helperText={form.touched.addresses && form.touched.addresses[index]?.lines && getIn(form.errors.addresses?.[index], `lines`)}
                                                        />
                                                    </div>
                                                    <div styleName="row-item-large">
                                                        <Field
                                                            select
                                                            id={`addresses_"${dto.addressId}_${index}_stateOrProvince`}
                                                            name={`addresses[${index}].stateOrProvince`}
                                                            label={"State"}
                                                            as={TextField}
                                                            error={form.touched.addresses && form.touched.addresses[index]?.stateOrProvince && Boolean(getIn(form.errors.addresses?.[index], `stateOrProvince`))}
                                                            helperText={form.touched.addresses && form.touched.addresses[index]?.stateOrProvince && getIn(form.errors.addresses?.[index], `stateOrProvince`)}
                                                        >
                                                            <MenuItem value="NSW">NSW</MenuItem>
                                                            <MenuItem value="QLD">QLD</MenuItem>
                                                            <MenuItem value="SA">SA</MenuItem>
                                                            <MenuItem value="TAS">TAS</MenuItem>
                                                            <MenuItem value="VIC">VIC</MenuItem>
                                                            <MenuItem value="WA">WA</MenuItem>
                                                        </Field>
                                                    </div>
                                                    <div styleName="row-item">
                                                        <Field
                                                            id={`addresses_"${dto.addressId}_${index}_postalCode`}
                                                            name={`addresses[${index}].postalCode`}
                                                            label={"Postal Code"}
                                                            as={TextField}
                                                            error={form.touched.addresses && form.touched.addresses[index]?.postalCode && Boolean(getIn(form.errors.addresses?.[index], `postalCode`))}
                                                            helperText={form.touched.addresses && form.touched.addresses[index]?.postalCode && getIn(form.errors.addresses?.[index], `postalCode`)}
                                                        />
                                                    </div>
                                                    <div styleName="row-item-large">
                                                        <Field
                                                            //select
                                                            id={`addresses_"${dto.addressId}_${index}_country`}
                                                            name={`addresses[${index}].country`}
                                                            label={"Country"}
                                                            as={TextField}
                                                            classes={styles}
                                                            error={form.touched.addresses && form.touched.addresses[index]?.country && Boolean(getIn(form.errors.addresses?.[index], `country`))}
                                                            helperText={form.touched.addresses && form.touched.addresses[index]?.country && getIn(form.errors.addresses?.[index], `country`)}
                                                        >
                                                            {/*{this.state.countryList.map((item, index) => (*/}
                                                            {/*    <MenuItem key={item.countryId} value={item.countryId}>{item.countryName}</MenuItem>*/}
                                                            {/*))}*/}
                                                        </Field>
                                                    </div>
                                                    <div styleName="row-item-large">
                                                        <Field
                                                            select
                                                            id={`addresses_"${dto.addressId}_${index}_addressTypeCode`}
                                                            name={`addresses[${index}].addressTypeCode`}
                                                            label={"Type"}
                                                            as={TextField}
                                                            classes={styles}
                                                            error={form.touched.addresses && form.touched.addresses[index]?.addressTypeCode && Boolean(getIn(form.errors.addresses?.[index], `addressTypeCode`))}
                                                            helperText={form.touched.addresses && form.touched.addresses[index]?.addressTypeCode && getIn(form.errors.addresses?.[index], `addressTypeCode`)}
                                                        >
                                                            {this.state.addressTypeList.map((item, index) => (
                                                                <MenuItem key={item.addressTypeCode} value={item.addressTypeCode}>{item.description}</MenuItem>
                                                            ))}
                                                        </Field>
                                                    </div>
                                                </div>
                                            </Grid>
                                            <Grid item xs={1}>
                                                <div styleName="align-action-buttons">
                                                    <Field
                                                        type="checkbox"
                                                        id={`addressesradio_"${dto.addressId}_${index}`}
                                                        name={`addresses[${index}].isPreferredAddress`}
                                                        onChange={() => {
                                                            for (let i = 0; i < form.values.addresses.length; i++) {
                                                                form.setFieldValue(`addresses[${i}].isPreferredAddress`, false);
                                                            }
                                                            form.setFieldValue(`addresses[${index}].isPreferredAddress`, true);
                                                        }}
                                                        as={Radio}
                                                    />
                                                </div>
                                            </Grid>
                                            <Grid item xs={1}>
                                                <div styleName="align-action-buttons">
                                                    <div styleName="add-icon" onClick={() => fields.remove(index)}>
                                                        <ButtonBase>
                                                            <div styleName="inner-icon">
                                                                <FontAwesomeIcon icon="trash" />
                                                            </div>
                                                        </ButtonBase>
                                                    </div>
                                                </div>
                                            </Grid>
                                        </React.Fragment>
                                    );
                                })}
                            </Grid>
                            <div style={{ color: "red", paddingTop: "17px" }}>
                                {typeof form.errors.addresses === 'string' ? <div>{form.errors.addresses}</div> : null}
                            </div>

                        </React.Fragment>
                    );
                }, styles, { allowMultiple: true })}
            </FieldArray>
        );
    }

    render() {
        return (
            <Dialog
                open={this.props.open}
                onClose={this.props.onCancel}
                maxWidth="lg"
            >
                <DialogTitle>
                    Addresses
                </DialogTitle>
                <DialogContent>
                    <div styleName="modal">
                        <Formik<IAddressField>
                            //classes={styles}
                            validationSchema={this.schema}
                            initialValues={this.multipleAddressField}
                            enableReinitialize={false}
                            validateOnChange={false}
                            validateOnBlur={false}
                            onSubmit={(data, actions) => {
                                actions.setSubmitting(false);
                                if (data.addresses.some(x => x.isPreferredAddress)) {
                                    this.props.onSave(data.addresses);
                                    this.props.onCancel && this.props.onCancel();
                                }
                                if (this.props.useSubmit) {
                                    this.optionalOnSave(data.addresses);
                                    this.props.onCancel && this.props.onCancel();
                                }
                            }}>
                            {form => (
                                <form onSubmit={form.handleSubmit}>
                                    {this.renderFieldArray(form)}
                                    <div styleName="button-wrapper">
                                        {this.props.onCancel ?
                                            <Button variant="outlined" onClick={() => this.props.onCancel()}>Cancel</Button> : <div />}
                                        <Button variant="contained" type="submit"
                                            onClick={() => form.handleSubmit}>Save</Button>
                                    </div>
                                </form>
                            )}
                        </Formik>
                    </div>
                </DialogContent>
            </Dialog>
        );
    }
}

interface IAddressField {
    addresses: GetListAddressCDto[];
}

interface Props {
    open: boolean;
    addresses: GetListAddressCDto[];
    onSave: (data: GetListAddressCDto[]) => void;
    onCancel: () => void;
    useSubmit?: boolean;
    parentEntityType?: string;
    parentEntityId?: string;
    onSubmit?: (message: string, severity: AlertColor, address?: GetListAddressCDto) => void;
}

interface State {
    //countryList: Array<GetListCountryDto>;
    addressTypeList: Array<AddressTypeCDto>;
}
