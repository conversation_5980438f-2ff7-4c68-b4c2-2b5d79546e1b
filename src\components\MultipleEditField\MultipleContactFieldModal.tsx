import * as React from 'react';

import styles from './styles.scss';
import CSSModules from 'react-css-modules';
import { <PERSON>ton, ButtonBase, Dialog, DialogContent, DialogTitle, Radio, TextField } from '@mui/material';
import { Field, FieldArray, Formik, FormikProps, getIn } from 'formik';
import * as yup from 'yup';
import { BaseContactMethodCDto } from 'redi-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { PhoneValidator } from '../../utils/phoneValidator';

export default class MultipleContactFieldModal extends React.PureComponent<Props, State> {
    schema: object;
    multipleContactField: IContactField;
	constructor(props: Props) {
		super(props);
        this.multipleContactField = { 
            contacts: this.props.contacts.filter(x => x.contactMethodTypeCode === this.props.type)
        };
        this.schema = yup.object().shape(this.props.type === "Email" ? {   
            contacts: yup.array()
              .of(
                yup.object().shape({
                  value: yup.string().email("Please enter email").min(4, 'too short').max(255, 'too long').required('Required')
                })
              )
              .required('Must have emails')
              .min(1, 'Minimum of at least 1 email')
          } : {
            contacts: yup.array()
            .of(
                yup.object().shape({
                    value: yup.string().test('phoneTest', (data) => {
                        return PhoneValidator.validate(data.value);
                    }, (element: any) => {
                        return !PhoneValidator.validate(element);
                    })
                    .required("Enter phone number"),
              })
            )
            .required('Must have phones')
            .min(1, 'Minimum of at least 1 phone'),
          });
		this.state = {};
	}


    renderFieldArray(form: FormikProps<IContactField>) {
        return (
            <FieldArray name={"contacts"} validateOnChange={true}>
                {CSSModules((fields: any) => {
                        return (
                            <React.Fragment>
                                <table>
                                    <thead>
                                        <tr>
                                            <th align="left">{this.props.type}</th>
                                            <th align="left">Primary</th>
                                            <th>
                                                <div styleName="add-button-container">
                                                    <div styleName="add-icon" onClick={() => {
                                                            fields.push({
                                                                value: "",
                                                                isPrimaryForMethodType: false,
                                                                parentEntityType: "Party",
                                                                contactMethodTypeCode: this.props.type
                                                            } as BaseContactMethodCDto);
                                                        }}>
                                                        <ButtonBase>
                                                            <div styleName="inner-icon">
                                                                <FontAwesomeIcon icon="plus" />
                                                            </div>
                                                        </ButtonBase>
                                                    </div>
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {form.values.contacts.map((dto: BaseContactMethodCDto, index: number) => {
                                            return (
                                                <React.Fragment key={`_contacts_"${dto.contactMethodId}_${index}`}>
                                                    <tr>
                                                        <td width="270px">
                                                            <Field 
                                                                label=" "
                                                                id={`contacts_"${dto.contactMethodId}_${index}`}
                                                                name={`contacts[${index}].value`}
                                                                as={TextField}
                                                                error={form.touched.contacts && form.touched.contacts[index]?.value && Boolean(getIn(form.errors.contacts?.[index], `value`))}
                                                                helperText={form.touched.contacts && form.touched.contacts[index]?.value && getIn(form.errors.contacts?.[index], `value`)}
                                                            />            
                                                        </td>
                                                        <td align="center" width="70px" style={{paddingTop: "17px"}}>
                                                            <Field 
                                                                type="checkbox"
                                                                id={`contactsradio_"${dto.contactMethodId}_${index}`}
                                                                name={`contacts[${index}].isPrimaryForMethodType`}
                                                                onChange={() => {
                                                                    for (let i = 0; i < form.values.contacts.length; i++) {
                                                                        form.setFieldValue(`contacts[${i}].isPrimaryForMethodType`, false);       
                                                                    }
                                                                    form.setFieldValue(`contacts[${index}].isPrimaryForMethodType`, true);
                                                                }}
                                                                as={Radio}
                                                            />
                                                        </td>
                                                        <td>
                                                            <div styleName="edit-icon" onClick={() => fields.remove(index)}>
                                                                <ButtonBase>
                                                                    <div styleName="inner-icon">
                                                                        <FontAwesomeIcon icon="trash" />
                                                                    </div>
                                                                </ButtonBase>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </React.Fragment>
                                            );
                                        })}
                                    </tbody>
                                </table>
                                <div style={{color: "red", paddingTop: "17px"}}>
                                    {typeof form.errors.contacts === 'string' ? <div>{form.errors.contacts}</div> : null}
                                </div>
                            </React.Fragment>
                        );
                    }, styles, { allowMultiple: true})}   
            </FieldArray>
        );
    }

	render() {
		return (
            <Dialog
                open={this.props.open}
                onClose={this.props.onCancel}
                maxWidth='sm'
            >
                <DialogTitle>
                    {this.props.type}
                </DialogTitle>
                <DialogContent>
                    <div styleName="modal">
                        <Formik<IContactField>
                            //classes={styles}
                            validationSchema={this.schema}
                            initialValues={this.multipleContactField}
                            validateOnChange={false}
                            onSubmit={(data, actions) => {
                                actions.setSubmitting(false);
                                if (data.contacts.some(x =>x.isPrimaryForMethodType)) {
                                    this.props.onSave(data.contacts);
                                    this.props.onCancel && this.props.onCancel();
                                }
                            }}>
                            {form => (
                                <form onSubmit={form.handleSubmit}>
                                    {this.renderFieldArray(form)}
                                    <div styleName="button-wrapper">
                                        {this.props.onCancel ?
                                        <Button variant="outlined" onClick={() => this.props.onCancel()}>Cancel</Button> : <div />}
                                        <Button variant="contained" type="submit"
                                            onClick={() => form.handleSubmit}>Save</Button>
                                    </div>
                                </form>
                            )}
                        </Formik>
                    </div>
                </DialogContent>
            </Dialog>
		);
	}
}

interface IContactField {
    contacts: BaseContactMethodCDto[];
}

interface Props {
    open: boolean;
    contacts: BaseContactMethodCDto[];
    type: "Email" | "Phone";
    onSave: (data: BaseContactMethodCDto[]) => void;
    onCancel: () => void;
}

interface State {}
