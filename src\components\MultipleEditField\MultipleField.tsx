import * as React from 'react';

import './styles.scss';
import { ButtonBase, TextField } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { FormikProps, getIn } from 'formik';
import { BaseAddressCDto, BaseContactMethodCDto, GetListAddressCDto, GetListContactMethodCDto } from 'redi-types';
import MultipleContactFieldModal from './MultipleContactFieldModal';
import MultipleAddressFieldModal from './MultipleAddressFieldModal';

export default class MultipleField<T extends 
{ contactMethods?: Array<BaseContactMethodCDto> } | 
{ addresses: GetListAddressCDto[]}> extends React.PureComponent<Props<T>, State> {
	constructor(props: Props<T>) {
		super(props);
		this.state = {
            primary: this.getPrimary(),
            open: false,
            loaded: false
        };

	}

    componentDidMount() {
        setTimeout(() => {
            if (this.state.primary) {
                this.props.form.setFieldValue(this.props.fieldName, this.state.primary);
            }
            this.setState({loaded: true});
        });
    }

    getPrimary() {
        if (this.props.data && this.props.data.length > 0) {
            switch (this.props.type) {
                case "Phone":
                case "Email":
                    const contacts = this.props.data as BaseContactMethodCDto[];
                    return contacts.find(x => x.isPrimaryForMethodType && x.contactMethodTypeCode === this.props.type)?.value ?? contacts[0]?.value;
                case "Address":
                    const addresses = this.props.data as BaseAddressCDto[];
                    return addresses.find(x => x.isPreferredAddress)?.fullAddress ?? addresses[0]?.fullAddress ?? "";
                default:
                    return "";
            }
        }
        return "";
    }

    renderMultipleField() {
        switch (this.props.type) {
            case "Phone":
            case "Email":
                const contacts = this.props.data as GetListContactMethodCDto[];
                return (
                    <MultipleContactFieldModal
                        type={this.props.type}
                        open={this.state.open}
                        contacts={contacts}
                        onSave={(data) => {
                            const value = data.find(x => x.isPrimaryForMethodType)?.value ?? "";
                            const list = [...contacts.filter(x => x.contactMethodTypeCode !== this.props.type), ...data];
                            this.props.form.setFieldValue(this.props.fieldName, value);
                            this.props.form.setFieldValue(this.props.name, list);
                            this.setState({primary: value});
                        }}
                        onCancel={() => this.setState({open: false})}
                    />
                );
            case "Address":
                const addresses = this.props.data as GetListAddressCDto[];
                return (
                    <MultipleAddressFieldModal
                        open={this.state.open}
                        addresses={addresses}
                        onSave={(data) => {
                            const primary = data.find(x => x.isPreferredAddress);
                            const value = (primary?.lines + " " + primary?.stateOrProvince + " " + primary?.postalCode + " " + primary?.country);
                            this.props.form.setFieldValue(this.props.fieldName, value);
                            this.props.form.setFieldValue(this.props.name, data);
                            this.setState({primary: value});
                        }}
                        onCancel={() => this.setState({open: false})}
                    />
                );
            default:
                return null;
        }
    }

	render() {
		return (
            <div styleName="multi-field-wrapper">
                <TextField
                    id={`multi${this.props.type}Field`}
                    name={this.props.fieldName}
                    label={this.props.label}
                    value={this.state.primary}
                    disabled={true}
                    error={this.props.error ?? (this.state.loaded && Boolean(getIn(this.props.form.errors, this.props.fieldName)))}
                    helperText={this.props.helperText ?? (this.state.loaded && getIn(this.props.form.errors, this.props.fieldName))}
                />
                {!this.props.hideEdit &&
                <div styleName="edit-icon" onClick={() => this.setState({open: true})}>
                    <ButtonBase>
                        <div styleName="inner-icon">
                            <FontAwesomeIcon icon="edit" />
                        </div>
                    </ButtonBase>
                </div>}
                {this.state.open && this.renderMultipleField()}
            </div>
		);
	}
}

interface Props<T> {
    label: string;
    name: string;
    fieldName: string;
    data: (GetListAddressCDto | BaseContactMethodCDto)[];
    form: FormikProps<T>;
    type: "Phone" | "Email" | "Address";
    hideEdit?: boolean;
    error?: boolean;
    helperText?: string | false;
}

interface State {
    primary: string;
    open: boolean;
    loaded: boolean;
}