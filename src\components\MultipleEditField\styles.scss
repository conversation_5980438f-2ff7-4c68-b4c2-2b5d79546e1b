@import "../../config/theme/vars.scss";

.modal {
    overflow: auto;
    padding-bottom: 60px;
    background-color: #fff;
    padding: 0px;
    max-width: 1100px;
    min-width: 400px;
}

.button-wrapper {
    flex:1;
    display: flex;
    justify-content: space-between;
    margin-top:25px;
    margin-bottom:15px;
    >div {
        &:hover {
            opacity:0.7;
        }
    }
}

.edit-icon {
    border-radius: 100%;
    width: 40px;
    height:40px;
    margin-left:5px;
    margin-top: 10px;
    display:flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    color: #00a0c8;
}

.add-icon {
    border-radius: 100%;
    border: 1px solid #ACC4CC;
    width: 40px;
    height:40px;
    display:flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.inner-icon {
    height: 40px;
    width: 40px;
    display:flex;
    align-items: center;
    justify-content: center;
}

.multi-field-wrapper {
    display:flex;
    >div:first-child {
        width: 100%;
    }
}

.add-button-container {
    display:flex;
    justify-content: center;
    align-items: center;
    align-content: center;
}

.row-item {
    display:flex;
    flex: 3;
    margin-right:15px;
    margin-top:15px;
}

.row-item-large {
    display:flex;
    flex: 7;
    margin-right:15px;
    margin-top:15px;
    width: 100%;
    >div {
        width: 100%;
    }
}

.address-container {
    display:flex;
    .row-item:last-child {
        margin-right:0px;
        >div {
            width: 100%;
        }
    }
}

.align-action-buttons {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

.icon-styling {
    top: 32px !important;
}