import { Field, FormikErrors, FormikTouched, getIn, FieldArray, FieldArrayRenderProps } from "formik";
import { ContactMethodDto } from "redi-types";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import './styles.scss';
import { Radio, TextField, Tooltip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { PartyTypeEnum } from "../../enum/PartyTypeEnum";
import { ContactMethodTypeEnum } from "../../enum/ContactMethodTypeEnum";

function addContact(fields: FieldArrayRenderProps, contactMethodType: ContactMethodTypeEnum, partType: PartyTypeEnum, count: number) {
    const dto = {
        contactMethodId: NIL_UUID,
        contactMethodTypeCode: contactMethodType,
        value: '',
        isPreferredContactMethod: false,
        isPrimaryForMethodType: false,
        parentEntityId: NIL_UUID,
        parentEntityType: partType,
        createdByName: "",
        contactMethodTypeLabel: "",
        sortOrder: count + 1
    };
    fields.push(dto);
}

function PartyContact(props: Props) {
    const { limit = 99, showDelete = true, touchedList, errorList, contacts, showPreferredContact, partType, contactMethodType, setFieldValue } = props;
    const buttonText = contactMethodType === ContactMethodTypeEnum.Email ? "Email" : "Phone Number";
    const showAdd = !contacts || contacts.filter(x => x.contactMethodTypeCode === contactMethodType).length < limit;
    return (
        <FieldArray name="contactMethods">
            {(fields: FieldArrayRenderProps) => (
                <div styleName="column card-fields-container" >
                    <div styleName="column">
                        <div styleName="row margin-bottom">
                            <div styleName="field-container size-4 field-container-title center-justify center-align"></div>
                            <div styleName="field-container size-1 field-container-title center-justify center-align">Primary</div>
                            {
                                props.showPreferredContact == true &&
                                <div styleName="field-container size-1 field-container-title center-justify center-align">Preferred</div>
                            }
                            {showDelete ? <div styleName="field-container size-1"></div> : null}
                        </div>
                        {
                            contacts &&
                            contacts.map((dto, index) => {
                                if (dto.contactMethodTypeCode !== contactMethodType) {
                                    return null;
                                } else {
                                    return (
                                        <div styleName="row phone-row-container" key={`${contactMethodType}_contact_${index}`}>
                                            <div styleName="field-container size-4">
                                                <Field
                                                    variant="outlined"
                                                    id={`contacts_${index}_value`}
                                                    name={`contactMethods[${index}].value`}
                                                    label={contactMethodType}
                                                    as={TextField}
                                                    error={touchedList && touchedList[index]?.value && Boolean(getIn(errorList?.[index], `value`))}
                                                    helperText={touchedList && touchedList[index]?.value && getIn(errorList?.[index], `value`)}
                                                />
                                            </div>
                                            <div styleName="column field-container size-1 center-justify center-align">
                                                <div>
                                                    <Field
                                                        type="checkbox"
                                                        id={`contactsradio_${index}`}
                                                        name={`contactMethods[${index}].isPrimaryForMethodType`}
                                                        onChange={() => {
                                                            for (let ii = 0, ilen = contacts?.length; ii < ilen; ii++) {
                                                                if (contacts[ii].contactMethodTypeCode === contactMethodType) {
                                                                    setFieldValue(`contactMethods[${ii}].isPrimaryForMethodType`, false);
                                                                }
                                                            }
                                                            setFieldValue(`contactMethods[${index}].isPrimaryForMethodType`, true);
                                                        }}
                                                        as={Radio}
                                                    />
                                                </div>
                                            </div>
                                            {
                                                showPreferredContact ?
                                                    <div styleName="column field-container size-1 center-justify center-align">
                                                        <div>
                                                            <Field
                                                                type="checkbox"
                                                                id={`contactsradio_${index}`}
                                                                name={`contactMethods[${index}].isPreferredContactMethod`}
                                                                onChange={() => {
                                                                    for (let ii = 0, ilen = contacts?.length; ii < ilen; ii++) {
                                                                        setFieldValue(`contactMethods[${ii}].isPreferredContactMethod`, false);
                                                                    }
                                                                    setFieldValue(`contactMethods[${index}].isPreferredContactMethod`, true);
                                                                }}
                                                                as={Radio}
                                                            />
                                                        </div>
                                                    </div> : null
                                            }
                                            {showDelete ?
                                                <div styleName="column field-container size-1 center-justify center-align">
                                                    <Tooltip title="Remove phone number" placement="bottom">
                                                        <div styleName="column field-container-action-button center-justify center-align" onClick={() => fields.remove(index)}>
                                                            <FontAwesomeIcon icon="trash" />
                                                        </div>
                                                    </Tooltip>
                                                </div> : null}
                                        </div>
                                    );
                                }
                            })}
                    </div>
                    {(showAdd) ?
                        <div styleName="row flex-end center-align">
                            <Tooltip title={`Add a new  ${buttonText}`} placement="bottom">
                                <div styleName="action-button" onClick={() => addContact(fields, contactMethodType, partType, contacts?.length || 0)}>
                                    <FontAwesomeIcon size="sm" icon="circle-plus" />
                                    <div>Add {buttonText}</div>
                                </div>
                            </Tooltip>
                        </div> : null}
                </div>
            )}
        </FieldArray>
    )
}

export default PartyContact;

interface Props {
    touchedList?: FormikTouched<ContactMethodDto>[] | undefined;
    errorList: string | string[] | FormikErrors<ContactMethodDto>[] | undefined;
    contacts: ContactMethodDto[] | undefined;
    showPreferredContact: boolean;
    partType: PartyTypeEnum;
    contactMethodType: ContactMethodTypeEnum;
    limit?: number;
    showDelete?: boolean;
    setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void;
}