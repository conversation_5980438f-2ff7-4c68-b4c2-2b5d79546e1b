import { useRef, useState } from "react";
import { RediAutocompleteField } from "redi-formik-material";
import { BaseListCDto, GetExtendedPartyCDto, InsurancePartyCDto } from "redi-types";
import PartyService from "../../services/party";
import { HttpResult } from "redi-http";
import DraggableDialog from "../DraggableDialog/DraggableDialog";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import './styles.scss';
import { PartyTypeEnum } from "../../enum/PartyTypeEnum";
import ContractorForm from "../../pages/Contractor/ContractorForm/ContractorForm";
import { InputBaseProps } from "@mui/material";
import InsuranceForm from "../../pages/Insurance/InsuranceForm/InsuranceForm";
import DriverForm from "../../pages/Driver/DriverForm/DriverForm";

export type ManagementItemType = 'Insurance' | 'Party';
export type ManagementItemDataDto = InsurancePartyCDto | GetExtendedPartyCDto;

function RediAutocompleteParty(props: Props) {

  const { autoSelectOnAdd = true, minLength = 2, displayName = "name" } = props;
  const initialValuesRef = useRef(PartyService.getDefaultValues(props.partyType, props.partyRoleTypeCode));
  const initialValues = initialValuesRef.current;
  const [selectedListing, setSelectedListing] = useState<ManagementItemDataDto>(initialValues);
  const [isAddPartyDialogOpen, setIsAddPartyDialogOpen] = useState(false);

  function openAddPartyDialog() {
    setIsAddPartyDialogOpen(true);
  }

  const closeAddPartyDialog = () => {
    setIsAddPartyDialogOpen(false);
  };

  const onAddPartyDialogSave = async (value: ManagementItemDataDto ) => {
    autoSelectOnAdd && props.onChange(value);
    closeAddPartyDialog();
  };

  const onChange = (val: string, obj: BaseListCDto) => {
    setSelectedListing(obj);
    props.onChange(obj);
  };

  function callService(query: string) {
    return new Promise<HttpResult<BaseListCDto[]>>((resolve, reject) => {

      var promise = PartyService.ListByRelationship(props.partyRoleTypeCode
        , props.partyRelationshipTypeCode
        , undefined
        , query
        , props.relatedPartyId
        , props.includeOnlyActive
        , props.returnContact
        , props.returnAddress
        , props.statusCode
        , props.organisationTypeCode);
        promise.then((data) => {
          if (!data.error && data.data) {
            resolve({ data: data.data.list });
          } else {
            reject('No data');
          }
        })
        .catch(error => error);
    }
    );
  }
  return (
    <>
      <div styleName="title-row">
        <div styleName="search-flex">
          <div styleName="search-field">
            <RediAutocompleteField
              id={props.name}
              name={props.name}
              label={props.label}
              initialValue={props.initialValue}
              minLength={minLength}
              autoSelectFirst
              callService={callService}
              fieldValue="partyId"
              displayValue={displayName}
              value={props.partyId}
              onChange={onChange}
              onBlur={props.onBlur}
              error={props.error}
              helperText={props.helperText}
              size={props.size}
            />
          </div>
        </div>
        {props.showAddNewButton ?
        <div styleName={`action-button ${props.disableAddButton ? "disabled" : ""} `} onClick={openAddPartyDialog}>
          <FontAwesomeIcon icon="circle-plus" />
          <div>Add New {props.buttonLabel}</div>
        </div> : undefined}
        <DraggableDialog title={`Add New ${props.buttonLabel}`} isOpen={isAddPartyDialogOpen} onCancel={closeAddPartyDialog}>
          {props.partyRoleTypeCode === "Contractor" &&
          <ContractorForm
              initialValues={initialValues}
              onCancel={closeAddPartyDialog}
              onSave={onAddPartyDialogSave}
              showPreferredContact={false}
              showHeader={false}
              relatedPartyId={props.relatedPartyId}
              showPreferredAddress={false}
          />
          }
          {props.partyRoleTypeCode === "Insurance" &&
            <InsuranceForm
              initialValues={initialValues}
              onCancel={closeAddPartyDialog}
              onSave={onAddPartyDialogSave}
              showHeader={false}
              relatedPartyId={props.relatedPartyId}
              itemType={"Insurance"}
            />
          }
          {props.partyRoleTypeCode === "Driver" &&
            <DriverForm
              initialValues={initialValues}
              onCancel={closeAddPartyDialog}
              onSave={onAddPartyDialogSave}
              showHeader={false}
              showAddTruck={false}
            />
          }
        </DraggableDialog>
      </div>
    </>
  );
}

export default RediAutocompleteParty;

interface Props {
  returnAddress?: boolean;
  returnContact?: boolean;
  includeOnlyActive?: boolean;
  label?: string;
  minLength?: number;
  buttonLabel?: string;
  onChange: (party: ManagementItemDataDto) => void;
  partyId?: string;
  partyRoleTypeCode: string;
  //The Party Id to pair the relationship with the passed in Party Id. DefaultOrganisationPartyId from Common.Settings by default
  relatedPartyId?: string;
  //The Role Type Code to pair the relationship with the passed in Party.
  relatedPartyRoleTypeCode?: string;
  //Filter the Party dropdown list by the Relationship Types
  partyRelationshipTypeCode: string;
  //Status Code of the list item(party) 
  statusCode?: string;
  //Filter organisations by their type
  organisationTypeCode?: string;
  showEditButton?: boolean;
  showAddNewButton?: boolean;
  disableAddButton?: boolean;
  //When adding new record have the record selected
  autoSelectOnAdd?: boolean;
  partyType: PartyTypeEnum;
  initialValue?: string;
  //Formik field error
  error?: boolean;
  //Formik Helper text
  helperText?: React.ReactNode;
  //Formik name
  name?: string;
  onBlur?: InputBaseProps['onBlur'];
  size?: "small" | "medium" | undefined;
  //Override the display name
  displayName?: string;
}