import * as React from 'react';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import CircularProgress from '@mui/material/CircularProgress';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';
import { HttpPromise } from 'redi-http';
import { useEffect, useRef, useState } from 'react';
import { ListResponseDto } from 'redi-types';
import { Checkbox, Typography } from '@mui/material';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import './styles.scss';

type TValue = { [x: string]: any, isAdded?: boolean };

function RediAutocompleteMultiselectField(props: AutocompleteMultiselectProps) {
    const { id, name, error, disabled, helperText, label, placeholder, variant, value, fieldDisplayText, fieldValue, minLength = 0, textFieldProps, callService, onChange, onTagClick, onDeleteTag } = props;
    const [ open, setOpen ] = useState(false);
    const [ selected, setSelected ] = useState<TValue | null>();
    const [ options, setOptions ] = useState<readonly TValue[]>([]);
    const [ isLoading, setIsLoading ] = useState(false);
    const [ inputValue, setInputValue ] = useState("");
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const promiseRef = useRef<HttpPromise<ListResponseDto<any>>>();

    const isMinLengthMet = minLength === (inputValue?.length ?? 0);

    useEffect(() => {
        if (!open) {
            setOptions([]);
        } else if (isMinLengthMet) {
            search();
        }
    }, [open]);

    useEffect(() => {
        return () => {
            if (promiseRef.current && promiseRef.current.cancel) {
                promiseRef.current.cancel();
            }
        }
    }, []);

    const search = (query?: string) => {
        !isLoading && setIsLoading(true);
        !open && setOpen(true);
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        timeoutRef.current = setTimeout(async () => {
            try {
                setInputValue(query ?? "");
                if (promiseRef.current && promiseRef.current.cancel) {
                    promiseRef.current.cancel();
                }
                promiseRef.current = callService(query);
                const response = await promiseRef.current;
                if (!open) {
                    setIsLoading(false);
                    return;
                }
                if (!response.error && response.data && response.data.list) {
                    setOptions(response.data.list);
                }
                setIsLoading(false);
            } catch (error) {
            } finally {
            }
        }, 400);

        return () => {
            if (promiseRef.current && promiseRef.current.cancel) {
                promiseRef.current.cancel();
            }
        }
    };

    const handleOnInputChange = (event: React.SyntheticEvent<Element, Event>, value: string) => {
        setInputValue(value ?? "");
        ((minLength === (value?.length ?? 0)) || open) && search(value);
    };

    return (
        <div>
        <Autocomplete
            id={"asynchronous_" + id}
            multiple={true}
            disabled={disabled}
            renderTags={(values, props) => {
                return (
                    <Typography className="autocomplete-label" style={{paddingLeft: "15px"}}>Selected ({values?.length || 0})</Typography>
                );
            }}
            sx={{ width: "100%" }}
            open={open}
            onOpen={() => {
                isMinLengthMet && setOpen(true);
            }}
            onClose={() => {
                setOpen(false);
            }}
            onInputChange={handleOnInputChange}
            isOptionEqualToValue={(option, value) => option[fieldValue] === value[fieldValue]}
            getOptionLabel={(option) => option[fieldDisplayText] ?? ""}
            options={options}
            value={value}
            onChange={(e: React.ChangeEvent<{}>, val) => {
                const copied = val.map((x) => ({ [fieldValue]: x[fieldValue], [fieldDisplayText]: x[fieldDisplayText] })) as any[];
                onChange(e, copied);
            }}
            loading={isLoading}
            renderInput={(params) => (
                <TextField
                    {...params}
                    {...textFieldProps}
                    name={name}
                    label={label}
                    placeholder={placeholder}
                    variant={variant}
                    helperText={helperText}
                    error={error}
                    InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                            <React.Fragment>
                                {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                            </React.Fragment>
                        ),
                    }}
                />
            )}
            disableCloseOnSelect={true}
            renderOption={(props, option, { inputValue }) => {
                const matches = match(option[fieldDisplayText], inputValue, { insideWords: true });
                const parts = parse(option[fieldDisplayText], matches);
                const checked = value ? value.some(x => x[fieldValue] === option[fieldValue]) : false;
                return (
                    <li {...props} key={"_option_multi_key_" + props.id  + "_" + option[fieldValue]}>
                        <div>
                            <Checkbox 
                                checked={checked}
                                value={checked}
                            />
                            {parts.map((part:any, index:number) => (
                                <span
                                    key={index}
                                    style={{
                                        fontWeight: part.highlight ? 700 : 400,
                                    }}
                                >
                                    {part.text}
                                </span>
                            ))}
                        </div>
                    </li>
                );
            }}
        />
        {value ? 
            <div>
              <div styleName="flex flex-wrap">
                  {value.map((item) => {
                      return (
                          <div styleName={(selected?.[fieldValue] === item[fieldValue] ? "active " : "") + ("field-tag")} 
                              key={`_filter_selected_${item[fieldValue]}`}>
                              <div onClick={() => { 
                                if (disabled) { return; }
                                let selected;
                                if (selected?.[fieldValue] === item[fieldValue]) {
                                  setSelected(null);
                                  selected = null;
                                } else {
                                  setSelected(item);
                                  selected = item;
                                }
                                onTagClick && onTagClick(selected);
                              }}>{item[fieldDisplayText]}</div>
                              <div styleName="delete-tag-icon" onClick={(e) => {
                                      e.preventDefault();
                                      if (disabled) { return; }
                                      const copied = [...value].map((x) => ({ [fieldValue]: x[fieldValue], [fieldDisplayText]: x[fieldDisplayText] })) as any[];
                                      const index = copied.findIndex(x => x[fieldValue] === item[fieldValue]);
                                      let removedItem;
                                      if (index > -1) {
                                          removedItem = copied[index];
                                          onDeleteTag && onDeleteTag(removedItem);
                                          copied.splice(index, 1);
                                      }
                                      onChange(e, copied, removedItem);
                                  }}>
                                  <FontAwesomeIcon icon={["fad","times"]} size="1x" />
                              </div>
      
                          </div>
                      );
                    })}
              </div>
            </div> : null}
            </div>
    );
}

export default RediAutocompleteMultiselectField;

export interface AutocompleteMultiselectProps {
    id: string;
    /** Field name */
    name: string;
    /** Disable field */
    disabled?: boolean;
    /** Label for the Text Input field */
    label?: string;
    /** Placeholder for the Text Input field */
    placeholder?: string;
    /** Variant for the Text Input field */
    variant?: "filled" | "outlined" | "standard";
    /** Helper text context */
    helperText?: React.ReactNode;
    /** If true, the label is displayed in an error state.  */
    error?: boolean | undefined;
    /** Actual value stored for the field */
    value: TValue[] | [];
    /** Call to backend to query results */
    callService: (query?: string) => HttpPromise<ListResponseDto<any>>;
    /** On change callback when dropdown field selected */
    onChange: (e: React.ChangeEvent<{}>, val:any[], removedItem?: any) => void;
    /** Value to be returned in the object array */
    fieldValue: string;
    /** Value to be displayed in the object array */
    fieldDisplayText: string;
    /** Specifies the minimum length of text before autocomplete will make suggestions */
    minLength?: number;
    /** Text field props to spread */
    textFieldProps?: TextFieldProps;
    /** On tag click callback */
    onTagClick?: (val: any) => void;
    /** On tag delete callback */
    onDeleteTag?: (val: any) => void;
}