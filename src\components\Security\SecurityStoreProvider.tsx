import React, { useRef } from 'react';
import { SecurityStoreContext } from './SecurityStoreContext';
import createSecurityStore, { SecurityManagerProps, SecurityStore } from './createSecurityStore';

function SecurityStoreProvider(props: Props) {
    const { children, options } = props;
    const storeRef = useRef<SecurityStore>()
    if (!storeRef.current) {
      storeRef.current = createSecurityStore(options)
    }

    return (
        <SecurityStoreContext.Provider value={storeRef.current}>
            {children}
        </SecurityStoreContext.Provider>
    );
}

export default SecurityStoreProvider;

interface Props  {
    children: React.ReactNode;
    options?: Partial<SecurityManagerProps>;
}