import { UserDto } from "redi-types";
import { createStore } from "zustand";

export type SecurityStore = ReturnType<typeof createSecurityStore>

function createSecurityStore(initProps?: Partial<SecurityManagerProps>) {

    const DEFAULT_PROPS: SecurityManagerProps = {
        user: null,
        isLoggedIn: false,
        claims: [],
        defaultClaims: [],
        tokenKey: "token-key",
        tokenExpDateKey:"tokenExpDateKey"
    };

    const store = createStore<SecurityManagerState>((set, get) => ({
        ...DEFAULT_PROPS,
        ...initProps,
        setUser: (user:UserDto) => set({user: user, isLoggedIn: !!user}),
        setClaims: (claims:string[]) => set({claims: claims}),
        getClaims: () => {
            const claims = Array.from(new Set([...get().claims, ...get().defaultClaims]));
            return claims;
        }
    }));

    return store;
}

export interface SecurityManagerProps {
    user: UserDto | null;
    isLoggedIn: boolean;
    claims: string[];
    defaultClaims: string[];
    tokenKey: string;
    tokenExpDateKey: string;
}

export interface SecurityManagerState extends SecurityManagerProps {
    setUser: (user: any) => void,
    setClaims: (claims: string[]) => void,
    getClaims: () => string[];
}

export default createSecurityStore;