import { useContext } from "react";
import { useStore } from "zustand";
import { SecurityStoreContext } from "./SecurityStoreContext";
import { SecurityManagerState } from "./createSecurityStore";

function useSecurityStore<T = SecurityManagerState>(selector: (state: SecurityManagerState) => T = (state) => state as T, equalityFn?: (a: T, b: T) => boolean) {
    const store = useContext(SecurityStoreContext);
    if (!store) throw new Error('Missing SecurityStoreProvider at the root')
    return useStore(store, selector, equalityFn);
}

export default useSecurityStore;