import { Dialog, DialogContent, DialogTitle, FormControlLabel, Input, Paper, PaperProps, Radio, RadioGroup, TextField, Button, Checkbox, Rating } from '@mui/material';
import { InputTypeCodeEnum } from '../../enum/InputTypeCodeEnum';
import { AvailableValuesWithStats, FilterOptions } from 'redi-types';
import { useEffect, useState } from 'react';
import useFilterSelector from '../../hooks/useFilter';
import { TimePicker } from '@mui/x-date-pickers';
import './styles.scss';

interface Props {
  inputTypeCode: InputTypeCodeEnum
  onChange: (value: any) => void;
  valueArray: AvailableValuesWithStats[];
  many: boolean
  attributeCode?: string;
}

function InputFactory(props: Props) {

  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const { filterOptions, handleSetFilter } = useFilterSelector();


  useEffect(() => {
    if (filterOptions?.filterInclude && props.attributeCode) {
      setSelectedValues(filterOptions.filterInclude[props.attributeCode] || []);
    } else {
      setSelectedValues([]);
    }
  }, [filterOptions]);


  function onChangeHandler(value: string) {
    if (props.many) {
      if (selectedValues.includes(value)) {
        //Unselect one of the selected values
        setSelectedValues(selectedValues.filter((item) => item !== value));
        props.onChange(selectedValues.filter((item) => item !== value));
      } else {
        //add value to selected values
        setSelectedValues([...selectedValues, value]);
        props.onChange([...selectedValues, value]);
      }
    } else {
      //set selected value to the value
      setSelectedValues([value]);
      props.onChange([value]);
    }
  }


  switch (props.inputTypeCode) {
    case InputTypeCodeEnum.Address:
      return (
        <TextField onChange={(e) => onChangeHandler(e.target.value)} />
      );
    case InputTypeCodeEnum.Checkbox:
      return (
        <>
          {props.valueArray.map((value, index) => (
            <div key={index} styleName='row'>
              <FormControlLabel

                control={<Checkbox checked={selectedValues.includes(value.value.toString())} />}
                label={value.value.toString()}
                onChange={() => onChangeHandler(value.value.toString())}
              />
              <p styleName='count'>({value.count})</p>
            </div>
          ))}
        </>
      );


    case InputTypeCodeEnum.Date:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.DateRange:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.Decimal:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.Distance:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.Dropdown:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.Integer:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.Map:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.MultiSelect:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.NumericRange:
      return (
        <div>
          <TextField
            label="Min Value"
            type="number"
            value={selectedValues[0]}
            onChange={(e) => onChangeHandler(e.target.value)}
          />
          <TextField
            label="Max Value"
            type="number"
            value={selectedValues[1]}
            onChange={(e) => onChangeHandler(e.target.value)}
          />
        </div>
      );
    case InputTypeCodeEnum.PriceRange:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.RadioButton:
      return (
        <>
          {props.valueArray.map((value, index) => (
            <FormControlLabel
              key={index}
              control={<Radio checked={selectedValues.includes(value.value.toString())} />}
              label={value.value.toString()}
              onChange={() => onChangeHandler(value.value.toString())}
            />
          ))}
        </>
      );
    case InputTypeCodeEnum.StarRating:
      return (
        <>
          {props.valueArray.map((value, index) => (
            <Rating
              key={index}
              value={Number(value.value)}
              onChange={(event, newValue) => onChangeHandler(newValue?.toString() || '')}
            />))}
        </>);
    case InputTypeCodeEnum.Suburb:
      return (
        <TextField />
      );
    case InputTypeCodeEnum.Text:
      return (
        <>
          {props.valueArray.map((value, index) => (
            <TextField key={index} value={value.value} onChange={(e) => onChangeHandler(e.target.value)} />
          ))}
        </>
      );
    case InputTypeCodeEnum.TextArea:
      return (
        <>
          {props.valueArray.map((value, index) => (
            <TextField multiline key={index} value={value.value} onChange={(e) => onChangeHandler(e.target.value)} />
          ))}
        </>
      );
    case InputTypeCodeEnum.Time:
      return (
        <>
          {/* {props.valueArray.map((value, index) => (
            <TimePicker
              key={index}
              value={value.value}
              onChange={(e) => onChangeHandler(e.target.value)}
              renderInput={(props) => <TextField {...props} />}
            />
          ))} */}
        </>
      );
    default:
      return (
        <>
          {props.valueArray.map((value, index) => (
            <FormControlLabel
              key={index}
              control={<Checkbox checked={selectedValues.includes(value.value.toString())} />}
              label={value.value.toString()}
              onChange={() => onChangeHandler(value.value.toString())}
            />
          ))}
        </>

      );
  }
}

export default InputFactory;
