/*

Add icons here:
    import { faGears } from "@fortawesome/pro-duotone-svg-icons";    
    library.add(faTimes)

Using import:
    import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

Using solid icons:
    <FontAwesomeIcon icon="times" />

Using pro light icons:
    <FontAwesomeIcon icon={['fal', 'gears']} />

Using pro regular icons:
    <FontAwesomeIcon icon={['far', 'gears']} />

Using pro thin icons:
    <FontAwesomeIcon icon={['fat', 'gears']} />

Using pro duotone icons:
    <FontAwesomeIcon icon={['fad', 'gears']} />

Setting duotone styles in CSS:
    --fa-primary-color: red;
    --fa-secondary-color: orange;
    --fa-primary-opacity: 0.8;
    --fa-secondary-opacity: 0.5
 
*/

import { IconDefinition, library } from "@fortawesome/fontawesome-svg-core";
import {
  faArrowAltCircleLeft,
  faCamera,
  faArrowCircleLeft,
  faArrowCircleRight,
  faBolt,
  faEngine,
  faFilter,
  faList,
  faPaintBrush,
  faSearch,
  faX,
  faHeart,
  faHashtag,
  faDatabase,
  faArrowDownArrowUp,
  faTruckFront,
  faFileContract
} from "@fortawesome/pro-duotone-svg-icons";
// import { faCamera } from "@fortawesome/pro-light-svg-icons";
import {
  faArrowAltCircleRight,
  faCalendarLines,
  faCog,
  faFileLines,
  faFilePen,
  faFolder as faFolderRegular,
  faUser,
  faUserBountyHunter,
  faUsers,
} from "@fortawesome/pro-regular-svg-icons";
import {
  faCheck,
  faCheckSquare,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faCirclePlus,
  faClose,
  faCopy,
  faEllipsisV,
  faFile,
  faFolder,
  faFolderOpen,
  faHome,
  faKey,
  faArrowLeft,
  faArrowRight,
  faLock,
  faMinusSquare,
  faNote,
  faPaste,
  faPenToSquare,
  faPlusSquare,
  faRotateRight,
  faScissors,
  faSquare,
  faTrash,
  faTriangleExclamation,
  faUserGear,
  faUserLock,
  faUserPen,
  faUserUnlock,
  faMagnifyingGlass,
  faCircleInfo
} from "@fortawesome/pro-solid-svg-icons";

const exportedIcons: IconDefinition[] = [
  // Add any icons that are used in exported components here (ie. SideMenu)
];

library.add(
  ...exportedIcons,
  /* Local icons (demo) */
  // Regular
  faFileContract,
  faTruckFront,
  faUser,
  faFilePen,
  faFileLines,
  faCalendarLines,
  faFolderRegular,
  faUserBountyHunter,
  faCog,
  faClose,
  faHome,
  faUsers,
  faChevronLeft,
  faChevronRight,
  faRotateRight,
  faCirclePlus,
  faScissors,
  faCopy,
  faPaste,
  // Solid
  faEllipsisV,
  faCheckSquare,
  faSquare,
  faChevronDown,
  faPlusSquare,
  faMinusSquare,
  faFolder,
  faFolderOpen,
  faFile,
  faUserGear,
  faTriangleExclamation,
  faKey,
  faUserLock,
  faUserUnlock,
  faLock,
  faNote,
  faTrash,
  faPenToSquare,
  faCheck,
  faUserPen,
  faList,
  faPaintBrush,
  faEngine,
  faBolt,
  faSearch,
  faX,
  faFilter,
  faArrowLeft,
  faArrowRight,
  faCamera,
  faHeart,
  faHashtag,
  faDatabase,
  faMagnifyingGlass,
  faArrowDownArrowUp,
  faCircleInfo
);

export default exportedIcons; // Export to Container Micro UI
