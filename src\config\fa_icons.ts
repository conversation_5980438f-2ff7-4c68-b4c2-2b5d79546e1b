/*

Add icons here:
    import { faGears } from "@fortawesome/pro-duotone-svg-icons";    
    library.add(faTimes)

Using import:
    import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

Using solid icons:
    <FontAwesomeIcon icon="times" />

Using pro light icons:
    <FontAwesomeIcon icon={['fal', 'gears']} />

Using pro regular icons:
    <FontAwesomeIcon icon={['far', 'gears']} />

Using pro thin icons:
    <FontAwesomeIcon icon={['fat', 'gears']} />

Using pro duotone icons:
    <FontAwesomeIcon icon={['fad', 'gears']} />

Setting duotone styles in CSS:
    --fa-primary-color: red;
    --fa-secondary-color: orange;
    --fa-primary-opacity: 0.8;
    --fa-secondary-opacity: 0.5
 
*/

import { library } from "@fortawesome/fontawesome-svg-core";
// import { } from "@fortawesome/pro-regular-svg-icons";
// import { } from "@fortawesome/pro-solid-svg-icons";
import { faAddressCard, faGear, faHome } from "@fortawesome/pro-duotone-svg-icons";

/* Import each Micro UI's exported icons here */ 
import sideMenuIcons from 'sidemenu/icons';
import userIcons from 'usercomponents/icons';
import crmIcons from 'crmcomponents/icons';
import jobIcons from 'jobcomponents/icons';
import listingmanagementIcons from 'listingmanagementcomponents/icons';
// import formIcons from 'formcomponents/icons';

library.add(
  ...sideMenuIcons,
  ...userIcons,
  ...crmIcons,
  ...jobIcons,
  ...listingmanagementIcons,
  // ...formIcons,
  // Local Routes
  // Regular
  // Solid
  // Duotone
  faHome,
  faAddressCard,
  faGear
);