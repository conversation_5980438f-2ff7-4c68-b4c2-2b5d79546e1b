/*

Add icons here:
    import { faGears } from "@fortawesome/pro-duotone-svg-icons";    
    library.add(faTimes)

Using import:
    import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

Using solid icons:
    <FontAwesomeIcon icon="times" />

Using pro light icons:
    <FontAwesomeIcon icon={['fal', 'gears']} />

Using pro regular icons:
    <FontAwesomeIcon icon={['far', 'gears']} />

Using pro thin icons:
    <FontAwesomeIcon icon={['fat', 'gears']} />

Using pro duotone icons:
    <FontAwesomeIcon icon={['fad', 'gears']} />

Setting duotone styles in CSS:
    --fa-primary-color: red;
    --fa-secondary-color: orange;
    --fa-primary-opacity: 0.8;
    --fa-secondary-opacity: 0.5
 
*/
import { faUser } from "@fortawesome/pro-regular-svg-icons";
import { IconDefinition, library } from "@fortawesome/fontawesome-svg-core";
import { faBuildingUser, faAddressBook, faBuilding, faPeopleGroup } from "@fortawesome/pro-duotone-svg-icons";
import { faCheck, faChevronDown, faCirclePlus, faClose, faCopy, faEllipsisV, faEnvelope, faFileLines, faLock, faPaste, faPenToSquare, faPhone, faPlus, faRotateRight, faScissors, faTrash, faTriangleExclamation, faUserPen } from "@fortawesome/pro-solid-svg-icons";

const exportedIcons: IconDefinition[] = [
  // Add any icons that are used in exported components here.
  // Regular
  faUser,
  faClose,
  faCirclePlus,
  faRotateRight,
  faPlus,
  faTrash,
  faScissors,
  faCopy,
  faPaste,
  // Solid
  faEllipsisV,
  faFileLines,
  faPhone,
  faEnvelope,
  faChevronDown,
  faUserPen,
  faTriangleExclamation,
  faLock,
  faPenToSquare,
  faCheck,
  // Duotone
  faBuilding,
  faPeopleGroup,
  faAddressBook,
  faBuildingUser,
];

library.add(
  ...exportedIcons,
  /* Local icons (demo) */
  // Regular
  // Solid
  // Duotone
);

export default exportedIcons; //Export to Container Micro UI