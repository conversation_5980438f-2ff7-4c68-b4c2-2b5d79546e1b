import { createBrowserRouter } from "react-router-dom";

import ErrorPage from '../pages/Error/Error';
import Shell from '../startup/Shell';
import userRoutes from 'usercomponents/routes';
import crmRoutes from 'crmcomponents/routes';
import loginRoutes from 'logincomponents/routes';
import jobRoutes from 'jobcomponents/routes';
import listingmanagementRoutes from 'listingmanagementcomponents/routes';
// import formRoutes from 'formcomponents/routes';
import withAsyncLoad from "../components/HOC/withAsyncLoad";
import { MenuItem } from "redi-types";

/*
 * React-Router Tutorial: https://reactrouter.com/en/main/start/tutorial
 */


const Settings = withAsyncLoad(() => import("../pages/Settings/Settings"));

const routes: MenuItem[] = [
  /* Add local application page routes here (Not exported) */
  ...jobRoutes,
  ...crmRoutes,
  ...userRoutes,
  ...listingmanagementRoutes,
  // ...formRoutes,
  {
    path: "Settings",
    name: "Settings",
    element: <Settings />,
    icon: ["fad", "gear"]
  },
];

const router = createBrowserRouter([
    {
        path: "*",
        element: <Shell routes={routes} />,
        errorElement: <ErrorPage />,
        children: routes
    },
    ...loginRoutes
], {});

export default router;