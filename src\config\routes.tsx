import withAsyncLoad from '../components/HOC/withAsyncLoad';

// const DriverList = withAsyncLoad(() => import("../pages/Driver/DriverList/DriverList"));
// const Driver = withAsyncLoad(() => import("../pages/Driver/Driver/Driver"));
const ContractorList = withAsyncLoad(() => import("../pages/Contractor/ContractorList/ContractorList"));
const Contractor = withAsyncLoad(() => import("../pages/Contractor/Contractor/Contractor"));
const InsuranceList = withAsyncLoad(() => import('../pages/Insurance/InsuranceList/InsuranceList'));

/* Routes in this file are exported to the host-container */

const routes = [
    // {
    //     path: "Drivers",
    //     name: "Drivers",
    //     element: <DriverList itemType='Person' />,
    //     icon: ["fad", "fa-building-user"]
    // },
    // {
    //     path: "Driver/:id",
    //     name: "Driver Detail",
    //     element: <Driver />,
    //     excludeFromMenu: true
    // },
    {
        path: "Drivers",
        name: "Drivers",
        element: <ContractorList />,
        icon: ["far", "user"]
    },
    {
        path: "Driver/:id",
        name: "Driver Detail",
        element: <Contractor />,
        excludeFromMenu: true
    },
    {
        path: "InsuranceManagement",
        name: "Insurance List",
        element: <InsuranceList itemType={'Insurance'} />,
        excludeFromMenu: true
    },
    
];

export default routes;
