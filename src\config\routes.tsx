import { MenuItem } from "redi-types";
import withAsyncLoad from "../components/HOC/withAsyncLoad";

//const UserManagement = withAsyncLoad(() => import("../pages/UserManagement/UserManagement"));

/* Routes in this file are exported to the host-container */
const TruckList = withAsyncLoad(() => import("../pages/Truck/TruckList/TruckList"));
const Truck = withAsyncLoad(() => import("../pages/Truck/Truck"));
const YardList = withAsyncLoad(() => import("../pages/Yard/YardList/YardList"));
const Yard = withAsyncLoad(() => import("../pages/Yard/Yard"));

const routes: MenuItem[] = [
  {
    path: "Trucks",
    name: "Trucks",
    element: <TruckList />,
    icon: ["fad", "truck-front"]
},
{
    path: "Truck/:id",
    name: "Truck",
    element: <Truck />,
    excludeFromMenu: true
},
{
    path: "YardLog",
    name: "Yard Log",
    element: <YardList />,
    icon: ["fad", "file-contract"]
},
{
    path: "Yard/:id",
    name: "Yard",
    element: <Yard />,
    excludeFromMenu: true
}
  // {
  //   path: "ManageListingMedia",
  //   name: "ManageListingMedia",
  //   element: <ManageListingMedia />,
  //   icon: ["fas", "list"],
  // },
];

export default routes;
