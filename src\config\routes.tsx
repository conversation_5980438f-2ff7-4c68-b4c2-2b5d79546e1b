import withAsyncLoad from '../components/HOC/withAsyncLoad';

const UserManagement = withAsyncLoad(() => import("../pages/UserManagement/UserManagement"));
const User = withAsyncLoad(() => import("../pages/UserManagement/User/User"));

/* Routes in this file are exported to the host-container */

const routes = [
    {
      path: "UserManagement",
      name: "User Management",
      element: <UserManagement />,
      icon: ["far", "users"]
    },
    {
      path: "UserManagement/:id",
      name: "User",
      element: <User />,
      excludeFromMenu: true
    }
];

export default routes;