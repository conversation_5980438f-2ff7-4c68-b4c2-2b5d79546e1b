import { DropdownDto } from "redi-types";
import { TruckClassEnum } from "../enum/TruckClassEnum";

const TruckClasses:DropdownDto[] = [
    { code: TruckClassEnum.Class1, description: "Load capacity 1,270 kg, crane/winch Safe Working Load (SWL) 1,045 kg" },
    { code: TruckClassEnum.Class2, description: "Load capacity 3,040 kg, crane/winch Safe Working Load (SWL) 2,540 kg" },
    { code: TruckClassEnum.Class3, description: "Load capacity 6,125 kg, crane/winch Safe Working Load (SWL) 5,080 kg" },
    { code: TruckClassEnum.Class4, description: "Load capacity 7,000 kg, crane/winch Safe Working Load (SWL) 7,000 kg" },
    { code: TruckClassEnum.Class4T, description: "Load capacity 12,000 kg, crane/winch Safe Working Load (SWL) 7,000 kg" }
];

export default TruckClasses;