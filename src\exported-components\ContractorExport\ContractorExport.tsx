import { useEffect, useState } from "react";
import { GetDeductionsCDto, GetExtendedPartyCDto, GetPartyCDto } from "redi-types";
import PartyService from "../../services/party";
import LoadingDataComponent from "../../components/LoadingDataComponent/LoadingDataComponent";
import ContractorDetails from "../../pages/Contractor/ContractorDetails/ContractorDetails";


function ContractorExport({ id }: { id: string }) {

    const [contractor, setContractor] = useState<GetExtendedPartyCDto>();
    const [isLoading, setIsLoading] = useState(true);
    console.log(id);
    useEffect(() => {
        if (id) {
            PartyService.Get(id, true).then((data) => {
                if (!data.error && data.data) {
                    data.data.deductions = deductionSwitch(data.data.deductions, true);
                    setContractor(data.data);
                }
                setIsLoading(false);
            })
        }
    }, [id]);
    
    const deductionTypes = ["Insurance", "Radio Fee", "Miscellaneous", "Other"];
    //Moves the other type deduction to the otherType field
    function deductionSwitch(deductions: GetDeductionsCDto[], dataIn: boolean): GetDeductionsCDto[] {
        if (dataIn) {
            return deductions.map((dto, index) => {
                if (!deductionTypes.includes(dto.type)) {
                    dto.otherType = dto.type;
                    dto.type = "Other";
                }
                return dto;
            });
        } else {
            return deductions.map((dto, index) => {
                if (dto.type === "Insurance") {
                    dto.type = "Insurance";
                } else if (dto.type === "Radio Fee") {
                    dto.type = "Radio Fee";
                } else if (dto.type === "Miscellaneous") {
                    dto.type = "Miscellaneous";
                } else {
                    dto.type = dto.otherType ?? dto.type;
                }
                return dto;
            });
        }

    }

    return (
        <LoadingDataComponent isLoading={isLoading} data={contractor}>
            <ContractorDetails
                id={contractor?.partyId}
                showHeader
                contractor={contractor!}
                onSave={(value) => setContractor(state => ({ ...state, ...value }))}
            />
        </LoadingDataComponent>
    );
}

export default ContractorExport;