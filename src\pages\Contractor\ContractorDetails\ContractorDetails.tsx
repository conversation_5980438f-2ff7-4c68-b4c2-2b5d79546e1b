import { useState } from "react";
import './styles.scss';
import { GetExtendedPartyCDto } from "redi-types";
import ContractorForm from "../ContractorForm/ContractorForm";
import ContractorView from "../ContractorView/ContractorView";

function SubcontractorDetails(props: Props) {
    const { contractor, onSave, showHeader, id } = props;
    const [isEdit, setIsEdit] = useState(false);

    return (
        isEdit ?
        <ContractorForm
            id={id}
            initialValues={contractor}
            onCancel={() => setIsEdit(false)}
            onSave={(data) => {
                setIsEdit(false)
                onSave && onSave(data)
            }} 
            showHeader={showHeader}
            showPreferredContact={false}
            showPreferredAddress={false}
        /> :
        <ContractorView
            contractor={contractor}
            onCancel={() => setIsEdit(true)}
            onSave={(data) => onSave && onSave(data)}
        />
    );
}

export default SubcontractorDetails;

interface Props {
    id?: string;
    contractor: GetExtendedPartyCDto;
    showHeader?: boolean;
    onSave?: (value: GetExtendedPartyCDto) => void;
}