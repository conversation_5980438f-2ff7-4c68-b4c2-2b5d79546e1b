import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, IconButton, InputAdornment, MenuItem, TextField } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import { Field, FieldArray, FieldProps, Formik, FormikErrors, FormikHelpers, FormikProps, FormikTouched, getIn } from "formik";
import { useEffect, useState } from "react";
import { RediAutocompleteField } from "redi-formik-material";
import { AddressTypeCDto, BaseOrganisationCDto, BasePersonCDto, GetDeductionsCDto, GetExtendedPartyCDto, PartyAttributeCDto } from "redi-types";
import * as yup from "yup";
import AddressTypeService from "../../../services/addressType";
import PartyService from "../../../services/party";
import RoleTypeService from "../../../services/roleType";
import { PhoneValidator } from "../../../utils/phoneValidator";
import "./styles.scss";
import { ContactMethodTypeEnum } from "../../../enum/ContactMethodTypeEnum";
import Deductions from "../../../components/Deductions/Deductions";
import PartyContact from "../../../components/PartyContact/PartyContact";
import PartyAddress from "../../../components/PartyAddress/PartyAddress";
import { PartyTypeEnum } from "../../../enum/PartyTypeEnum";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";
import EmployeeTypes from "../../../constants/EmployeeTypes";
import { employmentType } from "../default_values/default_values";

const ListingAutocompleteField = withAsyncLoad<any>(() => import('listingmanagementcomponents/ListingAutocompleteField'));

type PartyFormDto = GetExtendedPartyCDto & { truck?: { listingId: number, subject: string, description?: string } | null };

interface Props {
  id?: string;
  initialValues: PartyFormDto;
  relatedPartyId?: string; // prop was there before rebuild but never used
  relatedPartyRoleTypeCode?: string; // prop was there before rebuild but never used
  onCancel?: () => void;
  onSave?: (data: PartyFormDto) => void;
  showAddTruck?: boolean;
  showPreferredContact: boolean;
  showPreferredAddress: boolean;
  showHeader?: boolean;
  forceOrganisationTypeCode?: boolean;
  forceRoleTypeCode?: boolean;
}

function getDefaultPartyRelationships(party: GetExtendedPartyCDto) {

    const existingRelationships = party.partyRelationships ?? [];
    const hasTowOperator = existingRelationships.some(r => r.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator);
    //Extend logic if need be to include checks for other relationships
    if (hasTowOperator) {
      return existingRelationships;
    }

    return [
      ...existingRelationships,
      {
        listingId: 0,
        partyId: NIL_UUID,
        relationshipTypeId: ListingRelationshipTypeEnum.TowOperator
      }
    ];
}

function ContractorForm(props: Props) {
  const schema = yup.object({
    organisation: yup.object().when("partyType", {
      is: "Organisation",
      then: () =>
        yup.object({
          name: yup.string().required("Enter name"),
        }),
    }),
    partyAttributes: yup.array().of(
      yup.object().shape({
        value: yup
          .mixed()
          .when("attributeCode", {
            is: (value: string) => value === "ABN",
            then: () =>
              // yup
              //   .string()
              //    //Regex for an 11 number field (can include spaces)
              //   .matches(/^(\d *?){11}$/, "Please enter valid ABN")
              //   .typeError("Please enter a valid ABN")
              //   .required("Please enter a valid ABN"),
              yup.number().test('len', '11 numbers', (val: any) => val.toString().length == 11).required("Enter an ABN")
          })
          .when("attributeCode", {
            is: (value: string) => value === "Contact",
            then: () =>
              yup
                .string()
                .typeError("Please enter a valid Contact Name")
                .required("Please enter a valid Contact Name"),
          }),
      })
    ),
    deductions: yup.array().of(
      yup.object({
        amount: yup.number().typeError("Test"),
      })
    ),
    contactMethods: yup.array().of(
      yup.object({
        value: yup
          .string()
          .required("Enter a value")
          .when("contactMethodTypeCode", {
            is: (value: ContactMethodTypeEnum) =>
              value === ContactMethodTypeEnum.Email,
            then: (schema) => schema.email("Enter a valid email address"),
          })
          .when("contactMethodTypeCode", {
            is: (value: ContactMethodTypeEnum) =>
              value === ContactMethodTypeEnum.Phone,
            then: (schema) =>
              schema.test("phone-validation", function (value) {
                const { path, createError } = this;
                const message = PhoneValidator.validate(value, "AU");
                if (message) {
                  return createError({ path, message: message });
                }
                return true;
              }),
          }),
        isPreferredContactMethod: yup
          .bool()
          .required("Select preferred contact method"),
        isPrimaryForMethodType: yup
          .bool()
          .required("Select primary contact method"),
      })
    ),
    addresses: yup.array().of(
      yup.object({
        stateOrProvince: yup.string().required("Enter a State or Province"),
        postalCode: yup.string().required("Enter a postal code"),
        countryCode: yup.string().required("Select a country"),
        addressTypeCode: yup.string().required("Select an address type"),
        lines: yup.string().required("Enter address lines"),
      })
    ),
  });

  const { initialValues, showPreferredAddress = true, showPreferredContact = true, showAddTruck = true, ...rest} = props;

  const [deductionUpdate, setDeductionUpdate] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [party, setParty] = useState<PartyFormDto>(initialValues);
  const [addressTypeList, setAddressTypeList] = useState<AddressTypeCDto[]>([]);
  const [hideOrganisationType, setHideOrganisationType] = useState(false);
  const [hideRoleType, setHideRoleType] = useState(false);

  /* List dtos do not match Get dtos for performance reasons, don't pass in dto from list */
  useEffect(() => {
    // Get if editing (has id prop)
    (async () => {
      try {
      if (props.id) {
        const partyResponse = await PartyService.Get(props.id, true, true);
        if (!partyResponse.error && partyResponse.data) {
          const deductions = deductionSwitch(partyResponse.data.deductions, true);
          const truck = partyResponse.data?.partyRelationships?.find(x => x.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator);
          const employementTypeExists = partyResponse.data?.partyAttributes?.find(x => x.attributeCode === "EmployeeType");
          const party = {
            truck: truck ? { listingId: Number(truck.listingId), subject: truck.listingSubject!, description: truck.listingDescription } : null,
            ...partyResponse.data, 
            partyRelationships: getDefaultPartyRelationships(partyResponse.data),
            deductions: deductions,
            partyAttributes: employementTypeExists ? partyResponse.data.partyAttributes : [...partyResponse.data.partyAttributes ?? [], employmentType]
          };
          console.log("initialValues", props.initialValues);
          console.log("party", party);
          setParty(party);
          if (partyResponse.data.partyAttributes) {
            //setDeductions(partyResponse.data?.partyAttributes)
          }
        }
      }
      const addressResponse = await AddressTypeService.GetList();
      if (!addressResponse.error) {
        setAddressTypeList(addressResponse.data ?? []);
      }
    } finally {
      setIsLoading(false);
    }
    })();
  }, []);

  const deductionTypes = ["Insurance", "Radio Fee", "Miscellaneous", "Other"];
  //Moves the other type deduction to the otherType field
  function deductionSwitch(deductions: GetDeductionsCDto[], dataIn: boolean): GetDeductionsCDto[] {
    if (dataIn) {
      return deductions.map((dto, index) => {
        if (!deductionTypes.includes(dto.type)) {
          dto.otherType = dto.type;
          dto.type = "Other";
        }
        return dto;
      });
    } else {
      return deductions.map((dto, index) => {
        if (dto.type === "Insurance") {
          dto.type = "Insurance";
        } else if (dto.type === "Radio Fee") {
          dto.type = "Radio Fee";
        } else if (dto.type === "Miscellaneous") {
          dto.type = "Miscellaneous";
        } else {
          dto.type = dto.otherType ?? dto.type;
        }
        return dto;
      });
    }

  }


  function cancel(form: FormikProps<PartyFormDto>) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  async function save(
    formData: PartyFormDto,
    actions: FormikHelpers<PartyFormDto>
  ) {
    setIsSaving(true);
    // --- Left over logic from before rebuild
    if (formData.partyType != "Organisation") {
      formData.organisation = undefined;
    }
    if (formData.partyType != "Person") {
      formData.person = undefined;
    }
    // ---
    // Update Party Relationnship names used in the backend for auditing
    const contactName = formData.partyAttributes?.find(x => x.attributeCode === "Contact")?.value;
    const partyDisplayName = formData.organisation.name + " | " + contactName;
    formData = { ...formData, displayName: partyDisplayName, partyRelationships: formData?.partyRelationships?.map((item) => ({...item, partyDisplayName: partyDisplayName }))};
    if (props.id) {
      const deductions = deductionSwitch(formData.deductions, false);
      /* Update */
      const response = await PartyService.Update({ ...formData, deductions: deductions });
      if (!response.error && response.data) {
        props.onSave && props.onSave(response.data);
      }
    } else {
      /* Create */
      const response = await PartyService.Create(
        formData,
        formData.roleTypeCode as string,
        props.relatedPartyId,
        props.relatedPartyRoleTypeCode
      );
      if (!response.error && response.data) {
        props.onSave && props.onSave(response.data);
      }
    }
    setIsSaving(false);
  }

  return (
    isLoading ? null :
    <Formik<PartyFormDto>
      enableReinitialize // Rerender when props changes (initialValues)
      validationSchema={schema}
      initialValues={party}
      onSubmit={async (data, actions) => {
        await save(data, actions);
      }}
    >
      {(form) => {
        return (
          <form onSubmit={form.handleSubmit}>
            <div styleName="container">
              {props.showHeader && (
                <div styleName="row">
                  <div styleName="header">Driver Details</div>
                  <div styleName="row">
                    <IconButton onClick={() => form.handleSubmit()}>
                      <FontAwesomeIcon icon="check" />
                    </IconButton>
                    <IconButton onClick={() => cancel(form)}>
                      <FontAwesomeIcon icon="close" />
                    </IconButton>
                  </div>
                </div>
              )}
              <div styleName="form-grid">
                {/* {JSON.stringify(form.errors)} */}
                {form.values.partyType == "Organisation" && (
                  <>
                  <div styleName="row">
                    <div styleName="field-container size-3">
                      <Field
                        variant="standard"
                        id="organisation.name"
                        name="organisation.name"
                        label="Company Name"
                        as={TextField}
                        error={
                          (
                            form.touched
                              .organisation as unknown as FormikTouched<BaseOrganisationCDto>
                          )?.name &&
                          Boolean(
                            (
                              form.errors
                                .organisation as FormikErrors<BaseOrganisationCDto>
                            )?.name
                          )
                        }
                        helperText={
                          (
                            form.touched
                              .organisation as unknown as FormikTouched<BaseOrganisationCDto>
                          )?.name &&
                          (
                            form.errors
                              .organisation as FormikErrors<BaseOrganisationCDto>
                          )?.name
                        }
                      />
                    </div>
                  </div>
                  <div styleName="row">
                    <div styleName="field-container size-1">
                      <Field
                        type="number"
                        variant="standard"
                        id={`partyAttributes[${form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "ABN"
                        )}].value`}
                        name={`partyAttributes[${form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "ABN"
                        )}].value`}
                        label={"ABN"}
                        as={TextField}
                        error={
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "ABN"
                          ) != undefined &&
                          form.touched.partyAttributes &&
                          (
                            form.touched
                              .partyAttributes as unknown as FormikTouched<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "ABN"
                            )
                          ]?.value &&
                          Boolean(
                            getIn(
                              (
                                form.errors
                                  .partyAttributes as unknown as FormikErrors<
                                    Array<PartyAttributeCDto>
                                  >
                              )?.[
                              form.values.partyAttributes?.findIndex(
                                (item) => item.attributeCode === "ABN"
                              )
                              ],
                              `value`
                            )
                          )
                        }
                        helperText={
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "ABN"
                          ) != undefined &&
                          form.touched.partyAttributes &&
                          (
                            form.touched
                              .partyAttributes as unknown as FormikTouched<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "ABN"
                            )
                          ]?.value &&
                          getIn(
                            (
                              form.errors
                                .partyAttributes as unknown as FormikErrors<
                                  Array<PartyAttributeCDto>
                                >
                            )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "ABN"
                            )
                            ],
                            `value`
                          )
                        }
                      />
                    </div>
                  </div>
                  </>
                )}
                {form.values.partyType == "Person" && (
                  <div styleName="row">
                    <div styleName="field-container size-1">
                      <Field
                        variant="standard"
                        id="person.firstName"
                        name="person.firstName"
                        label="First Name"
                        as={TextField}
                        error={
                          (
                            form.touched
                              .person as unknown as FormikTouched<BasePersonCDto>
                          )?.firstName &&
                          Boolean(
                            (form.errors.person as FormikErrors<BasePersonCDto>)
                              ?.firstName
                          )
                        }
                        helperText={
                          (
                            form.touched
                              .person as unknown as FormikTouched<BasePersonCDto>
                          )?.firstName &&
                          (form.errors.person as FormikErrors<BasePersonCDto>)
                            ?.firstName
                        }
                      />
                    </div>
                    <div styleName="field-container size-1">
                      <Field
                        variant="standard"
                        id="person.familyName"
                        name="person.familyName"
                        label="Last Name"
                        as={TextField}
                        error={
                          (
                            form.touched
                              .person as unknown as FormikTouched<BasePersonCDto>
                          )?.familyName &&
                          Boolean(
                            (form.errors.person as FormikErrors<BasePersonCDto>)
                              ?.familyName
                          )
                        }
                        helperText={
                          (
                            form.touched
                              .person as unknown as FormikTouched<BasePersonCDto>
                          )?.familyName &&
                          (form.errors.person as FormikErrors<BasePersonCDto>)
                            ?.familyName
                        }
                      />
                    </div>
                    {hideRoleType != true && (
                      <div styleName="field-container size-1">
                        <Field
                          id="roleTypeCode"
                          name="roleTypeCode"
                          label="Roles"
                          as={RediAutocompleteField}
                          initialValue={initialValues.roleTypeCode}
                          //minLength={3}
                          autoSelectFirst
                          callService={RoleTypeService.GetList}
                          fieldValue="roleTypeCode"
                          displayValue="label"
                          onChange={(selected: any) => {
                            form.setFieldValue("roleTypeCode", selected);
                          }}
                          error={
                            form.touched.roleTypeCode &&
                            Boolean(form.errors.roleTypeCode)
                          }
                          helperText={
                            form.touched.roleTypeCode &&
                            form.errors.roleTypeCode
                          }
                        />
                      </div>
                    )}
                    {hideRoleType == true && (
                      <div styleName="field-container size-1"></div>
                    )}
                  </div>
                )}
                {/* Examples of different field types */}
                {form.values.partyType == "Person" && (
                  <DatePicker
                    label="Date of Birth"
                    inputFormat="DD/MM/YYYY"
                    value={form.values.person?.dateOfBirth}
                    onChange={(val) =>
                      form.setFieldValue("person.dateOfBirth", val)
                    }
                    renderInput={(params) => <TextField {...params} />}
                  />
                )}
                {form.values.partyType == "Organisation" && (
                  <>
                  <div styleName="row">
                    <div styleName="field-container size-1">
                      <Field
                        variant="standard"
                        id={`partyAttributes[${form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "Contact"
                        )}].value`}
                        name={`partyAttributes[${form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "Contact"
                        )}].value`}
                        label={"Driver's name"}
                        as={TextField}
                        error={
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "Contact"
                          ) != undefined &&
                          form.touched.partyAttributes &&
                          (
                            form.touched
                              .partyAttributes as unknown as FormikTouched<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "Contact"
                            )
                          ]?.value &&
                          Boolean(
                            getIn(
                              (
                                form.errors
                                  .partyAttributes as unknown as FormikErrors<
                                    Array<PartyAttributeCDto>
                                  >
                              )?.[
                              form.values.partyAttributes?.findIndex(
                                (item) => item.attributeCode === "Contact"
                              )
                              ],
                              `value`
                            )
                          )
                        }
                        helperText={
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "Contact"
                          ) != undefined &&
                          form.touched.partyAttributes &&
                          (
                            form.touched
                              .partyAttributes as unknown as FormikTouched<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "Contact"
                            )
                          ]?.value &&
                          getIn(
                            (
                              form.errors
                                .partyAttributes as unknown as FormikErrors<
                                  Array<PartyAttributeCDto>
                                >
                            )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "Contact"
                            )
                            ],
                            `value`
                          )
                        }
                      />
                    </div>
                  </div>
                                      <div styleName="row">
                    <div styleName="field-container size-1">
                      <FieldArray name="partyAttributes">
                      {(arrayHelpers) => (
                          form.values.partyAttributes &&
                          form.values.partyAttributes.map((dto, index) => (
                            dto.attributeCode === "EmployeeType" ?
                            <Field
                                key={"__employeeType" + index}
                                select
                                fullWidth
                                variant="standard"
                                label={"Employee type"}
                                id={`partyAttributes_${index}_value`}
                                name={`partyAttributes[${index}].value`}
                                placeholder="Employee Type"
                                as={TextField}
                                error={Boolean(getIn(form.touched, `partyRelationships.${index}.value`) && getIn(form.errors, `partyRelationships.${index}.value`))}
                                helperText={getIn(form.touched, `partyRelationships.${index}.value`) && getIn(form.errors, `partyRelationships.${index}.value`)}
                            >
                                {EmployeeTypes.map((item) => (
                                    <MenuItem key={item} value={item}>
                                        {item}
                                    </MenuItem>
                                ))}
                            </Field> : null
                          )
                      ))}
                      </FieldArray>
                    </div>
                    </div>
                    <div styleName="row">
                    <div styleName="field-container size-1">
                      <Field
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              %
                            </InputAdornment>
                          ),
                        }}
                        type="number"
                        variant="standard"
                        id={`partyAttributes[${form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "WithholdingRate"
                        )}].value`}
                        name={`partyAttributes[${form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "WithholdingRate"
                        )}].value`}
                        label={"Withholding Rate"}
                        as={TextField}
                        error={
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "WithholdingRate"
                          ) != undefined &&
                          form.touched.partyAttributes &&
                          (
                            form.touched
                              .partyAttributes as unknown as FormikTouched<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "WithholdingRate"
                            )
                          ]?.value &&
                          Boolean(
                            getIn(
                              (
                                form.errors
                                  .partyAttributes as unknown as FormikErrors<
                                    Array<PartyAttributeCDto>
                                  >
                              )?.[
                              form.values.partyAttributes?.findIndex(
                                (item) => item.attributeCode === "WithholdingRate"
                              )
                              ],
                              `value`
                            )
                          )
                        }
                        helperText={
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "WithholdingRate"
                          ) != undefined &&
                          form.touched.partyAttributes &&
                          (
                            form.touched
                              .partyAttributes as unknown as FormikTouched<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "WithholdingRate"
                            )
                          ]?.value &&
                          getIn(
                            (
                              form.errors
                                .partyAttributes as unknown as FormikErrors<
                                  Array<PartyAttributeCDto>
                                >
                            )?.[
                            form.values.partyAttributes?.findIndex(
                              (item) => item.attributeCode === "WithholdingRate"
                            )
                            ],
                            `value`
                          )
                        }
                      />
                    </div>
                    </div>
                  </>
                )}
                {showAddTruck ?
                <FieldArray name="partyRelationships">
                  {() => (
                    <>
                      {form.values.partyRelationships &&
                        form.values.partyRelationships.map((relationship, index) => (
                        relationship.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator && (
                          <div styleName="field-container size-1"  key={index}>
                            <Field name="truck">
                              {(fieldProps: FieldProps) => (
                                <ListingAutocompleteField
                                  id="truck"
                                  name={fieldProps.field.name}
                                  label="Truck"
                                  value={form.values.truck}
                                  initialDisplayText={form.values.truck?.description ?? ""}
                                  fieldValue="listingId"
                                  fieldDisplayText="description"
                                  autoSelectFirst={true}
                                  params={{
                                    listingTypeId: ListingTypeEnum.Truck
                                  }}
                                  textFieldProps={{
                                    variant: "outlined"
                                  }}
                                  onChange={(val: string, listing?: any) => {
                                    form.setFieldValue("truck", listing);
                                    form.setFieldValue(`partyRelationships.${index}.listingId`, listing?.listingId);
                                    form.setFieldValue(`partyRelationships.${index}.listingSubject`, listing?.subject);
                                    form.setFieldValue(`partyRelationships.${index}.listingDescription`, listing?.description);
                                    form.setFieldValue(`partyRelationships.${index}.partyId`, listing ? form.values.partyId : undefined);
                                  }}
                                  showAddNewButton={true}
                                  buttonLabel={"Truck"}
                                  error={Boolean(getIn(form.touched, `partyRelationships.${index}.value`) && getIn(form.errors, `partyRelationships.${index}.value`))}
                                  helperText={getIn(form.touched, `partyRelationships.${index}.value`) && getIn(form.errors, `partyRelationships.${index}.value`)} />
                              )}
                            </Field>
                          </div>)
                        ))}
                    </>
                  )}
                </FieldArray> : null}
                <div styleName="field-container size-1">
                  <Field
                    variant="standard"
                    id={`partyAttributes[${form.values.partyAttributes?.findIndex(
                      (item) => item.attributeCode === "ContractorNote"
                    )}].value`}
                    name={`partyAttributes[${form.values.partyAttributes?.findIndex(
                      (item) => item.attributeCode === "ContractorNote"
                    )}].value`}
                    label={"Note"}
                    as={TextField}
                    error={
                      form.values.partyAttributes?.findIndex(
                        (item) => item.attributeCode === "ContractorNote"
                      ) != undefined &&
                      form.touched.partyAttributes &&
                      (
                        form.touched
                          .partyAttributes as unknown as FormikTouched<
                            Array<PartyAttributeCDto>
                          >
                      )?.[
                        form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "ContractorNote"
                        )
                      ]?.value &&
                      Boolean(
                        getIn(
                          (
                            form.errors
                              .partyAttributes as unknown as FormikErrors<
                                Array<PartyAttributeCDto>
                              >
                          )?.[
                          form.values.partyAttributes?.findIndex(
                            (item) => item.attributeCode === "ContractorNote"
                          )
                          ],
                          `value`
                        )
                      )
                    }
                    helperText={
                      form.values.partyAttributes?.findIndex(
                        (item) => item.attributeCode === "ContractorNote"
                      ) != undefined &&
                      form.touched.partyAttributes &&
                      (
                        form.touched
                          .partyAttributes as unknown as FormikTouched<
                            Array<PartyAttributeCDto>
                          >
                      )?.[
                        form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "ContractorNote"
                        )
                      ]?.value &&
                      getIn(
                        (
                          form.errors
                            .partyAttributes as unknown as FormikErrors<
                              Array<PartyAttributeCDto>
                            >
                        )?.[
                        form.values.partyAttributes?.findIndex(
                          (item) => item.attributeCode === "ContractorNote"
                        )
                        ],
                        `value`
                      )
                    }
                  />
                </div>
                <Deductions
                  deductions={form.values.deductions}
                  touchedList={form.touched.deductions}
                  errorList={form.errors.deductions}
                  setFieldValue={form.setFieldValue}
                  deductionTypes={deductionTypes}
                />
                <PartyContact
                  partType={PartyTypeEnum.Organisation}
                  contactMethodType={ContactMethodTypeEnum.Phone}
                  contacts={form.values.contactMethods}
                  touchedList={form.touched.contactMethods}
                  errorList={form.errors.contactMethods}
                  showPreferredContact={showPreferredContact}
                  setFieldValue={form.setFieldValue}
                />
                <PartyContact
                  partType={PartyTypeEnum.Organisation}
                  contactMethodType={ContactMethodTypeEnum.Email}
                  contacts={form.values.contactMethods}
                  touchedList={form.touched.contactMethods}
                  errorList={form.errors.contactMethods}
                  showPreferredContact={showPreferredContact}
                  setFieldValue={form.setFieldValue}
                />
                <PartyAddress
                  partType={PartyTypeEnum.Organisation}
                  addresses={form.values.addresses}
                  touchedList={form.touched.addresses}
                  errorList={form.errors.addresses}
                  showPreferredAddress={showPreferredAddress}
                  setFieldValue={form.setFieldValue}
                />

                {!props.showHeader && (
                  <div styleName="button-row">
                    <Button variant="outlined" onClick={() => cancel(form)}>
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      onClick={() => form.handleSubmit}
                    >
                      Save
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </form>
        );
      }}
    </Formik>
  );
}

export default ContractorForm;
