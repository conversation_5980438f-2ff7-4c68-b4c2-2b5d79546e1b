import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { GetExtendedPartyCDto} from "redi-types";
import './styles.scss';
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import PartyService from "../../../services/party";
import DraggableDialog from "../../../components/DraggableDialog/DraggableDialog";
import ContractorForm from "../ContractorForm/ContractorForm";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import { PartyTypeEnum } from "../../../enum/PartyTypeEnum";
import { RoleTypeEnum } from "../../../enum/RoleTypeCodeEnum";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";


export type ManagementItemDataDto = GetExtendedPartyCDto;

interface Props {}

const tableHeaders: TableHeader[] = [
      { id: 'Name', label: 'Name', isSortable: true, align: "left" },
      { id: 'RelationshipTypeId3', label: 'Allocated Truck', isSortable: true, align: "left" },
      { id: 'StatusCode', label: 'Status', isSortable: true, align: "left" },
      { id: 'PrimaryPhone', label: 'Phone', align: "left" },
      { id: 'PrimaryEmail', label: 'Email', align: "left" },
      { id: 'CreatedOn', label: 'Created on', isSortable: true, align: "left" },
      { id: 'pendingPayment', label: 'Pending Payment', isSortable: false, align: "right" },
      { id: 'Menu', label: 'Actions', align: "right" },
];


function getDefaults() {
    const result = PartyService.getDefaultValues(PartyTypeEnum.Organisation, RoleTypeEnum.Contractor);
    return {
      ...result,
      partyRelationships: [{
        listingId: 0,
        partyId: NIL_UUID,
        relationshipTypeId: ListingRelationshipTypeEnum.TowOperator
      }]
    }
}


  const RowMenu = (props: { data: ManagementItemDataDto, onClickedRowId: (partyId: string) => void, onSetIsAddDialogOpen: (value: boolean) => void, onSetIsDeleteDialogOpen: (value: boolean) => void }) => {
    const { onClickedRowId, onSetIsAddDialogOpen, onSetIsDeleteDialogOpen } = props;
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const isOpen = Boolean(anchorEl);
    const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(null);
    };
    const handleDelete = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      onClickedRowId(props.data.partyId);
      onSetIsDeleteDialogOpen(true);
    };
    const handleEdit = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      onClickedRowId(props.data.partyId);
      onSetIsAddDialogOpen(true);
    };

    return (
      <>
        <IconButton 
          id="actions-list"
          aria-controls={isOpen ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={isOpen ? 'true' : undefined}
          onClick={openMenu}
        >
          <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
        </IconButton>
        <Menu
          id="actions-menu"
          anchorEl={anchorEl}
          open={isOpen}
          onClose={handleClose}
          MenuListProps={{'aria-labelledby': 'menu-button'}}
        >
          <MenuItem onClick={handleEdit}>
            <ListItemText>Edit</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDelete}>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>
      </>
    );
  }

function RenderTableRow(props: { data: ManagementItemDataDto, onClickedRowId: (partyId: string) => void, onSetIsAddDialogOpen: (value: boolean) => void, onSetIsDeleteDialogOpen: (value: boolean) => void }) {
  const { data } = props;
  const currentPendingPayment = useMemo(() => {
    if (data.partyAttributes && data.partyAttributes.length > 0) {
      const payment = data.partyAttributes.find(x => x.attributeCode === "PendingPayment");
      return payment?.value ?? "";
    }
    return null;
  }, [data.partyAttributes]);
  
    // currnency format regex
  function currencyFormat(num: number) {
    return '$' + num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
  }

  return (
    <>
      <TableCell>{data.displayName}</TableCell>
      <TableCell>{data.relationshipFields?.towOperator}</TableCell>
      <TableCell>{data.statusCode === "Active" ? "Available" : "Unavailable"}</TableCell>
      <TableCell>{data.primaryPhone}</TableCell>
      <TableCell>{data.primaryEmail}</TableCell>
      <TableCell>
        {
          data.createdOn
          ? new Date(data.createdOn).toLocaleDateString('en-AU')
          : 'N/A'
        }
      </TableCell>
      <TableCell align="right">{currencyFormat(parseFloat(currentPendingPayment!))}</TableCell>
      <TableCell align="right">
        <RowMenu {...props} />
      </TableCell>
    </>
  );
}

function ContractorList(props: Props) {

  const navigate = useNavigate();

  const [clickedRowId, setClickedRowId] = useState<string>();
  const [clickedRowData, setClickedRowData] = useState<ManagementItemDataDto>();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);  

  // Dialog
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();



  const [ initialValues, setInitialValues ] = useState(getDefaults);


  /* Add Party Dialog */

  function openAddDialog() {
    setIsAddDialogOpen(true);
  }

  function closeAddDialog() {
    setIsAddDialogOpen(false);
  }

  async function onAddDialogSave() {
    setShowSnackbar(true);
    setSnackbarMessage('Successfully Created Driver');
    setSnackbarSeverity('success');
    setRefreshTableTrigger(trigger => trigger + 1);
    setIsAddDialogOpen(false);
  }

  /* Delete Dialog */

  function closeDeleteDialog() {
    setIsDeleteDialogOpen(false);
  }

  async function deleteRowItem() {
    closeDeleteDialog();
    if (clickedRowId) {
      const response = await PartyService.Delete(clickedRowId);
      if (!response.error) {
        setShowSnackbar(true);
        setSnackbarMessage('Successfully Deleted Driver');
        setSnackbarSeverity('success');
        // Refresh table data
        setRefreshTableTrigger(trigger => trigger + 1);
      } else {
        setShowSnackbar(true);
        setSnackbarMessage('Failed to delete Driver');
        setSnackbarSeverity('error');
      }
    }
    setClickedRowId(undefined);
  }


  function renderPartyDataTable() {
    return (
      <DataTable<ManagementItemDataDto, "partyId">
        primaryKeyProperty="partyId"
        title={"Drivers"}
        tableId={'base-' + ("" + "Organisation").toLowerCase() + '-'}
        pageSize={10}
        initialSortColumn="Name"
        refreshTableTrigger={refreshTableTrigger}
        tableHeight={540}
        addButtonLabel={"New Driver"}
        addButtonOnClick={() => {
          setClickedRowId(undefined);
          setInitialValues(getDefaults());
          openAddDialog()}}
        tableHeaders={tableHeaders}
        renderTableRow={(data) => (
                        <RenderTableRow 
                          data={data} onClickedRowId={setClickedRowId} 
                          onSetIsAddDialogOpen={setIsAddDialogOpen}
                          onSetIsDeleteDialogOpen={setIsDeleteDialogOpen} 
                        />)}
        onRowClick={(row) => {navigate(`/Driver/${row.partyId}`)}}
        filterOptions={['Available', 'Unavailable', 'All']}
        callService={(inListParameters, inSearch, inFilter) => {
          const _inFilter = inFilter === 'All' ? 
                            '' :  inFilter === 'Available' ? 
                            'Active' : 'InActive';
          return PartyService.List(
            inListParameters, 
            "Company", 
            _inFilter, 
            inSearch, 
            "Contractor", 
            true, 
            true, 
            true,
            "3",
            "Description",
            inSearch ?
            {
              "3": { values: [inSearch], operator: "contains" }
            } : undefined);
        }}
      />
    );
  }

  function renderAddPartyDialog() {
    {/* Add Dialog */}
    console.log(clickedRowId);
    return (
        <DraggableDialog
          maxWidth="xl"
          title={`${clickedRowId === undefined ? 'Add New' : 'Edit'} Driver`}
          isOpen={isAddDialogOpen}
          onCancel={closeAddDialog}
        >
          <ContractorForm
            id={clickedRowId}
            onCancel={closeAddDialog}
            onSave={onAddDialogSave}
            showPreferredContact={true}
            showPreferredAddress={true}
            initialValues={initialValues}
        />
        </DraggableDialog>
    );
  }

  function renderYesNoPartyDialog() {
    {/* Delete Dialog */}
    return (
        <YesNoDialog
          title={"Delete " + RoleTypeEnum.Contractor}
          bodyText="Are you sure? This cannot be reversed"
          isOpen={isDeleteDialogOpen}
          onNo={closeDeleteDialog}
          onYes={deleteRowItem}
        />
    );
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        {renderPartyDataTable()}

        {renderAddPartyDialog()}

        {renderYesNoPartyDialog()}

        <Snackbar open={showSnackbar} 
          autoHideDuration={4000} 
          onClose={() => setShowSnackbar(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default ContractorList;