import { useState } from "react";
import { GetPartyCDto } from "redi-types";
import { ContractorStatusCodeEnum } from "../../../enum/ContractorStatusCodeEnum";
import PartyService from "../../../services/party";
import { IconButton, ListItemText, Menu, MenuItem } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import "./styles.scss";

interface Props {
    contractor: GetPartyCDto;
    onSave: (data: GetPartyCDto) => void;
}

function ContractorActionButtons(props: Props) {
    const { contractor, onSave } = props;
    const [ selectedStatus, setSelectedStatus] = useState<ContractorStatusCodeEnum | null>();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [deleteDialog, setDeleteDialog] = useState(false);


    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
        setSelectedStatus(null);
        setDeleteDialog(false);
    };
    const handleStatusSelect = (statusCode: ContractorStatusCodeEnum) => {
        if (contractor.statusCode === statusCode) { return; }
        setSelectedStatus(statusCode);
    };
    const handleSave = () => {
        PartyService.UpdateStatus(contractor.partyId, selectedStatus!).then((data) => {
        if (!data.error && data.data) {
            onSave(data.data);
        }
        handleClose();
       });
    };

    const handleDelete = () => {
        PartyService.Delete(contractor.partyId);
    }

    const currentStatus = contractor.statusCode === ContractorStatusCodeEnum.Active ? "Available" : "Unavailable";
    const selectedStatusName = selectedStatus === ContractorStatusCodeEnum.Active ? "Available" : "Unavailable";

    return(
        <>
            <IconButton
                id="actions-list"
                aria-controls={open ? 'case-details-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
            >
                <FontAwesomeIcon styleName="menu-icon" icon="ellipsis-v" />
            </IconButton>
            <Menu
                id="actions-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{"aria-labelledby": "menu-button"}}
            >
                {contractor.statusCode === ContractorStatusCodeEnum.Active ?
                <MenuItem onClick={() => handleStatusSelect(ContractorStatusCodeEnum.Inactive)}>
                    <ListItemText>Set Unavailable</ListItemText>
                </MenuItem> : null}
                {contractor.statusCode === ContractorStatusCodeEnum.Inactive ?
                <MenuItem onClick={() => handleStatusSelect(ContractorStatusCodeEnum.Active)}>
                    <ListItemText>Set Available</ListItemText>
                </MenuItem> : null}
                <MenuItem onClick={() => setDeleteDialog(true)} >
                    <ListItemText>Delete</ListItemText>
                </MenuItem>
            </Menu>
            {selectedStatus ? 
            <YesNoDialog
                title="Change Status"
                bodyText={`Please confirm your are changing status from ${currentStatus} to ${selectedStatusName}.`}
                isOpen={true}
                onNo={handleClose}
                onYes={handleSave}
            /> : null
            }
            {deleteDialog ?
            <YesNoDialog
                title="Delete Driver"
                bodyText={`Are you sure? This cannot be reversed`}
                isOpen={true}
                onNo={handleClose}
                onYes={handleDelete}
            /> : null
            }
        </>
    )
}

export default ContractorActionButtons;