import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Grid, IconButton } from "@mui/material";
import { useMemo } from "react";
import { GetExtendedPartyCDto } from "redi-types";
import "./styles.scss";
import ContractorActionButtons from "./ContractorActionButtons";
import { withholdingRate } from "../default_values/default_values";
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";

interface Props {
  contractor: GetExtendedPartyCDto;
  onCancel: () => void;
  onSave: (data: GetExtendedPartyCDto) => void;
}

function ContractorView(props: Props) {

  const { contractor, onSave, onCancel } = props;

  const { abn, note, contactName, withholdingRate, employmentType } = useMemo(() => {
    const response = { abn: "", note: "", contactName: "", withholdingRate: "", employmentType: "" };
    if (contractor.partyAttributes && contractor.partyAttributes.length > 0) {
      response.abn = contractor.partyAttributes.find(x => x.attributeCode === "ABN")?.value ?? "";
      response.note = contractor.partyAttributes.find(x => x.attributeCode === "ContractorNote")?.value ?? "";
      response.contactName = contractor.partyAttributes.find(x => x.attributeCode === "Contact")?.value ?? "";
      response.withholdingRate = contractor.partyAttributes.find(x => x.attributeCode === "WithholdingRate")?.value ?? "";
      response.employmentType = contractor.partyAttributes.find(x => x.attributeCode === "EmployeeType")?.value ?? "";
    }
    return response;
  }, [contractor.partyAttributes]);

  const truckAssigned = useMemo(() => {
    if (contractor.partyRelationships && contractor.partyRelationships.length > 0) {
      const truckAssigned = contractor.partyRelationships.find(x => x.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator);
      return truckAssigned?.listingDescription ?? "";
    }
    return null;
  }, [contractor.partyRelationships]);

  return (
    <div styleName="container">
      <div styleName="row">
        <div styleName="header">Driver Details</div>
        <div styleName="row">
          <IconButton onClick={onCancel}>
            <FontAwesomeIcon icon="pen-to-square" />
          </IconButton>
          <ContractorActionButtons contractor={contractor} onSave={onSave} />
        </div>
      </div>
      <div styleName="grid">
        <Grid container>
          <Grid item xs={6}>
            <div styleName="column">
              <div styleName="label">Contact Name</div>
              <div styleName="value">{contactName ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">ABN</div>
              <div styleName="value">{abn ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Phone</div>
              <div styleName="value">{contractor.primaryPhone ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Email</div>
              <div styleName="value">{contractor.primaryEmail ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Address</div>
              <div styleName="value">{contractor.primaryAddress ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Status</div>
              <div styleName="value">{contractor.statusCode === "Active" ? "Available" : "Unavailable"}</div>
            </div>
          </Grid>
          <Grid item xs={6}>
            <div styleName="column">
              <div styleName="label">Truck</div>
              <div styleName="value">{truckAssigned ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Employement Type</div>
              <div styleName="value">{employmentType ?? "N/A"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Withholding Rate</div>
              <div styleName="value">{withholdingRate + "%"}</div>
            </div>
            <div styleName="column">
              <div styleName="label">Deductions</div>
              {contractor.deductions?.map((deduction) =>
                <Grid container key={deduction.deductionId}>
                  <Grid xs={6} textAlign={"left"}>
                    {deduction.type}
                  </Grid>
                  <Grid xs={6} textAlign={"right"}>
                    {"$" + deduction.amount.toFixed(2)}
                  </Grid>
                </Grid>
              )}
            </div>
          </Grid>
          <Grid item xs={12}>
            <div styleName="column">
              <div styleName="label">Note</div>
              <div styleName="value">{note ?? "N/A"}</div>
            </div>
          </Grid>
        </Grid>
      </div>
    </div>
  )
}

export default ContractorView;
