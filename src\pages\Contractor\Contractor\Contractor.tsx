import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { GetExtendedPartyCDto, GetPartyCDto } from "redi-types";
import PartyService from "../../../services/party";
import { CircularProgress } from "@mui/material";
import ContractorHeader from "./ContractorHeader/ContractorHeader";
import LoadingDataComponent from "../../../components/LoadingDataComponent/LoadingDataComponent";
import './styles.scss';
import ContractorDetails from "../ContractorDetails/ContractorDetails";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";

const EventList = withAsyncLoad<{ parentEntityId?: string, parentEntityType?: string }>(() => import('commoncomponents/EventList'));

interface Props {}

function Contractor(props: Props) {
    const { id } = useParams();
    const [tabIndex, setTabIndex] = useState(0);
    const [contractor, setContractor] = useState<GetExtendedPartyCDto>();
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (id) {
            PartyService.Get(id, true, true).then((data) => {
                if (!data.error && data.data) {
                    setContractor(data.data);
                }
                setIsLoading(false);
            })
        }
    }, [id]);

    return (
        <div styleName="page">
            {
                isLoading
                    ?
                    <div styleName="loading">
                        <CircularProgress color="primary" size={50} />
                    </div>
                    :
                    <>
                        <div styleName="container">
                            <div styleName="content">
                                <LoadingDataComponent isLoading={isLoading} data={contractor}>
                                    <ContractorHeader contractor={contractor!} />
                                </LoadingDataComponent>
                                <div styleName="main-content">
                                    <div styleName="row">
                                        <div styleName={`tab ${tabIndex === 0 ? 'active' : ''}`} onClick={() => setTabIndex(0)}>
                                            Driver
                                        </div>
                                        <div styleName={`tab ${tabIndex === 1 ? 'active' : ''}`} onClick={() => setTabIndex(1)}>
                                            Events
                                        </div>
                                    </div>
                                    <div styleName="tab-body">
                                        <div styleName="tab-grid-1" style={{ display: tabIndex === 0 ? 'grid' : 'none' }}>
                                            <div>
                                                <LoadingDataComponent isLoading={isLoading} data={contractor}>
                                                    <ContractorDetails
                                                        id={id}
                                                        showHeader
                                                        contractor={contractor!}
                                                        onSave={(value) => setContractor(value)}
                                                    />
                                                </LoadingDataComponent>
                                            </div>
                                        </div>
                                        <div styleName="tab-grid-2" style={{ display: tabIndex === 1 ? 'grid' : 'none' }}>
                                            <LoadingDataComponent isLoading={isLoading} data={contractor}>
                                                <EventList 
                                                    parentEntityId={id} 
                                                    parentEntityType="Party"
                                                />
                                            </LoadingDataComponent>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
            }
        </div>
    )
}

export default Contractor;
