import { GetExtendedPartyCDto } from "redi-types";
import './styles.scss';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useMemo } from "react";
import { ListingRelationshipTypeEnum } from "../../../../enum/ListingRelationshipTypeEnum";

function ContractorHeader(props: Props) {
    const { contractor } = props;
    
    const { currentAbn, employeeType } = useMemo(() => {
        const response = {
            currentAbn: "",
            employeeType: ""
        };
        if (contractor.partyAttributes && contractor.partyAttributes.length > 0) {
            const abn = contractor.partyAttributes.find(x => x.attributeCode === "ABN");
            response.currentAbn = abn?.value ?? "";
        }
        if (contractor.partyAttributes && contractor.partyAttributes.length > 0) {
            const employeeType = contractor.partyAttributes.find(x => x.attributeCode === "EmployeeType");
            response.employeeType = employeeType?.value ?? "";
        }
        return response;
    }, [contractor.partyAttributes]);

    const truckAssigned = useMemo(() => {
        if (contractor.partyRelationships && contractor.partyRelationships.length > 0) {
        const truckAssigned = contractor.partyRelationships.find(x => x.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator);
        return truckAssigned?.listingSubject ?? "N/A";
        }
        return null;
    }, [contractor.partyRelationships]);

    return (
            <div styleName="summary-bar">
                <div styleName="column">
                    <div styleName="align-left">
                        <div styleName="top">{contractor.displayName ?? "N/A"}</div>
                        <div styleName="bottom">Name</div>
                    </div>
                </div>
                <div styleName="column">
                    <div styleName="align-left">
                        <div styleName="top">{currentAbn ?? "N/A"}</div>
                        <div styleName="bottom">ABN</div>
                    </div>
                </div>
                <div styleName="column">
                    <div styleName="align-left">
                        <div styleName="top">{employeeType}</div>
                        <div styleName="bottom">Type</div>
                    </div>
                </div>
                <div styleName="column">
                    <div styleName="align-left">
                        <div styleName="top">{truckAssigned}</div>
                        <div styleName="bottom">Truck</div>
                    </div>
                </div>
                <div styleName="column">
                    <div styleName="align-left">
                        <div styleName="top">{contractor.statusCode === "Active" ? "Available" : "Unavailable"}</div>
                        <div styleName="bottom">Status</div>
                    </div>
                </div>
            </div>
    );
}

export default ContractorHeader;

interface Props {
    contractor: GetExtendedPartyCDto;
}