import { PartyAttributeCDto } from "redi-types";
import { NIL as NIL_UUID, v4 as uuidv4 } from "uuid";

export const abn: PartyAttributeCDto = {
  partyAttributeId: 0,
  partyId: NIL_UUID,
  attributeCode: "ABN",
  value: "",
  label: "",
  inputTypeCode: "Decimal",
  attributeValueTypeCode: "ValueNumeric",
  options: [],
};
export const contact: PartyAttributeCDto = {
  partyAttributeId: 0,
  partyId: NIL_UUID,
  attributeCode: "Contact",
  value: "",
  label: "",
  inputTypeCode: "Text",
  attributeValueTypeCode: "ValueString",
  options: [],
};
export const contractorNote: PartyAttributeCDto = {
  partyAttributeId: 0,
  partyId: NIL_UUID,
  attributeCode: "ContractorNote",
  value: "",
  label: "",
  inputTypeCode: "Text",
  attributeValueTypeCode: "ValueString",
  options: [],
};
export const withholdingRate: PartyAttributeCDto = {
  partyAttributeId: 0,
  partyId: NIL_UUID,
  attributeCode: "WithholdingRate",
  value: "",
  label: "",
  inputTypeCode: "Decimal",
  attributeValueTypeCode: "ValueNumeric",
  options: [],
};
export const pendingPayment: PartyAttributeCDto = {
  partyAttributeId: 0,
  partyId: NIL_UUID,
  attributeCode: "PendingPayment",
  value: "0.00",
  label: "",
  inputTypeCode: "Decimal",
  attributeValueTypeCode: "ValueNumeric",
  options: [],
};
export const employmentType: PartyAttributeCDto = {
  partyAttributeId: 0,
  partyId: NIL_UUID,
  attributeCode: "EmployeeType",
  value: "",
  label: "",
  inputTypeCode: "Text",
  attributeValueTypeCode: "ValueString",
  options: [],
};
export const default_attribute_list: PartyAttributeCDto[] = [
  abn,
  contact,
  contractorNote,
  withholdingRate,
  employmentType,
  pendingPayment,
];
