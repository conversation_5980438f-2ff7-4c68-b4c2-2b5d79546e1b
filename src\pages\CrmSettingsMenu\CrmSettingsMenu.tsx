import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useNavigate } from 'react-router-dom';
import './styles.scss';

interface Props {}

function CrmSettingsMenu(props: Props) {

  const navigate = useNavigate();

  return (
    <div styleName="card">
      <div styleName="row">
        <FontAwesomeIcon icon="file-lines" />
        <h3>CRM</h3>
      </div>
      <a onClick={() => navigate('/ContractorManagement')}>Contractor</a>
      <a onClick={() => navigate('/InsuranceManagement')}>Insurance</a>
      <a onClick={() => navigate('/JobManagement')}>Job</a>
    </div>
  );
}

export default CrmSettingsMenu;