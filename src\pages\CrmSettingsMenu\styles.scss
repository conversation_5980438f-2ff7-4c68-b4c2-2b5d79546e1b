@import "../../config/theme/vars.scss";

.card {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  background-color: $primaryColor;
  color: $white;

  .row {
    display: flex;
    align-items: center;

    svg {
      font-size: 20px;
      margin-right: 0.5rem;
    }
  }

  a {
    font-size: 15px;
    cursor: pointer;
    opacity: 0.8;
    text-decoration: none;

    &:not(:last-child) {
      margin-bottom: 0.25rem;
    }
    &:hover {
      opacity: 1;
    }
  }
  
}