import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Accordion, AccordionDetails, AccordionSummary, Checkbox, FormControlLabel, FormGroup, Typography } from '@mui/material';
import { useState } from 'react';

interface Props {}

function AccordianDemo(props: Props) {

  const [expanded, setExpanded] = useState<number | null>();
  const handleChange = (panelIndex: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panelIndex : null);
  };

  return (
    <div>
      <Accordion expanded={expanded === 0} onChange={handleChange(0)}>
        <AccordionSummary
          expandIcon={ <FontAwesomeIcon icon="chevron-down" />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <b>Panel 1</b>
        </AccordionSummary>
        <AccordionDetails>Panel Body 1</AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 1} onChange={handleChange(1)}>
        <AccordionSummary
          expandIcon={ <FontAwesomeIcon icon="chevron-down" />}
          aria-controls="panel2-content"
          id="panel2-header"
        >
          <b>Panel 2</b>
        </AccordionSummary>
        <AccordionDetails>Panel Body 2</AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 2} onChange={handleChange(2)}>
        <AccordionSummary
          expandIcon={ <FontAwesomeIcon icon="chevron-down" />}
          aria-controls="panel3-content"
          id="panel3-header"
        >
          <b>Panel 3</b>
        </AccordionSummary>
        <AccordionDetails>Panel Body 3</AccordionDetails>
      </Accordion>
      <Accordion expanded={expanded === 3} onChange={handleChange(3)}>
        <AccordionSummary
          expandIcon={ <FontAwesomeIcon icon="chevron-down" />}
          aria-controls="panel4-content"
          id="panel4-header"
        >
          <b>Panel 4</b>
        </AccordionSummary>
        <AccordionDetails>Panel Body 4</AccordionDetails>
      </Accordion>
    </div>
  );
}

export default AccordianDemo;