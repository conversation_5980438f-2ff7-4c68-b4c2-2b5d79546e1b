import { useState } from "react";
import { RediAutocompleteField } from "redi-formik-material";
import { BaseExampleDto, GetListUserCDto } from "redi-types";
import ExampleService from "../../../services/demoServices/example";

interface Props {}

function RediAutocompleteDemo(props: Props) {
  const [search, setSearch] = useState("test");
  const [selectedListing, setSelectedListing] = useState<GetListUserCDto>();

  function onChange(val: string, obj: GetListUserCDto) {
    // If using in a form, you must set the field values here ie.
    // form.setFieldValue("managerUserId", obj.userId); form.setFieldValue("managerName", obj.fullName);
    setSearch(val);
    setSelectedListing(obj);
  }

  function callService(query: string) {
    return new Promise<{ data: BaseExampleDto[]; total: number }>(
      (resolve, reject) => {
        ExampleService.getList(query)
          .then((data) => {
            if (data.data) {
              const dsr = {
                data: data.data.list,
                total: data.data.totalNumberOfRows,
              };
              console.log("data:", dsr);
              resolve(dsr);
            } else {
              reject("No data");
            }
          })
          .catch((error) => error);
      }
    );
  }

  return (
    <>
      <RediAutocompleteField
        label="Redi Autocomplete"
        initialValue="test"
        minLength={3}
        autoSelectFirst
        callService={callService}
        fieldValue="userId" // From callService response dto
        displayValue="fullName" // From callService response dto
        value={search}
        onChange={onChange}
      />
      {selectedListing && (
        <>
          <b>Selected Autocomplete item:</b>
          <br />
          {JSON.stringify(selectedListing, null, 2)}
        </>
      )}
    </>
  );
}

export default RediAutocompleteDemo;

/* Syntax to use when inside a form:

  <Field
    id="tenantId"
    name="tenantId"
    label="Tenant"
    as={RediAutocompleteField}
    initialValue={(initialValues as ExtendedApplicationUserDto)?.tenant?.name}
    minLength={0}
    autoSelectFirst
    callService={(query: string) => new Promise<DataSourceResult<GetListTenantCDto>>((resolve, reject) => {
      TenantService.GetListQuery(undefined, undefined, query)
        .then((data) => {
          if (data.data) {
            const dsr: DataSourceResult<GetListTenantCDto> = {
              data: data.data.list,
              total: data.data.totalNumOfRows
            };
            resolve(dsr);
          } else {
            reject('No data');
          }
        })
        .catch(error => error);
    })}
    fieldValue="tenantId"
    displayValue="name"
    onChange={(val: string) => {
      console.log('onChange() - val:', val);
      form.setFieldValue("tenantId", val)
      console.log('form:', form);
    }}
    error={Boolean((form.errors as FormikErrors<ExtendedApplicationUserDto>).tenantId)}
    helperText={(form.errors as FormikErrors<ExtendedApplicationUserDto>).tenantId}
  />
  
*/
