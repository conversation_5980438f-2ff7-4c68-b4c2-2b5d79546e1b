import { useState } from "react";
import { RediAutocompleteField } from "redi-formik-material";
import { GetListingDto, GetPartyCDto } from "redi-types";
import { HttpResult } from "redi-http";
import PartyService from "../../../services/party";

interface Props {}

function RediAutocompleteDemo(props: Props) {

  const [subject, setSubject] = useState('Sub');
  const [selectedListing, setSelectedListing] = useState<GetListingDto>();

  function onChange(val: string, obj: GetListingDto) {
    setSubject(val);
    setSelectedListing(obj);
  }

  function callService(query: string) {
    return new Promise<HttpResult<GetPartyCDto[]>>((resolve, reject) => {
      PartyService.List(undefined, undefined, undefined, query, undefined ,undefined, undefined)
      .then((data) => {
        if (data.data) {
          resolve({ data: data.data.list });
        } else {
          reject('No data');
        }
      })
      .catch(error => error);
    }); 
  }

  return (
    <>
      <RediAutocompleteField
        label="Redi Autocomplete"
        initialValue=""
        minLength={3}
        autoSelectFirst
        callService={callService}
        fieldValue="subject"
        displayValue="subject"
        value={subject}
        onChange={onChange}
      />
      {
        selectedListing &&
        <>
          <b>Selected Autocomplete item:</b><br />
          {JSON.stringify(selectedListing, null, 2)}
        </>
      }
    </>
  );
}

export default RediAutocompleteDemo;