import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Stack } from "@mui/material";
import AvatarEditor from "../../../components/AvatarEditor/AvatarEditor";
import { useState } from "react";
import { FileCDto } from "redi-types";
import { HttpResult } from "redi-http";
import rediLogo from "../../../assets/rediLogo.png";
import PhotoCamera from "@mui/icons-material/PhotoCamera";

import "./styles.scss";

function AvatarEditorDemo() {
  const [editorOpen, setEditorOpen] = useState<boolean>(false);
  const [imageUrl, setImageUrl] = useState<string>(rediLogo);

  function openEditor() {
    setEditorOpen(true);
  }

  function closeEditor() {
    setEditorOpen(false);
  }

  /**
   * Gets the result of the http request so you can do what you need to with the FileCDto
   * @param result The result of the saving action. Undefined if save was not attempted
   */
  function handleSave(result: HttpResult<FileCDto> | undefined) {
    // Logic to save the new avatar using some other service
    if (result != undefined) {
      // Save successful
      if (!result.error && result.data) {
        //alert("Avatar Saved successffuullyy");
        //console.log(result.data);
        console.log("Updated avatar successfully");
        setImageUrl(result.data.pathOrUrl);
      } else if (result.error) {
        //alert("Error my guy " + result.error.summary);
        console.log(result.error);
      } else {
        //alert("Did not error, but did not receive any data")
        console.log("unknown error " + result);
      }
    } else {
      console.log("Sir, there's no partyId or partyType.\nThere'll be no avatar then. I'll tell James Cameron.");
      // Probably failed because you didn't supply a partyId and partyType
    }

    closeEditor();
  }

  return (
    <div>
      <Stack direction="row" spacing={2}>
        <Avatar src={imageUrl} sx={{ width: 150, height: 150 }} />

        <Tooltip title="Change Avatar" placement="bottom">
          <PhotoCamera styleName="camera-icon" onClick={openEditor}></PhotoCamera>
        </Tooltip>
      </Stack>
      {/* <Button onClick={openEditor} variant="contained">Open Avatar Editor Modal</Button> */}
      <AvatarEditor
        isOpen={editorOpen}
        cancelOnClick={closeEditor}
        onSave={handleSave}
        imageUrl={imageUrl} // Set the inital image in the editor
        buttonVariant="outlined" // Set the "Accept"/submit button style
        // Replace these with your own values
        parentEntityId={"00000000-0000-0000-0000-000000000000"}
        parentEntityType={"Submission"}
      />
    </div>
  );
}

export default AvatarEditorDemo;
