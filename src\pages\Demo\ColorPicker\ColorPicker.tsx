import { useState } from 'react';
import { ChromePicker, ColorResult, RGBColor } from 'react-color'

interface Props {}

function ColorPickerDemo(props: Props) {

  // Can't use alpha pickers if state is a hex string
  const [colorRGB, setColorRGB] = useState<RGBColor>({r: 0, g: 0, b: 0});

  const handleChange = (color: ColorResult, event: React.ChangeEvent<HTMLInputElement>) => {
    setColorRGB(color.rgb);
  };
  
  return (
    <>
      <ChromePicker color={ colorRGB } onChange={ handleChange } />
      <div>
        Color - rgba({colorRGB.r}, {colorRGB.g}, {colorRGB.b}, {colorRGB.a})
      </div>
    </>
  );
}

export default ColorPickerDemo;

/* 
  <AlphaPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <BlockPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <CirclePicker color={ color } onChangeComplete={ handleChangeComplete } />
  <CompactPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <GithubPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <HuePicker color={ color } onChangeComplete={ handleChangeComplete } />
  <MaterialPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <PhotoshopPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <SketchPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <SliderPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <SwatchesPicker color={ color } onChangeComplete={ handleChangeComplete } />
  <TwitterPicker color={ color } onChangeComplete={ handleChangeComplete } />
*/