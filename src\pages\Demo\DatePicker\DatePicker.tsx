import { TextField } from '@mui/material';
import { useState } from 'react';
import { DatePicker } from '@mui/x-date-pickers';
import './styles.scss';
// import AccessibleIcon from "@mui/icons-material/Accessible";

interface Props {}

function DatePickerDemo(props: Props) {

  const [date, setDate] = useState<Date|null>(new Date());
  
  return (
    <DatePicker
      label="Date Example"
      inputFormat="DD/MM/YYYY"
      value={date}
      onChange={(val) => setDate(val)}
      renderInput={(params) => <TextField {...params} />}
      // Override icons - MUI only
      // components={{
      //   OpenPickerIcon: AccessibleIcon,
      //   LeftArrowIcon: AccessibleIcon,
      //   RightArrowIcon: AccessibleIcon,
      //   SwitchViewIcon: AccessibleIcon
      // }}
    />
  );
}

export default DatePickerDemo;