import './styles.scss';
import FunctionComponentExample from "./BaseFunctionalComponent/FunctionComponent";
import FunctionComponentDestructedProps from "./BaseFunctionalComponent/FunctionComponentDestructedProps";
import ButtonDemo from "./Button/Button";
import CheckboxDemo from "./Checkbox/Checkbox";
import DialogDemo from "./Dialog/Dialog";
import FormDemo from "./Form/FormDemo";
import RadioButtonDemo from "./RadioButtons/RadioButtons";
import SelectDemo from "./Select/Select";
import CustomizedSnackbars from "./Snackbar/CustomizedSnackbars";
import SimpleSnackbar from "./Snackbar/SimpleSnackbar";
import SwitchDemo from "./Switch/Switch";
import TabsDemo from "./Tabs/Tabs";
import TextFieldsDemo from "./TextField/TextField";
import { Divider } from "@mui/material";
import DatePickerDemo from "./DatePicker/DatePicker";
import FileDropzoneDemo from './FileDropzone/FileDropzone';
import MenuDemo from './Menu/Menu';
import { BaseExampleDto } from 'redi-types';
import RediAutocompleteDemo from './Autocomplete/RediAutocomplete';
import InfiniteScrollDemo from './InfiniteScroll/InfiniteScrollDemo';
import styles from '../../config/theme/vars.scss';
import ColorPickerDemo from './ColorPicker/ColorPicker';
import AccordianDemo from './Accordian/Accordian';

function Demo() {

  // Dto import
  const example: BaseExampleDto = {
    exampleId: '1',
    exampleName: 'Component Demos'
  }

  return (
    <div styleName="demo">
      <div styleName="grid">
        <div>
          <h1>SCSS</h1>

          <div styleName="css-container">
            <div styleName="primary-color">$primaryColor</div>
            <div styleName="secondary-color">$secondaryColor</div>
            <div styleName="off-color">$offColor</div>
            <div styleName="secondary-off-color">$secondaryOffColor</div>
            <div style={{color: styles.primaryColor}}>SASS variable access in TypeScript (non prefered way)</div>
          </div>

          <Divider />

          <h1>HTML</h1>

          <h1>Heading 1</h1>
          <h2>Heading 2</h2>
          <h3>Heading 3</h3>
          <h4>Heading 4</h4>
          <h5>Heading 5</h5>
          <h6>Heading 6</h6>
          <p>Paragraph</p>

          <Divider />

          <h4>Drag and Drop File</h4>
          <FileDropzoneDemo />
          
          <Divider />

          {/* Function Component Examples */}
          <FunctionComponentExample heading="Basic Function Component with Props">
            <p>Child 1</p>
          </FunctionComponentExample>
          <FunctionComponentDestructedProps heading="Function Component with Destructured Props">
            <p>Child 2</p>
          </FunctionComponentDestructedProps>

          <Divider />

          <h4>Form</h4>
          <FormDemo />

          <Divider />

          <h4>Infinite Scroll</h4>
          <InfiniteScrollDemo />

          <Divider />

          <h4>Color Pickers</h4>
          <ColorPickerDemo />
        </div>
        <div>
          <h1>Material UI</h1>

          <h4>Button</h4>
          <ButtonDemo />

          <h4>Dropdown Menu</h4>
          <MenuDemo />

          <h4>Select</h4>
          <SelectDemo />

          <h4>Tabs</h4>
          <TabsDemo />

          <h4>Text Fields</h4>
          <TextFieldsDemo />

          <h4>Date Picker</h4>
          <DatePickerDemo />

          <h4>Switch</h4>
          <SwitchDemo />

          <h4>Checkbox</h4>
          <CheckboxDemo />

          <h4>Radio Buttons</h4>
          <RadioButtonDemo />

          <h4>Accordian</h4>
          <AccordianDemo />

          <h4>Snackbar (Toast)</h4>
          <SimpleSnackbar />
          <CustomizedSnackbars />
          
          <h4>Redi-Formik-Autocomplete</h4>
          <RediAutocompleteDemo />

          <h4>Dialog (Modal)</h4>
          <DialogDemo />

        </div>
      </div>

    </div>
  );
}

export default Demo;