import { Button, DialogActions, DialogContentText, Paper, PaperProps } from '@mui/material';
import React, { useState } from 'react';
import Draggable from 'react-draggable';
import DraggableDialog from '../../../components/DraggableDialog/DraggableDialog';

// Empty ref must be passed in to fix error in 'react-draggable': 'finddomnode-is-deprecated-in-strictmode'
// https://stackoverflow.com/questions/63603902/finddomnode-is-deprecated-in-strictmode-finddomnode-was-passed-an-instance-of-d
function PaperComponent(props: PaperProps) {
  const nodeRef = React.useRef(null);
  return (
    <Draggable
      nodeRef={nodeRef}
      handle="#draggable-dialog-title"
      cancel={'[class*="MuiDialogContent-root"]'}
    >
      <Paper ref={nodeRef} {...props} />
    </Draggable>
  );
}

interface Props {}

function DialogDemo(props: Props) {

  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  
  return (
    <>
      <Button variant="outlined" onClick={handleClickOpen}>
        Open draggable dialog
      </Button>
      <DraggableDialog
        title="Drag me"
        isOpen={open}
        onCancel={handleClose}
      >
        <DialogContentText>
          Drag me by my top bar to position me wherever you want!
        </DialogContentText>
        <DialogActions>
          <Button autoFocus onClick={handleClose}>
            No I refuse
          </Button>
          <Button onClick={handleClose}>Yes Sir!</Button>
        </DialogActions>
      </DraggableDialog>
    </>
  );
}

export default DialogDemo;