import { useState } from "react";
import Dropzone from 'react-dropzone';
import './styles.scss';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button } from "@mui/material";
import React from "react";

interface Props {}

function FileDropzoneDemo(props: Props) {
  
  const [files, setFiles] = useState<File[]>([]);

  const onDrop = (droppedFiles: File[]) => {
    // Do something with the files
    console.log('droppedFiles:', droppedFiles);
    setFiles([...files, ...droppedFiles]);
  };

  const removeFile = (index: number) => {
    const filesCopy = [...files];
    filesCopy.splice(index, 1); // Remove element at index i
    setFiles(filesCopy);
  }

  const submit = () => {
    console.log('Submitting files ...');
    files.forEach((file) => {
      const reader = new FileReader()

      reader.onabort = () => console.log('file reading was aborted');
      reader.onerror = () => console.log('file reading has failed');
      reader.onload = () => {
        // Do whatever you want with the file contents
        const binaryStr = reader.result;
        console.log(file.name, 'data:', binaryStr);
      }
      reader.readAsArrayBuffer(file);
    })
  }
    
  return (
    <div>
      <Dropzone onDrop={onDrop}>
        {({getRootProps, getInputProps, isDragActive}) => (
          <section>
            <div {...getRootProps()} styleName="drop-area">
              <input {...getInputProps()} />
              {
                isDragActive ?
                  <p>Drop that files here ...</p> :
                  <p>Drag and drop files here, or click to select files</p>
              }
            </div>
          </section>
        )}
      </Dropzone>
      {
        files.length > 0 &&
        <div>
          <h5>Upload Files</h5>
          <hr/>
          <div styleName="drop-row">
            {
              files.map((file, i) => { 
                return (
                  <React.Fragment key={i}>
                    <div>{file.name}</div>
                    <FontAwesomeIcon icon="close" styleName="icon" onClick={() => removeFile(i)} />
                  </React.Fragment>
                )
              })
            }
          </div>
          <Button variant="contained" onClick={submit}>Upload</Button>
        </div>
      }      
    </div>
  );
}

export default FileDropzoneDemo;