@import "../../../config/theme/vars.scss";

.drop-area {
  height: 10rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed $primaryColor;
  background-color: hsl(209, 60%, 95%);
  color: $primaryColor;
  border-radius: 6px;
  cursor: pointer;
}

.drop-row {
  display: grid;
  grid-template-columns: max-content auto;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;

  div {
    margin-right: 1rem;
  }
}

.icon {
  cursor: pointer;
}