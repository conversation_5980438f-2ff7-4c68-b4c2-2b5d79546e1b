import { useState } from "react";
import * as yup from "yup";
import { Field, Formik, FormikHelpers } from "formik";
import './styles.scss';
import { <PERSON><PERSON>, TextField } from "@mui/material";
import { DatePicker } from '@mui/x-date-pickers';
import { FormDto } from "redi-types";

interface Props {
  initialValues: FormDto;
  onSave: (data: FormDto) => void;
  onCancel: () => void;
}

function Form(props: Props) {

  let schema = yup.object({
    name: yup.string().required('Name is required'),
    age: yup.number().required('Age is required').positive().integer(),
    email: yup.string().email(),
    website: yup.string().url().nullable(),
    createdOn: yup.date().default(() => new Date()),
  });

  // Only used for printing form data to page
  const [submittedData, setSubmittedData] = useState<FormDto>();

  const reset = (data: FormDto, actions: FormikHelpers<any>) => {
    setSubmittedData(undefined);
		props.onCancel && props.onCancel();
	}

  const save = (data: FormDto, actions: FormikHelpers<any>) => {
    setSubmittedData(data);
		props.onSave && props.onSave(data);
	}

  // return (null);

  return (
    <>
    {
      props.initialValues &&
      <Formik<FormDto>
        // classes={styles}
        validationSchema={schema}
        initialValues={props.initialValues}
        onSubmit={(data, actions) => {
          save(data, actions);
        }}
        onReset={(data, actions) => {
          reset(data, actions);
        }}
      >
        {(form) => (
          <form onSubmit={form.handleSubmit}>
            <div> 
              <div styleName="form-grid">
                <Field
                  variant="standard"
                  label="Name"
                  id="name"
                  name="name"
                  placeholder="Enter your name"
                  as={TextField}
                  error={form.touched.name && Boolean(form.errors.name)}
                  helperText={form.touched.name && form.errors.name}
                />
                <Field
                  sx={{ width: 60 }}
                  type="number"
                  variant="standard"
                  label="Age"
                  id="age"
                  name="age"
                  as={TextField}
                  error={form.touched.age && Boolean(form.errors.age)}
                  helperText={form.touched.age && form.errors.age}
                />
                <Field
                  variant="standard"
                  label="Email"
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                  as={TextField}
                  error={form.touched.email && Boolean(form.errors.email)}
                  helperText={form.touched.email && form.errors.email}
                />
                <Field
                  variant="standard"
                  label="Website"
                  id="website"
                  name="website"
                  placeholder="www.example.com"
                  as={TextField}
                  error={form.touched.website && Boolean(form.errors.website)}
                  helperText={form.touched.website && form.errors.website}
                />
                <DatePicker
                  label="Date Example"
                  inputFormat="DD/MM/YYYY"
                  value={form.values.createdOn}
                  onChange={(val) => form.setFieldValue("createdOn", val)}
                  renderInput={(params) => <TextField {...params} />}
                />
              </div>
              {/* Buttons */}
              <div styleName="button-row">
                <Button variant="outlined" onClick={() => form.handleReset()}>
                  Reset
                </Button>
                <Button variant="contained" onClick={() => form.handleSubmit()}>
                  Save
                </Button>
              </div>
            </div>
          </form>
        )}
      </Formik>
    }
    Submitted Form Data:
    <br/>
    {
      submittedData &&
      JSON.stringify(submittedData, null, 2)
    }
    </>
  );
}

export default Form;