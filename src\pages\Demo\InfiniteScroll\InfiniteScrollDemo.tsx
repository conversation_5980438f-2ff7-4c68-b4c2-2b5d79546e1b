import { useEffect, useState } from 'react';
import InfiniteS<PERSON>roll from "react-infinite-scroll-component";
import { GetListingDto } from 'redi-types';
import ListingService from '../../../services/demoServices/listing';
import './styles.scss';

const InfiniteScrollDemo = () => {
  const pageSize = 3;

  const [items, setItems] = useState<GetListingDto[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [currentPageIndex, setCurrentPageIndex] = useState(1); // Must be primed for second call
  const [fetching, setFetching] = useState(false);

  useEffect(() => {
    return () => { fetchMoreData() };
  }, []);

  const fetchMoreData = async () => {
    if (fetching) {
      return;
    }

    setFetching(true);

    try {
      const response = await ListingService.SearchAsync('', 'subject', '', pageSize, currentPageIndex);
      const data = response?.data?.list;

      if (data && data.length > 0) {
        setItems([...items, ...data]);
        setCurrentPageIndex(currentPageIndex + 1);
      } else {
        setHasMore(false);
      }

      setCurrentPageIndex(currentPageIndex + 1);
    } finally {
      setFetching(false);
    }
  };

  return (
    <InfiniteScroll
      dataLength={items.length}
      next={fetchMoreData}
      hasMore={hasMore}
      loader={<h4>Loading...</h4>}
      height={100}
      endMessage={
        <p style={{ textAlign: "center" }}>
          <b>No more items</b>
        </p>
      }
    >
      {
        items.map((item, i) => (
          <div styleName="scroll-item" key={item.listingId}>
            #{i} - {item.subject}
          </div>
        ))}
    </InfiniteScroll>
  );
};

export default InfiniteScrollDemo;