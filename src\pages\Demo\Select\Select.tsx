import { FormControl, FormHelperText, InputLabel, MenuItem, Select, SelectChangeEvent } from '@mui/material';
import { useState } from 'react';

interface Props {}

function SelectDemo(props: Props) {

  const [select, setSelect] = useState('');
  const selectChanged = (newValue: SelectChangeEvent) => setSelect(newValue.target.value);

  {/* Both InputLabel and label are required, one for gap in border and one to display text */}
  return (
    <FormControl sx={{ minWidth: 120 }}>
      <InputLabel id="label-id">Label</InputLabel>
      <Select
        label="Label"
        labelId="label-id"
        placeholder="Placeholder"
        value={select}
        onChange={selectChanged}
      >
        <MenuItem value={1}>One</MenuItem>
        <MenuItem value={2}>Two</MenuItem>
        <MenuItem value={3}>Three</MenuItem>
      </Select>
      {/* <FormHelperText>With label + helper text</FormHelperText> */}
    </FormControl>
  );
}

export default SelectDemo;