import { Button, Snackbar } from '@mui/material';
import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface Props {}

function SimpleSnackbar(props: Props) {

  const [open, setOpen] = useState(false);

  const handleClick = () => {
    setOpen(true);
  };

  const handleClose = (event: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpen(false);
  };

  const action = (
    <>
      <Button variant="outlined" color="secondary" size="small" onClick={handleClose}>
        UNDO
      </Button>
      <FontAwesomeIcon icon="close" onClick={handleClose} aria-label="close" />
    </>
  );

  return (
    <div>
      <Button onClick={handleClick}>Open simple snackbar</Button>
      <Snackbar
        open={open}
        autoHideDuration={4000}
        onClose={handleClose}
        message="I'm a snackbar"
        action={action}
      />      
    </div>
  );
}

export default SimpleSnackbar;