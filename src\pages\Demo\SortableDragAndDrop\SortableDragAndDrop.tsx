import { useState } from 'react';
import "./styles.scss";
import DragSortList from '../../../components/DragSortList/DragSortList';

interface Props {}

function DragSortListDemo(props: Props) {

  const data: ExampleListDto[] = [
    {dataId: '1', label: 'Item 1'},
    {dataId: '2', label: 'Item 2'},
    {dataId: '3', label: 'Item 3'},
    {dataId: '4', label: 'Item 4'},
    {dataId: '5', label: 'Item 5'},
    {dataId: '6', label: 'Item 6'},
    {dataId: '7', label: 'Item 7'}
  ];

  const [items, setItems] = useState<ExampleListDto[]>(data);  

  function itemsReordered(updatedItems: ExampleListDto[]) {
    console.log('itemsReordered() - updatedItems:', updatedItems);
    setItems(updatedItems);
  }

  return (
    <div styleName="container">
      <DragSortList<ExampleListDto>
        primaryKey="dataId"
        items={items}
        onChange={itemsReordered}
        renderItem={(item) => (
          <DragSortList.Item id={item.dataId}>
            {item.label}
            <DragSortList.DragHandle />
          </DragSortList.Item>
        )}
      />
    </div>
  );
}

export default DragSortListDemo;

interface ExampleListDto {
  dataId: string;
  label: string;
}