@import "../../config/theme/vars.scss";

.demo {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5rem;
  padding: 0 5rem 3rem 5rem;
}

.css-container {
  background-color: #ccc;
}

.primary-color {
  color: $primaryColor;
}

.secondary-color {
  color: $secondaryColor;
}

.off-color {
  color: $offColor;
}

.secondary-off-color {
  color: $secondaryOffColor;
}