import { useState } from "react";
import './styles.scss';
import { GetExtendedPartyCDto } from "redi-types";
import DriverForm from "../DriverForm/DriverForm";
import DriverView from "../DriverView/DriverView";

function DriverDetails(props: Props) {
    const { driver, onSave, showHeader, id } = props;
    const [isEdit, setIsEdit] = useState(false);

    return (
        isEdit ?
        <DriverForm
            id={id}
            initialValues={driver}
            onCancel={() => setIsEdit(false)}
            onSave={(data) => {
                setIsEdit(false)
                onSave && onSave(data)
            }} 
            showHeader={showHeader}
            showPreferredContact={false}
            showPreferredAddress={false}
        /> :
        <DriverView
            driver={driver}
            onCancel={() => setIsEdit(true)}
            onSave={(data) => onSave && onSave(data)}
        />
    );
}

export default DriverDetails;

interface Props {
    id?: string;
    driver: GetExtendedPartyCDto;
    showHeader?: boolean;
    onSave?: (value: GetExtendedPartyCDto) => void;
}
