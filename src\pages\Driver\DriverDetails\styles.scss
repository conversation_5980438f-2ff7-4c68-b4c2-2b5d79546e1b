@import "../../../config/theme/vars.scss";

.container {
  display: flex;
  flex-direction: column;
  padding: 24px;

  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .header {
      color: $primaryColor;
      font-size: 1.3rem;
      font-weight: 600;
    }
  }

  .grid {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 20px;

    .row {
      display: flex;
      gap: 24px;
      margin-bottom: 0;

      .column {
        flex: 1;
      }
    }

    .column {
      .label {
        font-weight: 600;
        margin-bottom: 8px;
      }

      .value {
        color: #4E4949;
      }
    }
  }

  .empty-tab {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #666;
  }
}