@import "../../../config/theme/vars.scss";

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.events-container {
    padding: 1rem;
    background-color: $white;
    border-radius: 10px;

    .header {
        color: $primaryColor;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .events-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .event-item {
            display: grid;
            grid-template-columns: 150px 150px 1fr;
            padding: 1rem;
            background-color: #f5f5f5;
            border-radius: 5px;

            .event-date {
                font-weight: 600;
            }

            .event-type {
                color: $primaryColor;
                font-weight: 600;
            }

            .event-description {
                color: #4E4949;
            }
        }
    }
}