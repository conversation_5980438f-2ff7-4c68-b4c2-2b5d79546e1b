import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button, IconButton, InputAdornment, MenuItem, TextField } from "@mui/material";
import { Field, FieldArray, FieldProps, Formik, FormikHelpers, FormikProps, getIn } from "formik";
import { useState } from "react";
import { GetDeductionsCDto, GetExtendedPartyCDto } from "redi-types";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import * as yup from "yup";
import Deductions from "../../../components/Deductions/Deductions";
import PartyAddress from "../../../components/PartyAddress/PartyAddress";
import PartyContact from "../../../components/PartyContact/PartyContact";
import { ContactMethodTypeEnum } from "../../../enum/ContactMethodTypeEnum";
import { PartyTypeEnum } from "../../../enum/PartyTypeEnum";
import { PhoneValidator } from "../../../utils/phoneValidator";
import "./styles.scss";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";
import PartyService from "../../../services/party";
import { RoleTypeEnum } from "../../../enum/RoleTypeCodeEnum";
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";

const ListingAutocompleteField = withAsyncLoad<any>(() => import('listingmanagementcomponents/ListingAutocompleteField'));

type PartyFormDto = GetExtendedPartyCDto & { truck?: { listingId: number, subject: string } | null };

interface Props {
  id?: string;
  initialValues?: GetExtendedPartyCDto;
  onCancel?: () => void;
  onSave?: (data: GetExtendedPartyCDto) => void;
  showAddTruck?: boolean;
  showPreferredContact?: boolean;
  showPreferredAddress?: boolean;
  showHeader?: boolean;
}

const deductionTypes = ["Insurance", "Radio Fee", "Miscellaneous", "Other"];

const validationSchema = yup.object().shape({
  person: yup.object().when("partyType", {
    is: "Person",
    then: () =>
      yup.object({
        firstName: yup.string().required("Enter first name"),
        familyName: yup.string().required("Enter family name"),
      }),
  }),
  partyAttributes: yup.array().of(
    yup.object().shape({
      value: yup
        .mixed()
        .when("attributeCode", {
          is: (value: string) => value === "DriverABN",
          then: () =>
            yup.number()
              .test('len', 'Must be exactly 11 digits', val => val?.toString().length === 11)
              .required("ABN is required")
        })
        .when("attributeCode", {
          is: (value: string) => value === "DriverWithholdingRate",
          then: () =>
            yup.number()
              .min(0, "Must be at least 0")
              .max(100, "Must be at most 100")
              .required("Withholding rate is required")
        })
        // .when("attributeCode", {
        //   is: (value: string) => value === "TruckListingId",
        //   then: () =>
        //     yup.number()
        //       .required("Truck is required")
        // })
    })
  ),
  note: yup.string(),
  deductions: yup.array().of(
    yup.object().shape({
      type: yup.string().required("Type is required"),
      amount: yup.number()
        .typeError("Amount must be a number")
        .min(0, "Must be positive")
        .required("Amount is required")
    })
  ),
  contactMethods: yup.array().of(
    yup.object({
      value: yup
        .string()
        .required("Enter a value")
        .when("contactMethodTypeCode", {
          is: (value: ContactMethodTypeEnum) =>
            value === ContactMethodTypeEnum.Email,
          then: (schema) => schema.email("Enter a valid email address"),
        })
        .when("contactMethodTypeCode", {
          is: (value: ContactMethodTypeEnum) =>
            value === ContactMethodTypeEnum.Phone,
          then: (schema) =>
            schema.test("phone-validation", function (value) {
              const { path, createError } = this;
              const message = PhoneValidator.validate(value, "AU");
              if (message) {
                return createError({ path, message: message });
              }
              return true;
            }),
        }),
      isPreferredContactMethod: yup
        .bool()
        .required("Select preferred contact method"),
      isPrimaryForMethodType: yup
        .bool()
        .required("Select primary contact method"),
    })
  ),
  addresses: yup.array().of(
    yup.object({
      stateOrProvince: yup.string().required("Enter a State or Province"),
      postalCode: yup.string().required("Enter a postal code"),
      countryCode: yup.string().required("Select a country"),
      addressTypeCode: yup.string().required("Select an address type"),
      lines: yup.string().required("Enter address lines"),
    })
  ),
});

const defaultValues: PartyFormDto = {
  partyId: uuidv4(),
  statusCode: "Active",
  name: "",
  partyType: PartyTypeEnum.Person,
  roleTypeCode: RoleTypeEnum.Driver,
  person: {
    firstName: "",
    familyName: ""
  },
  partyAttributes: [
    {
      partyAttributeId: 0,
      partyId: NIL_UUID,
      attributeCode: "DriverABN",
      value: "",
      label: "",
      inputTypeCode: "Decimal",
      attributeValueTypeCode: "ValueNumeric",
      options: []
    },
    {
      partyAttributeId: 0,
      partyId: NIL_UUID,
      attributeCode: "DriverWithholdingRate",
      value: "0",
      label: "",
      inputTypeCode: "Decimal",
      attributeValueTypeCode: "ValueNumeric",
      options: []
    },
    {
      partyAttributeId: 0,
      partyId: NIL_UUID,
      attributeCode: "TruckId",
      value: "",
      label: "",
      inputTypeCode: "Text",
      attributeValueTypeCode: "ValueString",
      options: []
    },
    {
      partyAttributeId: 0,
      partyId: NIL_UUID,
      attributeCode: "TruckListingId",
      value: "0",
      label: "",
      inputTypeCode: "Text",
      attributeValueTypeCode: "ValueNumeric",
      options: []
    }
  ],
  truck: null,
  note: "",
  contactMethods: [
    {
      contactMethodId: NIL_UUID,
      contactMethodTypeCode: ContactMethodTypeEnum.Phone,
      value: "",
      isPreferredContactMethod: false,
      isPrimaryForMethodType: false,
      parentEntityId: NIL_UUID,
      parentEntityType: "Party",
      sortOrder: 1,
      contactMethodTypeLabel: ""
    },
    {
      contactMethodId: NIL_UUID,
      contactMethodTypeCode: ContactMethodTypeEnum.Email,
      value: "",
      isPreferredContactMethod: false,
      isPrimaryForMethodType: false,
      parentEntityId: NIL_UUID,
      parentEntityType: "Party",
      sortOrder: 2,
      contactMethodTypeLabel: ""
    }
  ],
  addresses: [
    {
      addressId: NIL_UUID,
      addressTypeCode: "",
      lines: "",
      city: "",
      stateOrProvince: "",
      postalCode: "",
      countryCode: "",
      isPreferredAddress: true,
      parentEntityId: NIL_UUID,
      parentEntityType: "Party",
      sortOrder: 1
    }
  ],
  deductions: [{
    deductionId: NIL_UUID,
    parentEntityId: NIL_UUID,
    type: "Radio Fee",
    amount: 0
  }],
  partyRelationships: [{
    listingId: 0,
    partyId: NIL_UUID,
    relationshipTypeId: ListingRelationshipTypeEnum.TowOperator
  }]
};

function DriverForm(props: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const [party] = useState<GetExtendedPartyCDto>(() => {
    if (props.initialValues) {
      const truck = props.initialValues?.partyRelationships?.find(x => x.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator);
      return {
        ...props.initialValues,
        truck: truck ? { listingId: Number(truck.listingId), subject: truck.listingSubject } : null,
      };
    } else {
      return defaultValues;
    }
});

  const { showPreferredAddress = true, showPreferredContact = true, showAddTruck = true, ...rest} = props;

  function deductionSwitch(deductions: GetDeductionsCDto[], dataIn: boolean): GetDeductionsCDto[] {
    if (dataIn) {
      return deductions.map((dto, index) => {
        if (!deductionTypes.includes(dto.type)) {
          dto.otherType = dto.type;
          dto.type = "Other";
        }
        return dto;
      });
    } else {
      return deductions.map((dto, index) => {
        if (dto.type === "Insurance") {
          dto.type = "Insurance";
        } else if (dto.type === "Radio Fee") {
          dto.type = "Radio Fee";
        } else if (dto.type === "Miscellaneous") {
          dto.type = "Miscellaneous";
        } else {
          dto.type = dto.otherType ?? dto.type;
        }
        return dto;
      });
    }

  }

  const save = async (formData: PartyFormDto, actions: FormikHelpers<PartyFormDto>) => {
    setIsLoading(true);
    // --- Left over logic from before rebuild
    if (formData.partyType != "Organisation") {
      formData.organisation = undefined;
    }
    if (formData.partyType != "Person") {
      formData.person = undefined;
    }
    // ---

    if (props.id) {
      const deductions = deductionSwitch(formData.deductions, false);
      /* Update */
      const response = await PartyService.Update({ ...formData, deductions: deductions });
      if (!response.error && response.data) {
        props.onSave && props.onSave(response.data);
      }
    } else {
      /* Create */
      const response = await PartyService.Create(
        formData,
        formData.roleTypeCode as string
      );
      if (!response.error && response.data) {
        props.onSave && props.onSave(response.data);
      }
    }
    setIsLoading(false);
  };

  const cancel = (form: FormikProps<PartyFormDto>) => {
    form.resetForm();
    props.onCancel && props.onCancel();
  };

  return (
    party &&
    <Formik<PartyFormDto>
      enableReinitialize
      initialValues={party}
      validationSchema={validationSchema}
      onSubmit={async (data, actions) => {
        await save(data, actions);
      }}
    >
      {(form) => (
        <form onSubmit={form.handleSubmit}>
          <div styleName="container">
            {props.showHeader && (
              <div styleName="row">
                <div styleName="header">Driver Details</div>
                <div styleName="row">
                  <IconButton onClick={() => form.handleSubmit()}>
                    <FontAwesomeIcon icon="check" />
                  </IconButton>
                  <IconButton onClick={() => cancel(form)}>
                    <FontAwesomeIcon icon="close" />
                  </IconButton>
                </div>
              </div>
            )}

            <div styleName="form-grid">
              <div styleName="row">
                  <div styleName="field-container size-2">
                    <Field
                      name="person.firstName"
                      as={TextField}
                      variant="standard"
                      label="First name"
                      fullWidth
                      error={getIn(form.touched, 'person.firstName') && Boolean(getIn(form.errors, 'person.firstName'))}
                      helperText={getIn(form.touched, 'person.firstName') && Boolean(getIn(form.errors, 'person.firstName'))}
                    />
                  </div>
                  <div styleName="field-container size-2">
                    <Field
                      name="person.familyName"
                      as={TextField}
                      variant="standard"
                      label="Family name"
                      fullWidth
                      error={getIn(form.touched, 'person.familyName') && Boolean(getIn(form.errors, 'person.familyName'))}
                      helperText={getIn(form.touched, 'person.familyName') && Boolean(getIn(form.errors, 'person.familyName'))}
                    />
                  </div>
                </div>
                <FieldArray name="partyAttributes">
                  {(arrayHelpers) => (
                    <>
                      {form.values.partyAttributes?.map((attribute, index) => (
                        <div key={index}>
                          {attribute.attributeCode === "DriverABN" && (
                            <div styleName="row">
                              <div styleName="field-container size-2">
                                <Field
                                  type="number"
                                  variant="standard"
                                  id={`partyAttributes.${index}.value`}
                                  name={`partyAttributes.${index}.value`}
                                  label="ABN"
                                  as={TextField}
                                  fullWidth
                                  error={Boolean(
                                    getIn(form.touched, `partyAttributes.${index}.value`) &&
                                    getIn(form.errors, `partyAttributes.${index}.value`)
                                  )}
                                  helperText={
                                    getIn(form.touched, `partyAttributes.${index}.value`) &&
                                    getIn(form.errors, `partyAttributes.${index}.value`)
                                  }
                                />
                              </div>
                            </div>
                          )}
                          {attribute.attributeCode === "DriverWithholdingRate" && (
                            <div styleName="row">
                              <div styleName="field-container size-2">
                                <Field
                                  type="number"
                                  variant="standard"
                                  id={`partyAttributes.${index}.value`}
                                  name={`partyAttributes.${index}.value`}
                                  label="Withholding Rate (%)"
                                  as={TextField}
                                  fullWidth
                                  InputProps={{
                                    startAdornment: <InputAdornment position="start">%</InputAdornment>,
                                  }}
                                  error={Boolean(
                                    getIn(form.touched, `partyAttributes.${index}.value`) &&
                                    getIn(form.errors, `partyAttributes.${index}.value`)
                                  )}
                                  helperText={
                                    getIn(form.touched, `partyAttributes.${index}.value`) &&
                                    getIn(form.errors, `partyAttributes.${index}.value`)
                                  }
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </>
                  )}
                </FieldArray>
                <FieldArray name="partyRelationships">
                  {() => (
                    <>
                      {form.values.partyRelationships &&
                        form.values.partyRelationships.map((relationship, index) => (
                          <div key={index}>
                            {relationship.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator && (
                              <div styleName="row">
                                <div styleName="field-container size-2">
                                  <Field name="truck">
                                    {(fieldProps: FieldProps) => (
                                      <ListingAutocompleteField
                                        id="truck"
                                        name={fieldProps.field.name}
                                        label="Truck"
                                        value={form.values.truck}
                                        fieldValue="listingId"
                                        fieldDisplayText="subject"
                                        autoSelectFirst={true}
                                        params={{
                                          listingTypeId: ListingTypeEnum.Truck
                                        }}
                                        textFieldProps={{
                                          variant: "outlined"
                                        }}
                                        onChange={(val: string, listing?: { listingId: number, subject: string }) => {
                                          form.setFieldValue("truck", listing);
                                          form.setFieldValue(`partyRelationships.${index}.listingId`, listing?.listingId);
                                          form.setFieldValue(`partyRelationships.${index}.listingSubject`, listing?.subject);
                                          form.setFieldValue(`partyRelationships.${index}.partyId`, listing ? form.values.partyId : undefined);
                                        }}
                                        showAddNewButton={showAddTruck}
                                        buttonLabel={"Truck"}
                                        error={Boolean(getIn(form.touched, `partyRelationships.${index}.value`) && getIn(form.errors, `partyRelationships.${index}.value`))}
                                        helperText={getIn(form.touched, `partyRelationships.${index}.value`) && getIn(form.errors, `partyRelationships.${index}.value`)} />
                                    )}
                                  </Field>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                    </>
                  )}
                </FieldArray>
                <div styleName="row">
                  <div styleName="field-container size-4">
                    <Field
                      name="note"
                      as={TextField}
                      variant="standard"
                      label="Note"
                      multiline
                      rows={3}
                      fullWidth
                    />
                  </div>
                </div>
            </div>
            <Deductions
              deductions={form.values.deductions}
              touchedList={form.touched.deductions}
              errorList={form.errors.deductions}
              setFieldValue={form.setFieldValue}
              deductionTypes={deductionTypes}
            />
            <PartyContact
              partType={PartyTypeEnum.Person}
              contactMethodType={ContactMethodTypeEnum.Phone}
              contacts={form.values.contactMethods}
              touchedList={form.touched.contactMethods}
              errorList={form.errors.contactMethods}
              showPreferredContact={showPreferredContact}
              setFieldValue={form.setFieldValue}
            />
            <PartyContact
              partType={PartyTypeEnum.Person}
              contactMethodType={ContactMethodTypeEnum.Email}
              contacts={form.values.contactMethods}
              touchedList={form.touched.contactMethods}
              errorList={form.errors.contactMethods}
              showPreferredContact={showPreferredContact}
              setFieldValue={form.setFieldValue}
            />
            <PartyAddress
              partType={PartyTypeEnum.Person}
              addresses={form.values.addresses}
              touchedList={form.touched.addresses}
              errorList={form.errors.addresses}
              showPreferredAddress={showPreferredAddress}
              setFieldValue={form.setFieldValue}
            />

            {!props.showHeader && (
              <div styleName="button-row">
                <Button variant="outlined" onClick={() => cancel(form)}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  type="submit"
                  onClick={() => {
                    console.log(form.errors);
                    form.handleSubmit();
                  }}
                >
                  Save
                </Button>
              </div>
            )}
          </div>
        </form>
      )}
    </Formik>
  );
}

export default DriverForm;















