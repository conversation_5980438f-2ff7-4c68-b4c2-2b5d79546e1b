import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import { GetExtendedListPartyCDto, GetExtendedPartyCDto } from "redi-types";
import PartyService from "../../../services/party";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DraggableDialog from "../../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import { RoleTypeEnum } from "../../../enum/RoleTypeCodeEnum";
import './styles.scss';
import DriverForm from "../DriverForm/DriverForm";

export type ManagementItemType = 'Party' | 'Organisation' | 'Person'| 'Profile';
export type ManagementItemDataDto = GetExtendedListPartyCDto;

interface Props {
  itemType: ManagementItemType
}

// currnency format regex
function currencyFormat(num: number) {
  return '$' + num.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
}

function DriverList(props: Props) {
  const navigate = useNavigate();

  const [clickedRowId, setClickedRowId] = useState<string>();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

  // Dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

  const tableHeaders: TableHeader[] = [];
  tableHeaders.push(
    { id: 'Name', label: 'Name', isSortable: true },
    { id: 'StatusCode', label: 'Status', isSortable: true },
    { id: 'driverABN', label: 'ABN' },
    { id: 'PrimaryPhone', label: 'Phone' },
    { id: 'pendingPayment', label: 'Pending Payment', isSortable: true, align: "right" },
    { id: 'Menu', label: 'Actions' }
  );

  function renderTableRow(data: ManagementItemDataDto) {
    const pendingPayment = data?.partyAttributes?.find(x => x.attributeCode === "PendingPayment")?.value ?? "";
    const abn = data?.partyAttributes?.find(x => x.attributeCode === "DriverABN")?.value ?? "";

    return (
      <>
        <TableCell>{data.name}</TableCell>
        <TableCell>{data.statusCode}</TableCell>
        <TableCell>{abn}</TableCell>
        <TableCell>{data.primaryPhone}</TableCell>
        <TableCell align="right">{pendingPayment ? currencyFormat(parseFloat(pendingPayment!)) : 0}</TableCell>
        <TableCell align="right"><RowMenu rowData={data} /></TableCell>
      </>
    );
  }

  /* Delete Dialog */
  function closeDeleteDialog() {
    setIsDeleteDialogOpen(false);
  }

  async function deleteRowItem() {
    closeDeleteDialog();
    if (clickedRowId) {
      const response = await PartyService.Delete(clickedRowId);
      if (!response.error) {
        setShowSnackbar(true);
        setSnackbarMessage('Successfully deleted driver');
        setSnackbarSeverity('success');
        setRefreshTableTrigger(trigger => trigger + 1);
      } else {
        setShowSnackbar(true);
        setSnackbarMessage('Failed to delete driver');
        setSnackbarSeverity('error');
      }
    }
    setClickedRowId(undefined);
  }

  const RowMenu = (props: { rowData: ManagementItemDataDto }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const isOpen = Boolean(anchorEl);
    
    const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(null);
    };

    const handleDelete = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedRowId(props.rowData.partyId);
      setIsDeleteDialogOpen(true);
    };

    return (
      <>
        <IconButton
          id="actions-list"
          aria-controls={isOpen ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={isOpen ? 'true' : undefined}
          onClick={openMenu}
        >
          <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
        </IconButton>
        <Menu
          id="actions-menu"
          anchorEl={anchorEl}
          open={isOpen}
          onClose={handleClose}
          MenuListProps={{ 'aria-labelledby': 'menu-button' }}
        >
          <MenuItem onClick={handleDelete}>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>
      </>
    );
  }

  function renderPartyDataTable() {
    return (
      <DataTable<ManagementItemDataDto, "partyId">
        primaryKeyProperty="partyId"
        title="Drivers"
        tableId={'base-' + ("" + props.itemType).toLowerCase() + '-'}
        pageSize={10}
        initialSortColumn="Name"
        refreshTableTrigger={refreshTableTrigger}
        tableHeight={540}
        addButtonLabel="Add Driver"
        addButtonOnClick={() => setIsAddDialogOpen(true)}
        tableHeaders={tableHeaders}
        renderTableRow={renderTableRow}
        onRowClick={(row) => navigate(`/Driver/${row.partyId}`)}
        filterOptions={['All', 'Active', 'Inactive']}
        callService={(inListParameters, inSearch, inFilter) => {
          const _inFilter = inFilter === 'All' ? '' : inFilter;
          return PartyService.List(inListParameters, undefined, _inFilter, inSearch, "Driver", true, true, true);
        }}
      />
    );
  }

  function renderYesNoPartyDialog() {
    return (
      <YesNoDialog
        title={"Delete " + RoleTypeEnum.Driver}
        bodyText="Are you sure? This cannot be reversed"
        isOpen={isDeleteDialogOpen}
        onNo={closeDeleteDialog}
        onYes={deleteRowItem}
      />
    );
  }

  function closeAddDialog() {
    setIsAddDialogOpen(false);
  }

  function onAddDialogSave(data: GetExtendedPartyCDto) {
    // TODO: Implement save logic
    closeAddDialog();
    setRefreshTableTrigger(prev => prev + 1);
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        {renderPartyDataTable()}
        {renderYesNoPartyDialog()}
        <DraggableDialog
          maxWidth="lg"
          title="Add New Driver"
          isOpen={isAddDialogOpen}
          onCancel={closeAddDialog}
        >
          <DriverForm
            onCancel={closeAddDialog}
            onSave={onAddDialogSave}
          />
        </DraggableDialog>
        <Snackbar open={showSnackbar}
          autoHideDuration={4000}
          onClose={() => setShowSnackbar(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default DriverList;
