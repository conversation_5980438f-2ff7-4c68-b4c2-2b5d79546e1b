import { useState } from "react";
import { GetPartyCDto } from "redi-types";
import PartyService from "../../../services/party";
import { IconButton, ListItemText, Menu, MenuItem } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import "./styles.scss";
import { DriverStatusCodeEnum } from "../../../enum/DriverStatusCodeEnum";

interface Props {
    driver: GetPartyCDto;
    onSave: (data: GetPartyCDto) => void;
}

function DriverActionButtons(props: Props) {
    const { driver, onSave } = props;
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState<string>();
    
    const open = Boolean(anchorEl);
    
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    
    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleStatusSelect = (status: string) => {
        setSelectedStatus(status);
        setShowConfirmDialog(true);
        handleClose();
    };

    const handleStatusChange = async () => {
        if (selectedStatus) {
            const updatedDriver = { ...driver, statusCode: selectedStatus };
            onSave(updatedDriver);
        }
        setShowConfirmDialog(false);
    };

    return(
        <>
            <IconButton
                id="actions-list"
                aria-controls={open ? 'case-details-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
            >
                <FontAwesomeIcon styleName="menu-icon" icon="ellipsis-v" />
            </IconButton>
            <Menu
                id="actions-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{"aria-labelledby": "menu-button"}}
            >
                {driver.statusCode === DriverStatusCodeEnum.Active ?
                <MenuItem onClick={() => handleStatusSelect(DriverStatusCodeEnum.Inactive)}>
                    <ListItemText>Set Inactive</ListItemText>
                </MenuItem> : null}
                {driver.statusCode === DriverStatusCodeEnum.Inactive ?
                <MenuItem onClick={() => handleStatusSelect(DriverStatusCodeEnum.Active)}>
                    <ListItemText>Set Active</ListItemText>
                </MenuItem> : null}
            </Menu>
            <YesNoDialog
                isOpen={showConfirmDialog}
                title="Confirm Status Change"
                bodyText={`Are you sure you want to change the status to ${selectedStatus}?`}
                onYes={handleStatusChange}
                onNo={() => setShowConfirmDialog(false)}
            />
        </>
    );
}

export default DriverActionButtons;