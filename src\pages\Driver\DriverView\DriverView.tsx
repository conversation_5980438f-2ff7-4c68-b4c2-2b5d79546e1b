import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconButton } from "@mui/material";
import { useMemo } from "react";
import { GetExtendedPartyCDto } from "redi-types";
import "./styles.scss";
import DriverActionButtons from "./DriverActionButtons";

interface Props {
  driver: GetExtendedPartyCDto;
  onCancel: () => void;
  onSave: (data: GetExtendedPartyCDto) => void;
}

function DriverView(props: Props) {
  const { driver, onSave, onCancel } = props;

  const currentAbn = useMemo(() => {
    if (driver.partyAttributes && driver.partyAttributes.length > 0) {
      const abn = driver.partyAttributes.find(x => x.attributeCode === "DriverABN");
      return abn?.value ?? "";
    }
    return null;
  }, [driver.partyAttributes]);

  const currentNote = useMemo(() => {
    if (driver.partyAttributes && driver.partyAttributes.length > 0) {
      const note = driver.partyAttributes.find(x => x.attributeCode === "DriverNote");
      return note?.value ?? "";
    }
    return null;
  }, [driver.partyAttributes]);

  return (
    <div styleName="container">
      <div styleName="row">
        <div styleName="header">Driver Details</div>
        <div styleName="row">
          <IconButton onClick={onCancel}>
            <FontAwesomeIcon icon="pen-to-square" />
          </IconButton>
          <DriverActionButtons driver={driver} onSave={onSave} />
        </div>
      </div>
      <div styleName="grid">
        <div styleName="column">
          <div styleName="label">Name</div>
          <div styleName="value">{driver.person?.fullName ?? "N/A"}</div>
        </div>
        <div styleName="column">
          <div styleName="label">ABN</div>
          <div styleName="value">{currentAbn ?? "N/A"}</div>
        </div>
        <div styleName="column">
          <div styleName="label">Status</div>
          <div styleName="value">{driver.statusCode ?? "N/A"}</div>
        </div>
        <div styleName="column">
          <div styleName="label">Deductions</div>
          {driver.deductions?.map((deduction) =>
            <div styleName="value" key={deduction.deductionId}>{deduction.type + " $" + deduction.amount.toFixed(2)}</div>
          )}
        </div>
        <div styleName="column">
          <div styleName="label">Phone</div>
          <div styleName="value">{driver.primaryPhone ?? "N/A"}</div>
        </div>
        <div styleName="column">
          <div styleName="label">Address</div>
          <div styleName="value">{driver.primaryAddress ?? "N/A"}</div>
        </div>
        <div styleName="column">
          <div styleName="label">Email</div>
          <div styleName="value">{driver.primaryEmail ?? "N/A"}</div>
        </div>
        <div styleName="column">
          <div styleName="label">Note</div>
          <div styleName="value">{currentNote ?? "N/A"}</div>
        </div>
      </div>
    </div>
  );
}

export default DriverView;