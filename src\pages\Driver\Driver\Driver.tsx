import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { GetExtendedPartyCDto } from "redi-types";
import PartyService from "../../../services/party";
import { CircularProgress } from "@mui/material";
import DriverHeader from "./DriverHeader/DriverHeader";
import LoadingDataComponent from "../../../components/LoadingDataComponent/LoadingDataComponent";
import './styles.scss';
import DriverDetails from "../DriverDetails/DriverDetails";
import { DriverEvents } from "../DriverEvents/DriverEvents";

interface Props {
}

function Driver(props: Props) {
    const { id } = useParams();
    const [tabIndex, setTabIndex] = useState(0);
    const [driver, setDriver] = useState<GetExtendedPartyCDto>();
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (id) {
            PartyService.Get(id, true, true).then((data) => {
                if (!data.error && data.data) {
                    setDriver(data.data);
                }
                setIsLoading(false);
            })
        }
    }, [id]);

    return (
        <div styleName="page">
            {
                isLoading
                    ?
                    <div styleName="loading">
                        <CircularProgress color="primary" size={50} />
                    </div>
                    :
                    <>
                        <div styleName="container">
                            <div styleName="content">
                                <LoadingDataComponent isLoading={isLoading} data={driver}>
                                    <DriverHeader driver={driver!} />
                                </LoadingDataComponent>
                                <div styleName="main-content">
                                    <div styleName="row">
                                        <div styleName={`tab ${tabIndex === 0 ? 'active' : ''}`} onClick={() => setTabIndex(0)}>
                                            Driver
                                        </div>
                                        <div 
                                            styleName={`tab ${tabIndex === 1 ? 'active' : ''}`} 
                                            onClick={() => setTabIndex(1)}
                                        >
                                            Events
                                        </div>
                                    </div>
                                    <div styleName="tab-body">
                                        <div styleName="tab-grid-1" style={{ display: tabIndex === 0 ? 'grid' : 'none' }}>
                                            <div>
                                                <LoadingDataComponent isLoading={isLoading} data={driver}>
                                                    <DriverDetails
                                                        id={id}
                                                        showHeader
                                                        driver={driver!}
                                                        onSave={(value) => setDriver(value)}
                                                    />
                                                </LoadingDataComponent>
                                            </div>
                                        </div>
                                        <div style={{ display: tabIndex === 1 ? 'block' : 'none' }}>
                                            <LoadingDataComponent isLoading={isLoading} data={driver}>
                                                <DriverEvents driverId={id!} />
                                            </LoadingDataComponent>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </>
            }
        </div>
    )
}

export default Driver;