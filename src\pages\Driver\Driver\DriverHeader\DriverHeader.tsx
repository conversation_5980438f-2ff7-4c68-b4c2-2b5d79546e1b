import { GetExtendedPartyCDto } from "redi-types";
import './styles.scss';
import { useMemo } from "react";

function DriverHeader(props: Props) {
    const { driver } = props;
    
    const currentAbn = useMemo(() => {
        if (driver.partyAttributes && driver.partyAttributes.length > 0) {
            const abn = driver.partyAttributes.find(x => x.attributeCode === "DriverABN");
            return abn?.value ?? "";
        }
        return "0";
    }, [driver.partyAttributes]);

    return (
        <div styleName="summary-bar">
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{driver.name ?? "N/A"}</div>
                    <div styleName="bottom">Name</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{currentAbn ?? "N/A"}</div>
                    <div styleName="bottom">ABN</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{driver.statusCode ?? "N/A"}</div>
                    <div styleName="bottom">Status</div>
                </div>
            </div>
        </div>
    );
}

export default DriverHeader;

interface Props {
    driver: GetExtendedPartyCDto;
}