@import "../../../../config/theme/vars.scss";


.summary-bar {
  background-color: #E7E7E7;
  border-radius: 15px;
  margin: 1rem;
  padding: 0.8rem 0;
  display: flex;

  .column {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: left;
    padding-left: 33px;
    text-align: center;
  }

  .top {
    font-size: 16px;
    color: $primaryColor;
    font-weight: 600;
  }

  .middle {
    font-size: 16px;
    color: $primaryColor;
    font-weight: 600;
  }

  .bottom {
    font-size: 13px;
    color: #4E4949;
    font-weight: 600;
  }

  .menu-icon {
    height: 25px;
    width: 25px;
    padding: 0;
    color: rgba(0, 0, 0, 0.6);
  }
}

.align-left {
  text-align: left;
}