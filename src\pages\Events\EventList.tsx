import { TableCell } from "@mui/material";
import { EventListCDto } from "redi-types";
import './styles.scss';
import DataTable, { TableHeader } from "../../components/DataTable/DataTable";
import { dateFormatOptions } from "../../utils/dateFormOptions";
import EventService from "../../services/event";

interface Props {
  parentEntityId?: string;
  parentEntityType?: string;
  parentEntityIntId?: number;
  parentEntityId2?: string;
  parentEntityType2?: string;
  parentEntityIntId2?: number;
}

function EventList(props: Props) {
  const tableHeaders: TableHeader[] = [
    { id: 'createdByName', label: 'User', isSortable: true },
    { id: 'createdOn', label: 'Date', isSortable: true },
    { id: 'description', label: 'Description' }
  ];

  function renderTableRow(data: EventListCDto) {
    return (
      <>
        <TableCell>
          <div><b>{data.createdByName || 'N/A'}</b></div>
        </TableCell>
        <TableCell>
          {
            data.createdOn
              ? new Date(data.createdOn).toLocaleDateString('en-AU', dateFormatOptions)
              : 'N/A'
          }
        </TableCell>
        <TableCell>{data.description}</TableCell>
      </>
    );
  }

  return (
    <DataTable<EventListCDto, "eventId">
      primaryKeyProperty="eventId"
      title="Events"
      tableId='base-events'
      pageSize={10}
      initialSortColumn="-CreatedOn"
      tableHeight={540}
      tableHeaders={tableHeaders}
      showFilters={false}
      renderTableRow={renderTableRow}
      callService={(inListParameters, inSearch) => 
        EventService.List(
          inListParameters,
          props.parentEntityId,
          props.parentEntityType,
          props.parentEntityIntId,
          props.parentEntityId2,
          props.parentEntityType2,
          props.parentEntityIntId2,
          inSearch
        )
      }
    />
  );
}

export default EventList;
