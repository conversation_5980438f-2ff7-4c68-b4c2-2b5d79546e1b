import config from '../../config/config';
import './styles.scss';

function Home(props: Props) {
    return (
      <div style={{backgroundColor: 'green', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' , textAlign: 'center',  height: '1200px'}}>
        <div style={{color: 'white', fontSize: '30px'}}>Home Page</div>
        <div style={{color: 'white', fontSize: '30px'}}>Config Example - API Url: {config.apiURL}</div>
      </div>
    )
}

export default Home;

interface Props {
    title: string;
}