import React from 'react';
import config from '../../config/config';
import logo from './../../assets/logo.svg';
import './styles.css';
import './styles.scss';

function Home(props: Props) {
    return (
        <div className="App">
            <header className="App-header">
            <img src={logo} className="App-logo" alt="logo" />
            <p>
                {props.title}
            </p>
            <a 
                className="App-link-2"
                href="https://reactjs.org"
                target="_blank"
                rel="noopener noreferrer"
            >
                Learn React
            </a>
            <div>
                API EXAMPLE: {config.apiURL}
            </div>
            </header>
      </div>
    )
}

export default Home;

interface Props {
    title: string;
}