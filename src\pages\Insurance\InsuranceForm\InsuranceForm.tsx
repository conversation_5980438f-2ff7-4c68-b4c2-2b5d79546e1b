import { BaseOrganisationCDto} from "redi-types";
import { ManagementItemType } from "../InsuranceList/InsuranceList";
import * as yup from "yup";
import { NIL as NIL_UUID, v4 as uuidv4 } from "uuid";
import { Field, Formik, FormikErrors, FormikHelpers, FormikProps, FormikTouched } from "formik";
import { useEffect, useState } from "react";
import PartyService from "../../../services/party";
import OrganisationTypeService from "../../../services/organisationType";
import RoleTypeService from "../../../services/roleType";
import { Button, IconButton, TextField } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import './styles.scss';
import { GetExtendedPartyCDto } from "redi-types";

interface Props {
    id?: string;
    initialValues: GetExtendedPartyCDto;
    itemType: ManagementItemType;
    relatedPartyId?: string;
    relatedPartyRoleTypeCode?: string;
    onCancel?: () => void;
  onSave?: (data: GetExtendedPartyCDto) => void;
    forceOrganisationTypeCode?: boolean;
    forceRoleTypeCode?: boolean;
    showHeader?: boolean;
}

function InsuranceForm(props: Props) {

    const { initialValues } = props;

  const [party, setParty] = useState<GetExtendedPartyCDto>(initialValues);
    const [isLoading, setIsLoading] = useState(false);
    const [hideOrganisationType, setHideOrganisationType] = useState(false);
    const [hideRoleType, setHideRoleType] = useState(false);

    const schema = yup.object({
        organisation: yup.object({
            name: yup.string().required("Enter name"),
        })
    })

    useEffect(() => {
      // Get if editing (has id prop)
      (async () => {
        if (props.id) {
          const partyResponse = await PartyService.Get(props.id, true, true);
          if (!partyResponse.error && partyResponse.data) {
            setParty(partyResponse.data);
            if (partyResponse.data.partyAttributes) {
              //setDeductions(partyResponse.data?.partyAttributes)
            }
          }
        }
      })();
    }, []);

    function cancel(form: FormikProps<GetExtendedPartyCDto>) {
        form.resetForm();
        props.onCancel && props.onCancel();
      }

    async function save(
        formData: GetExtendedPartyCDto,
        actions: FormikHelpers<GetExtendedPartyCDto>
      ) {
        setIsLoading(true);
        // --- Left over logic from before rebuild
        if (formData.partyType != "Organisation") {
          formData.organisation = undefined;
        }
        // ---
    
        if (props.id) {
          /* Update */
          const response = await PartyService.Update(formData);
          if (!response.error && response.data) {
            props.onSave && props.onSave(response.data);
          } 
        } else {
          /* Create */
          const response = await PartyService.Create(
            formData,
            formData.roleTypeCode as string,
            props.relatedPartyId,
            props.relatedPartyRoleTypeCode
          );
          if (!response.error && response.data) {
            props.onSave && props.onSave(response.data);
          } 
        }
        setIsLoading(false);
    }
    return(
        <Formik<GetExtendedPartyCDto>
            enableReinitialize
            validationSchema={schema}
            initialValues={party}
            onSubmit={async (data, actions) => {
                await save(data, actions);
            }}
        >
            {(form) => {
                return (
                    <form onSubmit={form.handleSubmit}>
                        <div styleName="container">
                        {props.showHeader && (
                            <div styleName="row">
                            <div styleName="header">Driver Details</div>
                            <div styleName="row">
                                <IconButton onClick={() => form.handleSubmit()}>
                                <FontAwesomeIcon icon="check" />
                                </IconButton>
                                <IconButton onClick={() => cancel(form)}>
                                <FontAwesomeIcon icon="close" />
                                </IconButton>
                            </div>
                            </div>
                        )}
                        <div styleName="form-grid">
                            {form.values.partyType == "Organisation" && (
                            <div styleName="row">
                                <div styleName="field-container size-2">
                                <Field
                                    variant="standard"
                                    id={`organisation.name`}
                                    name={`organisation.name`}
                                    label={"Name"}
                                    as={TextField}
                                    error={
                                      (
                                        form.touched
                                          .organisation as unknown as FormikTouched<BaseOrganisationCDto>
                                      )?.name &&
                                      Boolean(
                                        (
                                          form.errors
                                            .organisation as FormikErrors<BaseOrganisationCDto>
                                        )?.name
                                      )
                                    }
                                    helperText={
                                      (
                                        form.touched
                                          .organisation as unknown as FormikTouched<BaseOrganisationCDto>
                                      )?.name &&
                                      (
                                        form.errors
                                          .organisation as FormikErrors<BaseOrganisationCDto>
                                      )?.name
                                    }
                                />
                                </div>
                            </div>
                            )}
                            </div>
                            {!props.showHeader && (
                            <div styleName="button-row">
                                <Button variant="outlined" onClick={() => cancel(form)}>
                                Cancel
                                </Button>
                                <Button
                                variant="contained"
                                type="submit"
                                onClick={() => form.handleSubmit}
                                >
                                Save
                                </Button>
                            </div>
                            )}
                        </div>
                    </form>
                )
            }}
        </Formik>
    )
}

export default InsuranceForm;