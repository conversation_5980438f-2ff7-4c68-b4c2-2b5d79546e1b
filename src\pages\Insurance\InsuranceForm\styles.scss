@import "../../../config/theme/vars.scss";

.row {
  display: flex;
  flex-direction: row;
}
.column {
  display: flex;
  flex-direction: column;
}

.trashicon {
  margin-top: 1.5rem;
  margin-left: 2rem;
  margin-right: 5rem;
}
.field-container {
  margin-left: 4px;
  margin-right: 4px;
  &:first-child {
    margin-left: 0px;
  }
  &:last-child {
    margin-right: 0px;
  }
  > div {
    display: flex;
    flex: 1;
  }
}
.false-field-container {
  margin-left: 4px;
  margin-right: 4px;
  &:first-child {
    margin-left: 0px;
  }
  &:last-child {
    margin-right: 0px;
  }
}
.false-field-container.size-1,
.field-container.size-1 {
  flex: 1;
}
.false-field-container.size-2,
.field-container.size-2 {
  flex: 2;
}
.false-field-container.size-3,
.field-container.size-3 {
  flex: 3;
}
.false-field-container.size-4,
.field-container.size-4 {
  flex: 4;
}
.false-field-container.size-5,
.field-container.size-5 {
  flex: 5;
}

.new-phone-container {
  justify-content: flex-start;
  align-items: flex-end;
}
.new-email-container {
  justify-content: flex-start;
  align-items: flex-end;
}
.card-fields-container {
  background-color: $primaryBgColor;
  padding: 16px;
  border-radius: 8px;

  margin-left: 4px;
  margin-right: 4px;
  &:first-child {
    margin-left: 0px;
  }
  &:last-child {
    margin-right: 0px;
  }
}
.phone-row-container,
.email-row-container {
  padding-bottom: 10px;
}
.address-row-container {
  padding-bottom: 20px;
}
.address-field-gap {
  padding-bottom: 8px;
}
.card-fields-container.size-1 {
  flex: 1;
}
.field-container-title {
  text-align: center;
  color: $primaryColor;
  font-weight: 700;
  font-size: 0.7rem;
}
.field-container-action-button {
  text-align: center;
  color: $primaryColor;
  font-weight: 700;
  font-size: 1.2rem;
  cursor: pointer;
}
.action-button {
  display: flex;
  align-items: center;
  color: $primaryColor;
  cursor: pointer;

  svg {
    font-size: 1.2rem;
    margin-right: 0.5rem;
  }

  div {
    font-weight: 500;
    font-size: 1rem;

  }
}
.center-justify {
  justify-content: center;
}
.center-align {
  align-items: center;
}

.container {
  display: flex;
  flex-direction: column;

  >.row {
    justify-content: space-between;
    margin-bottom: 1.25rem;

    .header {
      color: $primaryColor;
      font-size: 1.3rem;
      font-weight: 600;
    }

    button {
      color: $primaryColor;

      .menu-icon {
        height: 1.25rem;
        width: 1.25rem;
        font-size: 1.25rem;
        padding: 0;
        color: rgba(0, 0, 0, 0.6);
      }
    }

    svg {        
      font-size: 1.1rem;
      width: 1.1rem;
      height: 1.1rem;
    }    
  }

  .grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;

    >.column {
      color: #4E4949;
      font-size: 1rem;

      .label {
        font-weight: 600;
      }

      .value {
        font-weight: 400;
      }

      .green {
        color: $green !important;
      }
      
      .red {
        color: $red !important;
      }
    }
  }
}

// Edit
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0 2rem;
  padding-top: 1rem;

  // Stop form field's validation growing/shinking the dialog
  & > div {
    min-height: 72px;
  }

  & > label {
    margin: 0 0 1.8rem 0;
  }
}

.button-row {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}