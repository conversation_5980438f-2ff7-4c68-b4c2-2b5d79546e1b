import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import { GetExtendedPartyCDto, InsurancePartyCDto } from "redi-types";
import PartyService from "../../../services/party";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DraggableDialog from "../../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import { RoleTypeEnum } from "../../../enum/RoleTypeCodeEnum";
import './styles.scss';
import InsuranceForm from "../InsuranceForm/InsuranceForm";
import { PartyTypeEnum } from "../../../enum/PartyTypeEnum";

export type ManagementItemType = 'Insurance';
export type ManagementItemDataDto = GetExtendedPartyCDto;

interface Props {
  itemType: ManagementItemType
}

function InsuranceList(props: Props) {
  const navigate = useNavigate();

  const [clickedRowId, setClickedRowId] = useState<string>();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

  // Dialog
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

  const initialValuesRef = useRef(PartyService.getDefaultValues(PartyTypeEnum.Organisation, RoleTypeEnum.Insurance, undefined, false));
  const initialValues = initialValuesRef.current;

  const tableHeaders: TableHeader[] = [];
  tableHeaders.push(
    { id: 'Name', label: 'Name', isSortable: true },
    { id: 'Menu', label: 'Actions' },
  );

  function renderTableRow(data: ManagementItemDataDto) {
    return (
      <>
        <TableCell>{data.name}</TableCell>
        <TableCell><RowMenu rowData={data} /></TableCell>
      </>
    )
  }

  function openAddDialog() {
    setIsAddDialogOpen(true);
  }

  function closeAddDialog() {
    setIsAddDialogOpen(false);
  }

  async function onAddDialogSave() {
    setRefreshTableTrigger(trigger => trigger + 1);
    setIsAddDialogOpen(false);
  }

  /* Delete Dialog */

  function closeDeleteDialog() {
    setIsDeleteDialogOpen(false);
  }

  async function deleteRowItem() {
    closeDeleteDialog();
    if (clickedRowId) {
      const response = await PartyService.Delete(clickedRowId);
      if (!response.error) {
        setShowSnackbar(true);
        setSnackbarMessage('Successfully deleted ' + ("" + props.itemType).toLowerCase());
        setSnackbarSeverity('success');
        // Refresh table data
        setRefreshTableTrigger(trigger => trigger + 1);
      } else {
        setShowSnackbar(true);
        setSnackbarMessage('Failed to delete ' + ("" + props.itemType).toLowerCase());
        setSnackbarSeverity('error');
      }
    }
    setClickedRowId(undefined);
  }

  const RowMenu = (props: { rowData: ManagementItemDataDto }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const isOpen = Boolean(anchorEl);
    const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(null);
    };
    const handleDelete = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedRowId(props.rowData.partyId);
      setIsDeleteDialogOpen(true);
    };
    const handleEdit = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedRowId(props.rowData.partyId);
      setIsAddDialogOpen(true);
    };

    return (
      <>
        <IconButton
          id="actions-list"
          aria-controls={isOpen ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={isOpen ? 'true' : undefined}
          onClick={openMenu}
        >
          <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
        </IconButton>
        <Menu
          id="actions-menu"
          anchorEl={anchorEl}
          open={isOpen}
          onClose={handleClose}
          MenuListProps={{ 'aria-labelledby': 'menu-button' }}
        >
          <MenuItem onClick={handleEdit}>
            <ListItemText>Edit</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDelete}>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>
      </>
    );
  }

  function renderPartyDataTable() {
    return (
      <DataTable<ManagementItemDataDto, "partyId">
        primaryKeyProperty="partyId"
        title={"Insurance Companies"}
        tableId={'base-' + ("" + props.itemType).toLowerCase() + '-'}
        pageSize={10}
        initialSortColumn="Name"
        refreshTableTrigger={refreshTableTrigger}
        tableHeight={540}
        addButtonLabel={"New Insurance"}
        addButtonOnClick={() => {
          setClickedRowId("")
          openAddDialog()
        }}
        tableHeaders={tableHeaders}
        renderTableRow={renderTableRow}
        filterOptions={['All']}
        callService={(inListParameters, inSearch, inFilter) => {
          const _inFilter = inFilter === 'All' ? '' : inFilter;
          return PartyService.ListOrganisations(inListParameters, inSearch, "Insurance", undefined, _inFilter, true, true);
        }}
      />
    )
  }
  function renderAddPartyDialog() {
    {/* Add Dialog */ }
    return (
      <DraggableDialog
        maxWidth="xl"
        title={`${clickedRowId === undefined ? 'Add New' : 'Edit'} Insurance`}
        isOpen={isAddDialogOpen}
        onCancel={closeAddDialog}
      >
        <InsuranceForm
          id={clickedRowId}
          onCancel={closeAddDialog}
          onSave={onAddDialogSave}
          initialValues={initialValues}
          itemType="Insurance"
        />
      </DraggableDialog>
    );
  }

  function renderYesNoPartyDialog() {
    {/* Delete Dialog */ }
    return (
      <YesNoDialog
        title={"Delete " + RoleTypeEnum.Insurance}
        bodyText="Are you sure? This cannot be reversed"
        isOpen={isDeleteDialogOpen}
        onNo={closeDeleteDialog}
        onYes={deleteRowItem}
      />
    );
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        {renderPartyDataTable()}

        {renderAddPartyDialog()}

        {renderYesNoPartyDialog()}

        <Snackbar open={showSnackbar}
          autoHideDuration={4000}
          onClose={() => setShowSnackbar(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default InsuranceList;