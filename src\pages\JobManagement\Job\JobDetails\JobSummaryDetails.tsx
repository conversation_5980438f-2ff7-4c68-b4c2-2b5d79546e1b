import { CircularProgress } from "@mui/material";
import { useEffect, useState } from "react";
import { GetJobCDto } from "redi-types";
import JobService from "../../../../services/job";
import './styles.scss';

interface Props {
  jobId: string;
}

function JobSummaryDetails(props: Props) {
  const [job, setJob] = useState<GetJobCDto | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchJobData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await JobService.Get(props.jobId);
        
        if (!response.error && response.data) {
          setJob(response.data);
        } else {
          setError('Failed to load job data');
        }
      } catch (err) {
        setError('An error occurred while fetching job data');
      } finally {
        setIsLoading(false);
      }
    };

    if (props.jobId) {
      fetchJobData();
    }
  }, [props.jobId]);

  if (isLoading) {
    return (
      <div styleName="loading-container">
        <CircularProgress color="primary" size={50} />
      </div>
    );
  }

  if (error) {
    return (
      <div styleName="error-container">
        <div>Error: {error}</div>
      </div>
    );
  }

  if (!job) {
    return (
      <div styleName="error-container">
        <div>No job data available</div>
      </div>
    );
  }

  return (
    <div styleName="container">
      <div styleName="sub-header">Job Summary</div>

      <div styleName="grid">
        <div styleName="column">
          <div styleName="label">Job Reference</div>
          <div styleName="value">{job.jobReference}</div>
        </div>

        <div styleName="column">
          <div styleName="label">Invoice Number</div>
          <div styleName="value uppercase">{job.invoiceNumber}</div>
        </div>

        <div styleName="column">
          <div styleName="label">Job Date</div>
          <div styleName="value">
            {job?.createdOn
              ? new Date(job.createdOn).toLocaleDateString('en-AU')
              : 'N/A'}
          </div>
        </div>

        <div styleName="column">
          <div styleName="label">Insurance</div>
          <div styleName="value">{job.insuranceCompanyName}</div>
        </div>

        <div styleName="column">
          <div styleName="label">Status</div>
          <div styleName="value">{job.statusCode}</div>
        </div>
      </div>
    </div>
  );
}

export default JobSummaryDetails;
