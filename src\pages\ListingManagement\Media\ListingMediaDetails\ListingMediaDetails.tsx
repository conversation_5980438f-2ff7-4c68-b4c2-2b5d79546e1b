import { useState } from "react";
import { ListingMediaCDto } from "redi-types";
import { ListingMediaForm } from "../ListingMediaForm/ListingMediaForm";
import { ListingMediaView } from "../ListingMediaView/ListingMediaView";

interface Props {
  media: ListingMediaCDto;
  list: ListingMediaCDto[];
  onSave?: (value?: ListingMediaCDto) => void;
}

export function ListingMediaDetails(props: Props) {
  const { media, onSave } = props;
  const [isEdit, setIsEdit] = useState(false);
  return isEdit ? (
    <ListingMediaForm
      list={props.list}
      initialValues={media}
      onCancel={() => setIsEdit(false)}
      onSave={(data) => {
        setIsEdit(false);
        onSave && onSave(data);
      }}
    />
  ) : (
    <ListingMediaView
      media={media}
      onCancel={() => setIsEdit(true)}
      onSave={() => onSave && onSave()}
    />
  );
}
