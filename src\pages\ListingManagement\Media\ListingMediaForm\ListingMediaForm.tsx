import { useEffect, useState } from "react";
import { ListingMediaCDto } from "redi-types";
import { useParams } from "react-router-dom";
import { Field, Formik, FormikProps } from "formik";
import { IconButton, TextField } from "@mui/material";
import { DisplayAttributeField } from "../../../components/DisplayAttribute/DisplayAttributeField";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "./styles.scss";
import ManageService from "../../../../services/manage";

interface Props {
  initialValues: ListingMediaCDto;
  list: ListingMediaCDto[];
  onCancel?: () => void;
  onSave?: (value: ListingMediaCDto) => void;
}

export function ListingMediaForm(props: Props) {
  const [initialValues, setInitialValues] = useState<ListingMediaCDto>(
    props.initialValues
  );

  async function save(formData: ListingMediaCDto) {
    const medias = props.list.map((media) => {
      if (media.listingMediaId === formData.listingMediaId) {
        return formData;
      } else {
        return media;
      }
    });
    ManageService.updateMedia(initialValues.listingId!, medias);
    props.onSave && props.onSave(formData);
  }

  function handleCancel(form: FormikProps<ListingMediaCDto>) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  return (
    <Formik<ListingMediaCDto>
      initialValues={initialValues}
      onSubmit={async (data) => {
        await save(data);
      }}
    >
      {(form) => {
        return (
          <form onSubmit={form.handleSubmit}>
            <div styleName="container">
              <div styleName="row">
                <div styleName="header">Media Details</div>
                <div styleName="row">
                  <IconButton onClick={() => form.handleSubmit()}>
                    <FontAwesomeIcon icon="check" />
                  </IconButton>
                  <IconButton onClick={() => handleCancel(form)}>
                    <FontAwesomeIcon icon="close" />
                  </IconButton>
                </div>
              </div>
            </div>
            <div styleName="field">
              <Field
                fullWidth
                variant="outlined"
                id={`title`}
                name={`title`}
                label={"Title"}
                as={TextField}
                onBlur={(event: any) => {
                  form.setFieldTouched(event.target.name, true, true);
                }}
              />
            </div>
          </form>
        );
      }}
    </Formik>
  );
}
