import {
  ListingMediaCDto,
  ManageListingWithDisplayGroupedDataAndMediaDto,
} from "redi-types";
import { InputTypeCodeEnum } from "../../../../enum/InputTypeCodeEnum";
import { DisplayAttribute } from "../../../../components/DisplayAttribute/DisplayAttribute";
import { Button, IconButton } from "@mui/material";
import { LoadScript } from "@react-google-maps/api";
import config from "../../../../config/config";
import { StatusUpdateMenu } from "../StatusUpdateMenu/StatusUpdateMenu";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import "./styles.scss";

interface Props {
  media: ListingMediaCDto;
  onCancel: () => void;
  onSave: () => void;
}

export function ListingMediaView(props: Props) {
  const { media } = props;

  function handleCancel() {
    props.onCancel && props.onCancel();
  }

  return (
    <>
      <div styleName="container">
        <div styleName="row">
          <div styleName="header">Media Details</div>
          <div styleName="row">
            <IconButton onClick={handleCancel}>
              <FontAwesomeIcon icon="pen-to-square" />
            </IconButton>
          </div>
        </div>
      </div>
      <div styleName="displaycontainer">
        <div styleName="displaycontainer-title">Title</div>
        <div styleName="attributeRow">{media.title}</div>
      </div>
    </>
  );
}
