@import "../../../../config/theme/vars.scss";

.row {
  display: flex;
  align-items: center;
}

.attributeRow {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.column {
  display: flex;
  flex-direction: column;
}

.displaycontainer-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.2rem;
  color: $primaryColor;
  margin-bottom: 0.5rem;
}

.displaycontainer {
  margin-bottom: 1rem;
}

.lowercase {
  text-transform: lowercase;
}

.container {
  display: flex;
  flex-direction: column;

  >.row {
    justify-content: space-between;
    margin-bottom: 1.25rem;

    .header {
      color: $primaryColor;
      font-size: 1.3rem;
      font-weight: 600;
    }

    button {
      // Edit Icon
      svg {
        color: $primaryColor; 
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }

      .menu-icon {
        height: 1.25rem;
        width: 1.25rem;
        font-size: 1.25rem;
        padding: 0;
        color: rgba(0, 0, 0, 0.6);
      }
    }  
  }

  .grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;

    >.column {
      color: #4E4949;
      font-size: 1rem;

      .label {
        font-weight: 600;
      }

      .value {
        font-weight: 400;
      }

      .green {
        color: $green !important;
      }
      
      .red {
        color: $red !important;
      }
    }
  }
}

// Edit
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0 2rem;
  padding-top: 1rem;

  // Stop form field's validation growing/shinking the dialog
  & > div {
    min-height: 72px;
  }

  & > label {
    margin: 0 0 1.8rem 0;
  }
}