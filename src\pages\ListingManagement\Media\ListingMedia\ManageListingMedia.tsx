import { useParams } from "react-router-dom";
import "./styles.scss";
import {
  ListingMediaCDto,
  ManageListingMediaCDto,
  ManageListingWithDataAndMediaCDto,
  ManageListingWithDisplayGroupedDataAndMediaDto,
  ViewTypeOptions,
} from "redi-types";
import { useEffect, useState } from "react";
import ImageCarousel from "../../../../components/ImageCarousel/ImageCarousel";
import LightBoxWrapper from "../../../../components/LightBox/LightBox";
import ManageService from "../../../../services/manage";
import { UploadListingMedia } from "../UploadListingMedia/UploadListingMedia";

interface Props {
  hideHeroImage?: boolean;
  hideTitle?: boolean;
  titleAlign?: string;
  hideFavouriteButton?: boolean;
  hideAttributes?: boolean;
  hideAttributeLabel?: boolean;
  attributesCols?: number;
  hideViewDetailsButton?: boolean;
  hideContactButton?: boolean;
  options?: ViewTypeOptions;
  returnMediaDto?: (mediaDto: ManageListingMediaCDto) => void;
  initialValues?: ManageListingWithDisplayGroupedDataAndMediaDto;
  isEdit?: boolean;
}

export default function ManageListingMedia(props: Props) {
  const { id } = useParams();
  const [listing, setListing] = useState<
  ManageListingWithDisplayGroupedDataAndMediaDto | undefined
  >(props.initialValues);
  const [openLightbox, setOpenLightbox] = useState(false);
  const [refresh, setRefresh] = useState(1);

  useEffect(() => {
    getListingData();
  }, [refresh, id]);

  function refreshData() {
    setRefresh(refresh + 1);
  }

  function updateProfile(profileMedia?: ListingMediaCDto) {
    if (listing) {
      let newListing: ManageListingWithDisplayGroupedDataAndMediaDto = listing;
      newListing.profileListingMediaId = profileMedia?.listingMediaId;
      newListing.profileListingMediaUrl = profileMedia?.mediaUrl;
      updateListing(newListing);
    }
  }

  async function getListingData() {
    if (id) {
      await ManageService.get(Number(id)).then((result) => {
        if (result.data) {
          setListing(result.data);
        }
      });
    }
  }

  async function createMediaListing(mediaUrl?: string) {
    if (mediaUrl) {
      let newListingMedia: ManageListingMediaCDto = {
        listingId: 0,
        listingMediaId: 0,
        mediaUrl: mediaUrl,
        mediaTypeCode: "image",
        mediaCategoryCode: "Listings",
        sortOrder: 0,
      };
      if (listing) {
        let tempListing = { ...listing };
        tempListing.media?.push(newListingMedia);
        setListing(tempListing);
      }
    }
  }

  async function updateListing(listing: ManageListingWithDisplayGroupedDataAndMediaDto) {
    if (id) {
      await ManageService.updateMedia(listing.listingId!, listing.media!);
    }
  }

  return (
    <>
      <div styleName="container">
        {!props.hideHeroImage && listing?.media && (
          <div styleName="carousel-container">
            <div
              title="Double Click for Fullscreen"
              onDoubleClick={() => setOpenLightbox(true)}
            >
              {listing.media ? (
                <>
                  <ImageCarousel
                    isEdit={props.isEdit}
                    profileImageId={listing.profileListingMediaId}
                    showThumbs
                    showIndicators
                    showStatus
                    mediaArray={listing.media}
                    onSave={refreshData}
                    updateProfile={(profileMedia) =>
                      updateProfile(profileMedia)
                    }
                  />
                </>
              ) : null}
            </div>
            <LightBoxWrapper
              open={openLightbox}
              setOpen={setOpenLightbox}
              mediaArray={listing.media}
            />
          </div>
        )}
        <UploadListingMedia
          listingId={id}
          onUpload={(mediaUrl) =>
            id ? refreshData() : createMediaListing(mediaUrl)
          }
        />
      </div>
    </>
  );
}
