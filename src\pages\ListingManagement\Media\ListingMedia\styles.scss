@import "../../../../config/theme/vars.scss";


.carousel-container {
    display: flex;
    flex-direction: column;
}

.title {
    font-size: 16;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.container {
    display: flex;
    flex-direction: column;
  
    >.row {
      justify-content: space-between;
      margin-bottom: 1.25rem;
  
      .header {
        color: $primaryColor;
        font-size: 1.3rem;
        font-weight: 600;
      }
  
      button {
        color: $primaryColor;
  
        .menu-icon {
          height: 1.25rem;
          width: 1.25rem;
          font-size: 1.25rem;
          padding: 0;
          color: rgba(0, 0, 0, 0.6);
        }
      }
  
      svg {        
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }    
    }
  
    .grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 0.5rem;
  
      >.column {
        color: #4E4949;
        font-size: 1rem;
  
        .label {
          font-weight: 600;
        }
  
        .value {
          font-weight: 400;
        }
  
        .green {
          color: $green !important;
        }
        
        .red {
          color: $red !important;
        }
      }
    }
  }

@media ($ltxs) {
    .attribute-details-item {
        width: 40%;
    }

    .attributes-container {
        flex-wrap: wrap;
    }
}

.row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 5px;
}

.favourites {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    cursor: pointer;
    align-items: center;
}

.header {
    color: $primaryColor;
    font-size: 1.3rem;
    font-weight: 600;
  }

.drop-area {
    height: 10rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed $primaryColor;
    background-color: $primaryBgColor;
    color: $primaryColor;
    border-radius: 6px;
    cursor: pointer;
  }
  
  .drop-row {
    display: grid;
    grid-template-columns: max-content auto;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
  
    div {
      margin-right: 1rem;
    }
  }
  
  .icon {
    cursor: pointer;
  }