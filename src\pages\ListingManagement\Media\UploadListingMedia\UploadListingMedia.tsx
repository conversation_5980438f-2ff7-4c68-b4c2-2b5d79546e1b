import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button } from "@mui/material";
import React, { useState } from "react";
import Dropzone from "react-dropzone";
import "./styles.scss";
import ManageService from "../../../../services/manage";
import { ManageListingMediaCDto } from "redi-types";
import FileManager from "../../../../services/file";

interface Props {
  listingId?: string;
  //onSave?: (value: ManageListingMediaCDto) => void;
  displayUploadedFileNames?: boolean;
  onFileDrop?: () => void;
  acceptedFileTypes?: string[];
  maxFiles?: number;
  onUpload?: (mediaUrl?: string) => void;
}

UploadListingMedia.defaultProps = {
  displayUploadedFileNames: true,
  acceptedFileTypes: "",
};
export function UploadListingMedia(props: Props) {
  const [files, setFiles] = useState<File[]>([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mediaUrl, setMediaUrl] = useState<string>();
  const onDrop = (droppedFiles: File[]) => {
    //confirm correct file types
    droppedFiles.forEach;
    // Do something with the files
    //console.log('Accepted Files:', acceptedFiles)
    setFiles([...files, ...droppedFiles]);
  };

  const removeFile = (index: number) => {
    const filesCopy = [...files];
    filesCopy.splice(index, 1); // Remove element at index i
    setFiles(filesCopy);
  };

  async function submit() {
    setIsSubmitting(true);
    console.log("Submitting files ...");
    files.forEach((file) => {
      const reader = new FileReader();
      reader.onabort = () => console.log("file reading was aborted");
      reader.onerror = () => console.log("file reading has failed");
      reader.onload = async () => {
        await FileManager.UploadFile(
          file,
          file.name,
          "Listings",
          true,
          props.listingId ? Number(props.listingId) : undefined
        ).then((data) => {
          if (data.data) {
            setMediaUrl(data.data);
            //console.log(data.data);
            props.onUpload && props.onUpload(data.data);
          }
        });
        setIsSubmitting(false);
      };
      reader.readAsArrayBuffer(file);
    });
  }
  return (
    <div>
      <Dropzone
        onDrop={onDrop}
        maxFiles={props.maxFiles}
        {...(props.acceptedFileTypes && <>accept={props.acceptedFileTypes}</>)}
      >
        {({ getRootProps, getInputProps, isDragActive }) => (
          <section>
            <div {...getRootProps()} styleName="drop-area">
              <input {...getInputProps()} />
              {isDragActive ? (
                <p>Drop that files here ...</p>
              ) : (
                <p>Drag and drop files here, or click to select files</p>
              )}
            </div>
          </section>
        )}
      </Dropzone>
      {files.length > 0 && props.displayUploadedFileNames && (
        <div>
          <h5>Upload Files</h5>
          <hr />
          <div styleName="drop-row">
            {files.map((file, i) => {
              return (
                <React.Fragment key={i}>
                  <div>{file.name}</div>
                  <FontAwesomeIcon
                    icon="close"
                    styleName="icon"
                    onClick={() => removeFile(i)}
                  />
                </React.Fragment>
              );
            })}
          </div>
          <Button variant="contained" onClick={submit} disabled={isSubmitting}>
            Upload
          </Button>
        </div>
      )}
      {errorMessage && <h4>{errorMessage}</h4>}
    </div>
  );
}
