import UserManagementMenu from 'usercomponents/UserManagementMenu';
// import FormSettingsMenu from 'formcomponents/FormSettingsMenu';
import CrmSettingsMenu from 'crmcomponents/CrmSettingsMenu';
import './styles.scss';

interface Props {}

function Settings(props: Props) {
    return (
      <div styleName="page">
        <div styleName="card">
          <div styleName="container">
            <div styleName="table-title">Settings</div>
            <div styleName="grid">
              <UserManagementMenu />
              <CrmSettingsMenu />
            </div>
          </div>
        </div>
      </div>
    )
}

export default Settings;