import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import { CircularProgress } from "@mui/material";
import './styles.scss';
import { TruckDetails } from "./TruckDetails/TruckDetails";
import { TruckHeader } from "./TruckHeader/TruckHeader";
import ManageService from "../../services/manage";
import LoadingDataComponent from "../../components/LoadingDataComponent/LoadingDataComponent";
import withAsyncLoad from "../../components/HOC/withAsyncLoad";

const EventList = withAsyncLoad<{ parentEntityIntId2?: number, parentEntityType2?: string }>(() => import('commoncomponents/EventList'));

interface Props {}

export function Truck(props: Props) {
    const { id } = useParams();
    const [tabIndex, setTabIndex] = useState(0);
    const [truck, setTruck] = useState<ManageListingWithDisplayGroupedDataAndMediaDto>();
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (id) {
            ManageService.get(Number(id), false, true, "TruckDetail", undefined, undefined, true).then((data) => {
                if (!data.error && data.data) {
                    setTruck(data.data);
                }
                setIsLoading(false);
            });
        }
    }, [id]);

    return (
        <div styleName="page">
            {isLoading ? (
                <div styleName="loading">
                    <CircularProgress color="primary" size={50} />
                </div>
            ) : (
                <>
                    <div styleName="container">
                        <div styleName="content">
                            <LoadingDataComponent isLoading={isLoading} data={truck}>
                                <TruckHeader truck={truck!} />
                            </LoadingDataComponent>
                            <div styleName="main-content">
                                <div styleName="row">
                                    <div 
                                        styleName={`tab ${tabIndex === 0 ? 'active' : ''}`} 
                                        onClick={() => setTabIndex(0)}
                                    >
                                        Details
                                    </div>
                                    <div 
                                        styleName={`tab ${tabIndex === 1 ? 'active' : ''}`} 
                                        onClick={() => setTabIndex(1)}
                                    >
                                        Events
                                    </div>
                                </div>
                                <div styleName="tab-body">
                                    <div styleName="tab-grid-1" style={{ display: tabIndex === 0 ? 'grid' : 'none' }}>
                                        <LoadingDataComponent isLoading={isLoading} data={truck}>
                                            <TruckDetails
                                                truck={truck!}
                                                onSave={(value) => value && setTruck(value)}
                                            />
                                        </LoadingDataComponent>
                                    </div>
                                    <div styleName="tab-grid-2" style={{ display: tabIndex === 1 ? 'grid' : 'none' }}>
                                        <LoadingDataComponent isLoading={isLoading} data={truck}>
                                            <EventList 
                                                parentEntityIntId2={truck?.listingId!} 
                                                parentEntityType2="Listing"
                                            />
                                        </LoadingDataComponent>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
}

export default Truck;