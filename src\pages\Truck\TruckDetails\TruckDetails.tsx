import { useState } from "react";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import './styles.scss';
import { TruckForm } from "../TruckForm/TruckForm";
import { TruckView } from "../TruckView/TruckView";

interface Props {
    truck: ManageListingWithDisplayGroupedDataAndMediaDto;
    onSave?: (value?: ManageListingWithDisplayGroupedDataAndMediaDto) => void | undefined;
}

export function TruckDetails(props: Props) {
    const { truck, onSave } = props;
    const [isEdit, setIsEdit] = useState(false);

    return (
        isEdit ? 
        <TruckForm
            initialValues={truck}
            showHeader={true}
            onCancel={() => setIsEdit(false)}
            onSave={(data) => {
                setIsEdit(false);
                onSave && onSave(data);
            }}
        /> :
        <TruckView
            truck={truck}
            onEdit={() => setIsEdit(true)}
            onSave={onSave}
        />
    );
}