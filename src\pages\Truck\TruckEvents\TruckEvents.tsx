import { useEffect, useState } from 'react';
import { CircularProgress } from '@mui/material';
import './styles.scss';

interface Props {
    truckId: string;
}

interface TruckEvent {
    id: number;
    date: string;
    type: string;
    description: string;
}

export function TruckEvents(props: Props) {
    const { truckId } = props;
    const [events, setEvents] = useState<TruckEvent[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // TODO: Replace with actual API call
        setTimeout(() => {
            setEvents([
                { id: 1, date: '2023-01-01', type: 'Maintenance', description: 'Regular maintenance check' },
                { id: 2, date: '2023-01-15', type: 'Assignment', description: 'Assigned to John Doe' },
            ]);
            setIsLoading(false);
        }, 1000);
    }, [truckId]);

    if (isLoading) {
        return (
            <div styleName="loading">
                <CircularProgress color="primary" size={30} />
            </div>
        );
    }

    return (
        <div styleName="events-container">
            <div styleName="header">Event History</div>
            <div styleName="events-list">
                {events.map(event => (
                    <div key={event.id} styleName="event-item">
                        <div styleName="event-date">{event.date}</div>
                        <div styleName="event-type">{event.type}</div>
                        <div styleName="event-description">{event.description}</div>
                    </div>
                ))}
            </div>
        </div>
    );
}