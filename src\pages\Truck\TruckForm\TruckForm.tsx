import { ListingPartyRelationshipListDto, ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import { Field, FieldArray, FieldProps, Formik, FormikHelpers, getIn } from "formik";
import { Box, Button, CircularProgress, IconButton, MenuItem, Select, TextField } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import * as yup from "yup";
import "./styles.scss";
import ManageService from "../../../services/manage";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";
import { useEffect, useMemo, useState } from "react";
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import TruckClasses from "../../../constants/TruckClasses";
import TruckFeeTypes from "../../../constants/TruckFeeTypes";

const RediAutocompleteParty = withAsyncLoad<any>(() => import('crmcomponents/RediAutocompleteParty'));
const LocationAutocompleteField = withAsyncLoad<any>(() => import('commoncomponents/LocationAutocompleteField'));

type FormDto =  {
  partyRelationships: (ListingPartyRelationshipListDto | {
      listingId: number;
      partyId: null;
      partyDisplayName: null;
      relationshipTypeId: ListingRelationshipTypeEnum;
  })[];
  truckId: string | undefined;
  yard: {
      name: string,
      locationId?: number
  } | null;
  carRegistration: string;
  carMakeModel: string;
  towTruckClass: string;
  towTruckFeeType: string;
  feeSplitDriver: number;
  feeSplitTruck: number;
}

interface Props {
  referenceNo?: string;
  initialValues?: ManageListingWithDisplayGroupedDataAndMediaDto;
  onCancel?: () => void;
  onSave?: (value?: ManageListingWithDisplayGroupedDataAndMediaDto) => void;
  showHeader?: boolean;
  showAddDriver?: boolean;
}

function getDefaultPartyRelationships(listing?: ManageListingWithDisplayGroupedDataAndMediaDto ) {

  const existingRelationships = listing?.partyRelationships ?? [];
  const hasTowOperator = existingRelationships.some(r => r.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator);
  //Extend logic if need be to include checks for other relationships
  if (hasTowOperator) {
    return existingRelationships;
  }

  return [
    ...existingRelationships,
    {
      listingId: 0,
      partyId: null,
      partyDisplayName: null,
      relationshipTypeId: ListingRelationshipTypeEnum.TowOperator
    }
  ];
}

export function TruckForm(props: Props) {

  const { showAddDriver = true, ...rest } = props;
  const [ initialValues, setInitialValues ] = useState<FormDto>();
  const [ listing, setListing ] = useState<ManageListingWithDisplayGroupedDataAndMediaDto>();

  const schema = yup.object().shape({
    truckId: yup.string().required("Required"),
    yard: yup.object().shape({
      name: yup.string().required("Required"),
      locationId: yup.string().required("Required"),
    }),
    towTruckClass: yup.string().required("Required"),
    towTruckFeeType: yup.string().required("Required"),
    feeSplitDriver: yup.number().required("Required"),
    feeSplitTruck: yup.number().required("Required")
  })
  .test(
    "feeSplitSum",
    "Fee split values must add up to 100",
    function (value) {
      const { feeSplitDriver, feeSplitTruck } = value ?? {};
      const isDriverValid = typeof feeSplitDriver === "number";
      const isTruckValid = typeof feeSplitTruck === "number";

      if (isDriverValid && isTruckValid && feeSplitDriver + feeSplitTruck !== 100) {
        // Push error to a custom path
        return this.createError({
          path: "feeSplitSum",
          message: "Fee splits must add up to 100",
        });
      }

      return true;
    }
  );

  useEffect(() => { 

    async function fetch() {
      let values = props.initialValues;
      if (props.referenceNo) {
        const response = await ManageService.getListingByRef(props.referenceNo, false, true, true);
        if (!response.error && response.data) {
          values = response?.data;
        }
      }
      setInitialValues({
        carRegistration: "",
        carMakeModel: "",
        towTruckClass: "",
        towTruckFeeType: "",
        feeSplitDriver: 0,
        feeSplitTruck: 0,
        ...values?.fields,
        partyRelationships: getDefaultPartyRelationships(values),
        truckId: values?.subject,
        yard: values?.fields?.yard ? {
          name: values?.fields?.yard,
          locationId: values?.fields?.yardLocationId ? Number(values?.fields?.yardLocationId) : undefined,
        } : null
      });
      setListing(values);
    }
    fetch();
  }, [props.initialValues, props.referenceNo]);

  async function handleSubmit(values: FormDto, actions: FormikHelpers<FormDto>) {
    actions.setSubmitting(true);
    const formattedData = {
      ...ManageService.getInitialValues(),
      ...listing,
      subject: values.truckId,
      description: values.truckId + " | " + values.carRegistration,
      listingTypeId:ListingTypeEnum.Truck,
      partyRelationships: values?.partyRelationships?.map((item) => ({...item, listingSubject: values.truckId })),
      fields: {
        truckId: values.truckId,
        carRegistration: values.carRegistration,
        carMakeModel: values.carMakeModel,
        yard: values.yard?.name,
        yardLocationId: values.yard?.locationId,
        towTruckClass: values.towTruckClass,
        towTruckFeeType: values.towTruckFeeType,
        feeSplitDriver: values.feeSplitDriver,
        feeSplitTruck: values.feeSplitTruck
      }
    } as ManageListingWithDisplayGroupedDataAndMediaDto;
    let response;
    if (listing?.listingId) {
      response = await ManageService.updateListingWDP(formattedData);
    } else {
      response = await ManageService.createListingWDP(formattedData, false);
    }
    actions.setSubmitting(false);
    props.onSave && props.onSave(response?.data ?? formattedData);
  }

  const cancel = (form: any) => {
    form.resetForm();
    props.onCancel && props.onCancel();
  };

  return (
    !initialValues ?
    <Box
      display="flex"
      height="100%"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      textAlign="center"
    >
      <CircularProgress size={25} />
    </Box> :
    <Formik
      initialValues={initialValues}
      validationSchema={schema}
      onSubmit={handleSubmit}
    >
      {(form) => (
        <form onSubmit={form.handleSubmit}>
          <div styleName="container">
            {props.showHeader && (
              <div styleName="row">
                <div styleName="header">Truck Details</div>
                <div styleName="row">
                  <IconButton onClick={() => form.handleSubmit()}>
                    <FontAwesomeIcon icon="check" />
                  </IconButton>
                  <IconButton onClick={() => cancel(form)}>
                    <FontAwesomeIcon icon="close" />
                  </IconButton>
                </div>
              </div>
            )}

            <div styleName="form-grid">
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    name="truckId"
                    as={TextField}
                    variant="standard"
                    label="Truck ID"
                    fullWidth
                    error={Boolean(
                      getIn(form.touched, "truckId") &&
                      getIn(form.errors, "truckId")
                    )}
                    helperText={
                      getIn(form.touched, "truckId") &&
                      getIn(form.errors, "truckId")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    name="carRegistration"
                    as={TextField}
                    variant="standard"
                    label="Truck Rego"
                    fullWidth
                    error={Boolean(
                      getIn(form.touched, "carRegistration") &&
                      getIn(form.errors, "carRegistration")
                    )}
                    helperText={
                      getIn(form.touched, "carRegistration") &&
                      getIn(form.errors, "carRegistration")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    name="carMakeModel"
                    as={TextField}
                    variant="standard"
                    label="Make/Model"
                    fullWidth
                    error={Boolean(
                      getIn(form.touched, "carMakeModel") &&
                      getIn(form.errors, "carMakeModel")
                    )}
                    helperText={
                      getIn(form.touched, "carMakeModel") &&
                      getIn(form.errors, "carMakeModel")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                      select
                      fullWidth
                      name="towTruckClass"
                      variant="standard"
                      label="Class"
                      as={TextField}
                      error={Boolean(
                        getIn(form.touched, "towTruckClass") &&
                        getIn(form.errors, "towTruckClass")
                      )}
                      helperText={
                        getIn(form.touched, "towTruckClass") &&
                        getIn(form.errors, "towTruckClass")
                      }
                  >
                      {TruckClasses.map((item) => (
                          <MenuItem key={item.code} value={item.code}>
                              {item.code}
                          </MenuItem>
                      ))}
                  </Field>
                </div>
              </div>
              {showAddDriver ?
              <div styleName="row">
              <FieldArray name="partyRelationships">
                {() => (
                  <>
                    {form.values.partyRelationships && 
                    form.values.partyRelationships.map((relationship, index) => (
                    relationship.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator && (
                      <div styleName="field-container size-4" key={index}>
                        <Field
                          id={`partyRelationships.${index}.partyId`}
                          name={`partyRelationships.${index}.partyId`}>
                          {(props: FieldProps) => (
                            <RediAutocompleteParty
                              name={props.field.name}
                              label="Driver"
                              buttonLabel="driver"
                              returnAddress={false}
                              initialValue={relationship.partyDisplayName}
                              partyId={relationship.partyId}
                              showAddNewButton={true}
                              displayName={"displayName"}
                              partyRoleTypeCode="Contractor"
                              partyType="Organisation"
                              partyRelationshipTypeCode="Employment"
                              error={Boolean(
                                getIn(form.touched, `partyRelationships.${index}.partyId`) &&
                                getIn(form.errors, `partyRelationships.${index}.partyId`)
                              )}
                              helperText={
                                getIn(form.touched, `partyRelationships.${index}.partyId`) &&
                                getIn(form.errors, `partyRelationships.${index}.partyId`)
                              }
                              onBlur={props?.field.onBlur}
                              onChange={(dto?: { partyId: string, displayName: string }) => {
                                form.setFieldValue(`partyRelationships.${index}.partyId`, dto?.partyId);
                                form.setFieldValue(`partyRelationships.${index}.partyDisplayName`, dto?.displayName);
                              }}
                            />
                          )}
                        </Field>
                      </div>)
                    ))}
                  </>
                )}
              </FieldArray>
              </div> : null}
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    id="yard.locationId"
                    name="yard.locationId">
                    {(fieldProps: FieldProps) => (
                      <LocationAutocompleteField
                        id="yard.locationId"
                        name={fieldProps.field.name}
                        label="Yard"
                        value={form.values.yard}
                        initialDisplayText={form.values.yard?.name ?? ""}
                        autoSelectFirst={true}
                        params={{
                          type: "Yard"
                        }}
                        onChange={(val:string, location: any) => {
                          form.setFieldValue("yard", location);
                        }}
                        error={Boolean(
                          getIn(fieldProps.meta.touched, "yard.locationId") &&
                          getIn(fieldProps.meta.error, "yard.locationId")
                        )}
                        helperText={
                          getIn(fieldProps.meta.touched, "yard.locationId") &&
                          getIn(fieldProps.meta.error, "yard.locationId")
                        }
                      />
                    )}
                  </Field>
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                      select
                      fullWidth
                      name="towTruckFeeType"
                      variant="standard"
                      label="Fee Type"
                      as={TextField}
                      error={Boolean(
                        getIn(form.touched, "towTruckFeeType") &&
                        getIn(form.errors, "towTruckFeeType")
                      )}
                      helperText={
                        getIn(form.touched, "towTruckFeeType") &&
                        getIn(form.errors, "towTruckFeeType")
                      }
                  >
                      {TruckFeeTypes.map((item) => (
                          <MenuItem key={item.code} value={item.code}>
                              {item.description}
                          </MenuItem>
                      ))}
                  </Field>
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    name="feeSplitDriver"
                    as={TextField}
                    variant="standard"
                    label="Fee Split % -  Driver"
                    fullWidth
                    type="number"
                    error={Boolean(
                      getIn(form.touched, "feeSplitDriver") &&
                      getIn(form.errors, "feeSplitDriver")
                    )}
                    helperText={
                      getIn(form.touched, "feeSplitDriver") &&
                      getIn(form.errors, "feeSplitDriver")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                <div styleName="field-container size-1">
                  <Field
                    name="feeSplitTruck"
                    as={TextField}
                    variant="standard"
                    label="Fee Split % -  Truck"
                    fullWidth
                    type="number"
                    error={Boolean(
                      getIn(form.touched, "feeSplitTruck") &&
                      getIn(form.errors, "feeSplitTruck")
                    )}
                    helperText={
                      getIn(form.touched, "feeSplitTruck") &&
                      getIn(form.errors, "feeSplitTruck")
                    }
                  />
                </div>
              </div>
              <div styleName="row">
                {getIn(form.errors, "feeSplitSum") && (
                  <p style={{ color: "red" }}>{getIn(form.errors, "feeSplitSum")}</p>
                )}
              </div>
            </div>

            {!props.showHeader && (
              <div styleName="button-row">
                <Button onClick={() => cancel(form)}>Cancel</Button>
                <Button type="submit" variant="contained" color="primary">
                  Save
                </Button>
              </div>
            )}
          </div>
        </form>
      )}
    </Formik>
  );
}


