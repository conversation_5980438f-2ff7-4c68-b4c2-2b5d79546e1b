import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import './styles.scss';
import { useMemo } from "react";
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";

interface Props {
    truck: ManageListingWithDisplayGroupedDataAndMediaDto;
}

export function TruckHeader(props: Props) {
    const { truck } = props;
    
    const towOperator = useMemo(() => {
        return truck.partyRelationships?.find(x => x.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator)?.partyDisplayName;
    }, [truck]);

    return (
        <div styleName="summary-bar">
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{truck.subject ?? "N/A"}</div>
                    <div styleName="bottom">Truck ID</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{truck.fields?.carRegistration ?? "N/A"}</div>
                    <div styleName="bottom">Rego</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{towOperator ?? "N/A"}</div>
                    <div styleName="bottom">Driver</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{truck.fields?.yard ?? "N/A"}</div>
                    <div styleName="bottom">Yard</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{truck.statusCode === "Active" ? "Available" : "Unavailable"}</div>
                    <div styleName="bottom">Status</div>
                </div>
            </div>
        </div>
    );
}

