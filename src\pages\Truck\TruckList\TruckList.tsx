import { Alert, AlertColor, Icon<PERSON>utton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ListingWithDataAndMediaCDto } from "redi-types";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import './styles.scss';
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import DraggableDialog from "../../../components/DraggableDialog/DraggableDialog";
import { TruckForm } from "../TruckForm/TruckForm";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import ListingService from "../../../services/listing";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";
import ManageService from "../../../services/manage";


interface Props {}

export function TruckList(props: Props) {
  const navigate = useNavigate();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTruck, setSelectedTruck] = useState<ListingWithDataAndMediaCDto>();
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>("success");
  const isDeletingRef = useRef(false);

  const tableHeaders: TableHeader[] = [
    { id: "Subject", label: "Truck Id", isSortable: true, align: "left" },
    { id: "carRegistration", label: "Rego", isSortable: true, align: "left"   },
    { id: "RelationshipTypeId3", label: "Driver", isSortable: true, align: "left"   },
    { id: "yard", label: "Location", isSortable: true, align: "left"   },
    { id: "StatusCode", label: "Status", isSortable: true, align: "left"  },
    { id: "actions", label: "Actions", isSortable: false, align: "right"  }
  ];

  function renderTableRow(data: ListingWithDataAndMediaCDto) {
    return (
      <>
        <TableCell>{<b>{data.subject}</b>}</TableCell>
        <TableCell>{data.fields?.carRegistration}</TableCell>
        <TableCell>{data.relationshipFields?.towOperator}</TableCell>
        <TableCell>{data.fields?.yard}</TableCell>
        <TableCell>{data.statusCode === "Active" ? "Available" : "Unavailable"}</TableCell>
        <TableCell align="right">
          <RowMenu rowData={data} />
        </TableCell>
      </>
    );
  }

  function RowMenu({ rowData }: { rowData: ListingWithDataAndMediaCDto }) {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = (event: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
      event?.stopPropagation();
      setAnchorEl(null);
    };

    const handleEdit = (event: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
      handleClose(event);
      //navigate(`/Truck/${rowData.listingId}`);
      event.stopPropagation();
      setAnchorEl(null);
      setSelectedTruck(rowData);
      setIsAddDialogOpen(true);
    };

    const handleDelete = (event: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
      handleClose(event);
      setSelectedTruck(rowData);
      setIsDeleteDialogOpen(true);
    };

    return (
      <>
        <IconButton onClick={handleClick}>
          <FontAwesomeIcon icon="ellipsis-v" />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
        >
          <MenuItem onClick={handleEdit}><ListItemText>Edit</ListItemText></MenuItem>
          <MenuItem onClick={handleDelete}><ListItemText>Delete</ListItemText></MenuItem>
        </Menu>
      </>
    );
  }

  function closeAddDialog() {
    setIsAddDialogOpen(false);
    setSelectedTruck(undefined);
  }

  function closeDeleteDialog() {
    setIsDeleteDialogOpen(false);
  }

  async function onAddDialogSave() {
    setRefreshTableTrigger(prev => prev + 1);
    setIsAddDialogOpen(false);
    setSnackbarMessage("Truck added successfully");
    setSnackbarSeverity("success");
    setShowSnackbar(true);
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        <DataTable<ListingWithDataAndMediaCDto, "listingId">
          primaryKeyProperty="listingId"
          title="Trucks"
          tableId="base-truck-list"
          pageSize={10}
          initialSortColumn="Subject"
          refreshTableTrigger={refreshTableTrigger}
          tableHeight={600}
          tableHeaders={tableHeaders}
          renderTableRow={renderTableRow}
          addButtonLabel="New Truck"
          addButtonOnClick={() => setIsAddDialogOpen(true)}
          onRowClick={(row) => navigate(`/Truck/${row.listingId}`)}
          filterOptions={['Available', 'Unavailable', 'All']}
          callService={(params, search, inFilter) => {
            const _inFilter = inFilter === 'All' ? 
                            '' :  inFilter === 'Available' ? 
                            'Active' : 'Expired';
            return ListingService.getList(
                        undefined,
                        undefined,
                        "TruckDetail",
                        undefined,
                        undefined,
                        undefined,
                        undefined,
                        _inFilter,
                        undefined,
                        ListingTypeEnum.Truck,
                        undefined,
                        undefined,
                        undefined,
                        undefined,
                        params,
                        undefined,
                        undefined,
                        true,
                        undefined,
                        undefined,
                        undefined,
                        true,
                        "3",
                        //Truck Id, Rego, Driver, Yard, Make/Model
                        search ? {
                          "TruckId": { values: [search], label: "truckId", operator: "contains"  },
                          "CarRegistration": { values: [search], label: "carRegistration", operator: "contains"  },
                          "CarMakeModel": { values: [search], label: "carMakeModel", operator: "contains"  },
                          "Yard": { values: [search], label: "yard", operator: "contains"  }
                        } : undefined,
                        search ?
                        {
                          "3": { values: [search], label: "3", operator: "contains" }
                        } : undefined
                      )}
                    }
        />
        {isAddDialogOpen ?
        <DraggableDialog
          maxWidth="xl"
          title="Add New Truck"
          isOpen={isAddDialogOpen}
          onCancel={closeAddDialog}
          fullWidth={true}
        >
          <TruckForm
            referenceNo={selectedTruck?.referenceNo}
            onCancel={closeAddDialog}
            onSave={onAddDialogSave}
          />
        </DraggableDialog> : null}

        <YesNoDialog
          title="Delete Truck"
          bodyText="Are you sure you want to delete this truck?"
          isOpen={isDeleteDialogOpen}
          onNo={closeDeleteDialog}
          onYes={async () => {
            if (isDeletingRef.current) { return; }
            isDeletingRef.current = true;
            // Add delete logic here
            await ManageService.deleteListing(selectedTruck?.listingId!);
            closeDeleteDialog();
            setRefreshTableTrigger(prev => prev + 1);
            isDeletingRef.current = false;
          }}
        />

        <Snackbar 
          open={showSnackbar}
          autoHideDuration={4000}
          onClose={() => setShowSnackbar(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default TruckList;

