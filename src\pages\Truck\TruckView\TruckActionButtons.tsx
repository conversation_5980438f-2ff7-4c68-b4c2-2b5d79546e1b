import { useRef, useState } from "react";
import { ContractorStatusCodeEnum } from "../../../enum/ContractorStatusCodeEnum";
import { IconButton, ListItemText, Menu, MenuItem } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import "./styles.scss";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import { ListingStatus } from "../../../enum/listingStatus";
import ManageService from "../../../services/manage";
import { HttpResult } from "redi-http";
import { useNavigate } from "react-router-dom";

interface Props {
    truck: ManageListingWithDisplayGroupedDataAndMediaDto;
    onSave: (data: ManageListingWithDisplayGroupedDataAndMediaDto) => void;
}

function TruckActionButtons(props: Props) {
    const { truck, onSave } = props;
    const navigate = useNavigate();
    const [ selectedStatus, setSelectedStatus] = useState<ListingStatus | null>();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [deleteDialog, setDeleteDialog] = useState(false);
    const promiseDeleteRef = useRef<Promise<HttpResult> | null>(null);

    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
        setSelectedStatus(null);
        setDeleteDialog(false);
    };
    const handleStatusSelect = (statusCode: ListingStatus) => {
        if (truck.statusCode === statusCode) { return; }
        setSelectedStatus(statusCode);
    };
    const handleSave = () => {
        ManageService.updateListingStatus(truck.listingId!, selectedStatus!).then((data) => {
        if (!data.error) {
            onSave({...truck, statusCode: selectedStatus!});
        }
        handleClose();
       });
    };

    const handleDelete = async () => {
        if (promiseDeleteRef.current) { return; }
        promiseDeleteRef.current = ManageService.deleteListing(truck.listingId!);
        await promiseDeleteRef.current;
        promiseDeleteRef.current = null;
        navigate("/Trucks");
    }

    const currentStatus = truck.statusCode === ListingStatus.Active ? "Available" : "Unavailable";
    const selectedStatusName = selectedStatus === ListingStatus.Active ? "Available" : "Unavailable";

    return(
        <>
            <IconButton
                id="actions-list"
                aria-controls={open ? 'case-details-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                onClick={handleClick}
            >
                <FontAwesomeIcon styleName="menu-icon" icon="ellipsis-v" />
            </IconButton>
            <Menu
                id="actions-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{"aria-labelledby": "menu-button"}}
            >
                {truck.statusCode === ListingStatus.Active ?
                <MenuItem onClick={() => handleStatusSelect(ListingStatus.Expired)}>
                    <ListItemText>Set Unavailable</ListItemText>
                </MenuItem> : null}
                {truck.statusCode === ListingStatus.Expired ?
                <MenuItem onClick={() => handleStatusSelect(ListingStatus.Active)}>
                    <ListItemText>Set Available</ListItemText>
                </MenuItem> : null}
                <MenuItem onClick={() => setDeleteDialog(true)} >
                    <ListItemText>Delete</ListItemText>
                </MenuItem>
            </Menu>
            {selectedStatus ? 
            <YesNoDialog
                title="Change Status"
                bodyText={`Please confirm your are changing status from ${currentStatus} to ${selectedStatusName}.`}
                isOpen={true}
                onNo={handleClose}
                onYes={handleSave}
            /> : null
            }
            {deleteDialog ?
            <YesNoDialog
                title="Delete Truck"
                bodyText={`Are you sure? This cannot be reversed`}
                isOpen={true}
                onNo={handleClose}
                onYes={handleDelete}
            /> : null
            }
        </>
    )
}

export default TruckActionButtons;