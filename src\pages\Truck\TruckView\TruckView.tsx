import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Grid, IconButton } from "@mui/material";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import "./styles.scss";
import { useMemo } from "react";
import { ListingRelationshipTypeEnum } from "../../../enum/ListingRelationshipTypeEnum";
import TruckActionButtons from "./TruckActionButtons";
import TruckClasses from "../../../constants/TruckClasses";

interface Props {
    truck: ManageListingWithDisplayGroupedDataAndMediaDto;
    onEdit: () => void;
    onSave?: (data?: ManageListingWithDisplayGroupedDataAndMediaDto) => void | undefined;
}

export function TruckView(props: Props) {
    const { truck, onEdit, onSave } = props;

    //Potential sample response: [Company Name] | [Driver Name]
    const { company, driver} = useMemo(() => {
        const response = truck.partyRelationships?.find(x => x.relationshipTypeId === ListingRelationshipTypeEnum.TowOperator)?.partyDisplayName;
        const values = response?.split("|");
        return {
            company: values?.[0]?.trim(),
            driver: values?.[1]?.trim()
        };
    }, [truck]);

    return (
        <div styleName="container">
            <div styleName="row">
                <div styleName="header">Truck Information</div>
                <div styleName="row">
                <IconButton onClick={onEdit}>
                    <FontAwesomeIcon icon={["fas", "edit"]} />
                </IconButton>
                {onSave ?
                <TruckActionButtons truck={truck} onSave={onSave} /> : null}
                </div>
            </div>
            <div styleName="grid">
                <Grid container>
                    <Grid item xs={6}>
                        <div styleName="column">
                            <div styleName="label">Id</div>
                            <div styleName="value">{truck.subject ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Rego</div>
                            <div styleName="value">{truck.fields?.carRegistration ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Class</div>
                            <div styleName="value">{truck.fields?.towTruckClass}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Make/Model</div>
                            <div styleName="value">{truck.fields?.carMakeModel ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Yard</div>
                            <div styleName="value">{truck.fields?.yard ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Status</div>
                            <div styleName="value">{truck.statusCode === "Active" ? "Available" : "Unavailable"}</div>
                        </div>
                    </Grid>
                    <Grid item xs={6}>
                        <div styleName="column">
                            <div styleName="label">Driver</div>
                            <div styleName="value">{driver ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Company</div>
                            <div styleName="value">{company ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Fee Type</div>
                            <div styleName="value">{truck.fields?.towTruckFeeType ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Fee Split - Driver</div>
                            <div styleName="value">{truck.fields?.feeSplitDriver ?? "N/A"}</div>
                        </div>
                        <div styleName="column">
                            <div styleName="label">Fee Split - Truck</div>
                            <div styleName="value">{truck.fields?.feeSplitTruck ?? "N/A"}</div>
                        </div>
                    </Grid>
                </Grid>



            </div>
        </div>
    );
}

