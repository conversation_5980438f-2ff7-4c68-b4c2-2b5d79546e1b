@import "../../../config/theme/vars.scss";

.row {
  display: flex;
  align-items: center;
}

.container {
  display: flex;
  flex-direction: column;

  >.row {
    justify-content: space-between;
    margin-bottom: 1.25rem;

    .header {
      color: $primaryColor;
      font-size: 1.3rem;
      font-weight: 600;
    }

    button {
      svg {
        color: $primaryColor;
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }
      
      .menu-icon {
        height: 1.25rem;
        width: 1.25rem;
        font-size: 1.25rem;
        padding: 0;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }

  .grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;

    .column {
      color: #4E4949;
      font-size: 1rem;
      padding: 5px;

      .label {
        font-weight: 600;
      }

      .value {
        font-weight: 400;
      }
    }
  }
}