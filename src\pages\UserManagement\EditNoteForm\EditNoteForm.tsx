import { LoadingButton } from "@mui/lab";
import { AlertColor, Button, TextField } from "@mui/material";
import { Field, Formik, FormikHelpers, FormikProps } from "formik";
import { useState } from "react";
import { ApplicationUser, GetListUserManagementCDto } from "redi-types";
import * as yup from "yup";
import "./styles.scss";
import UserService from "../../../services/demoServices/user";

interface Props {
  initialValues?: GetListUserManagementCDto;
  onCancel?: () => void;
  onSave?: (message: string, severity: AlertColor) => void;
}

function EditNoteForm(props: Props) {

  let schema = yup.object({
    note: yup.string().required('Enter note')
  });

  const [isLoading, setIsLoading] = useState(false);

  function cancel(form: FormikProps<ApplicationUser>) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  async function save(data: ApplicationUser, actions: FormikHelpers<any>) {
    setIsLoading(true);
    const response = await UserService.UpdateNote(data.userId, data.note);
    if (!response.error) {
      props.onSave && props.onSave('Successfully Updated Note', 'success');
    } else {
      props.onSave && props.onSave('Failed To Update Note', 'error');
    }
    setIsLoading(false);
  }

  if (!props.initialValues) {
    return null;
  } else {
    return (
      <>
        <Formik<ApplicationUser>
          enableReinitialize // Rerender when props changes (initialValues)
          validationSchema={schema}
          initialValues={props.initialValues}
          onSubmit={(data, actions) => {
            save(data, actions);
          }}
        >
          {(form) => (
            <form onSubmit={form.handleSubmit}>
              <div styleName="form-grid">
                <Field
                  multiline
                  rows={4}
                  variant="standard"
                  label="Note"
                  id="note"
                  name="note"
                  as={TextField}
                  error={form.touched.note && Boolean(form.errors.note)}
                  helperText={form.touched.note && form.errors.note}
                />
              </div>
              <div styleName="row">
                <div styleName="button-row">
                  <Button variant="outlined" onClick={() => cancel(form)}>
                    Cancel
                  </Button>
                  <LoadingButton
                    variant="contained"
                    loading={isLoading}
                    onClick={() => form.handleSubmit()}
                  >
                    Submit
                  </LoadingButton>
                </div>
              </div>            
            </form>
          )}
        </Formik>
      </>
    );
  }
}

export default EditNoteForm;