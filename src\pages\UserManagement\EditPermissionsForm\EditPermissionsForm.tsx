import { LoadingButton } from "@mui/lab";
import { AlertColor, Button, MenuItem, TextField } from "@mui/material";
import { Field, Formik, FormikHelpers, FormikProps } from "formik";
import { useEffect, useState } from "react";
import { ApplicationUser, GetListClaimCDto, GetListRoleCDto } from "redi-types";
import * as yup from "yup";
import ClaimCheckboxTree, { TreeNode } from "../../../components/ClaimCheckboxTree/ClaimCheckboxTree";
import UserService from "../../../services/demoServices/user";
import "./styles.scss";

interface Props {
  isModal?: boolean;
  user?: ApplicationUser;
  allClaims: GetListClaimCDto[];
  allRoles: GetListRoleCDto[];
  onCancel?: () => void;
  onSave?: (message: string, severity: AlertColor, refreshData?: boolean) => void;
}

function EditPermissionsForm(props: Props) {

  let schema = yup.object({
    roleId: yup.string().required('Please select a role')
  });

  const [isLoading, setIsLoading] = useState(false);
  const [hasDataChanged, setHasDataChanged] = useState(false); // Save's disable logic

  // Claims Tree
  const [tree, setTree] = useState<TreeNode[]>([]);
  const [checkedNodes, setCheckedNodes] = useState<string[]>([]); // User's claimIds

  const initialValues = {
    roleId: props.user?.roleId ?? '' 
  }

  useEffect(() => {
    if (props.allClaims.length !== 0 && props.allRoles.length !== 0) {
      initialiseCheckedNodes(); // The user's claims
      const tree = convertArrayToTree(props.allClaims);
      setTree(tree);
    }
  }, [props.allClaims, props.allRoles]);

  async function initialiseCheckedNodes() {
    if (!props.user) {
      return;
    }

    const usersClaimsResponse = await UserService.GetUserClaims(props.user.userId);
    if (!usersClaimsResponse.error && usersClaimsResponse.data) {
      const claimIds = usersClaimsResponse.data.map(claim => claim.claimId);
      setCheckedNodes(claimIds);
    }
  }

  function convertArrayToTree(claims: GetListClaimCDto[]): TreeNode[] {  
    // Convert from GetListClaimCDto to key/value object ie.
    // [{claimId: "1", name: "Claim", claimGroup: "Group1" }, ...]
    // =>
    // { "Group1": {label: "Group1", value: "Group1", children: [{label: "1", value: "Claim"}], ... }
    const claimGroups: { [key: string]: TreeNode } = {};
  
    claims.forEach((item) => {
      // Create claim group if doesn't exist yet
      if (!claimGroups[item.claimGroup]) {
        claimGroups[item.claimGroup] = {
          value: item.claimGroup,
          label: item.claimGroup,
          children: [],
        };
      }
  
      // Add the current item to the children array of its group
      const children = claimGroups[item.claimGroup].children;
      if (children) {
        children.push({
          value: item.claimId,
          label: item.name
        });
      } else {
        claimGroups[item.claimGroup].children = [{
          value: item.claimId,
          label: item.name
        }];
      }
    });


    // Convert from key/value object to a values array ie.
    // { "Group1": {label: "Group1", value: "Group1", children: [{label: "1", value: "Claim"}], ... }
    // =>
    // [ {label: "Group1", value: "Group1", children: [{label: "1", value: "Claim"}], ...]
    const tree: TreeNode[] = Object.keys(claimGroups).map((key) => claimGroups[key]);
    return tree;
  }

  function onNodeChecked(checkedNodes: string[]) {
    setHasDataChanged(true);
    setCheckedNodes(checkedNodes);
  }

  function roleChanged(event: React.ChangeEvent<HTMLInputElement>, form: FormikProps<PermissionForm>) {
    setHasDataChanged(true);

    const roleId = event.target.value;
    form.setFieldValue('roleId', roleId);
    // Change what nodes are checked - an array of claimIds:
    const role = props.allRoles.find(role => role.roleId === roleId);
    const claimIds = role?.claims?.map(claim => claim.claimId) ?? [];
    setCheckedNodes(claimIds);
  }

  function cancel(form: FormikProps<PermissionForm>) {
    form.resetForm();
    setHasDataChanged(false);
    props.onCancel && props.onCancel();
  }

  async function save(data: PermissionForm, actions: FormikHelpers<any>) {
    setIsLoading(true);

    const userId = props?.user?.userId ?? '';
    const checkedClaims = props.allClaims.filter((claim) => checkedNodes.includes(claim.claimId));
    const response = await UserService.AddClaims(userId, checkedClaims, data.roleId);
    if (!response.error) {
      // TODO: If current logged in user is changing their own claims then their jwt must be regenerated
      // Waiting on logic for detecting current user.
      // if (userId === <Current Logged In User>) {
      //   //refresh jwt
      //   await UserService.RegenerateJwt(); 
      // }
      setHasDataChanged(false);
      props.onSave && props.onSave('Successfully Updated Permissions', 'success', true);
    } else {
      props.onSave && props.onSave('Failed To Update Permissions', 'error');
    }

    setIsLoading(false);
  }

  function renderPermissions() {
    return (
      <>
        <Formik<PermissionForm>
          enableReinitialize // Rerender when props changes (initialValues)
          validationSchema={schema}
          initialValues={initialValues}
          onSubmit={(data, actions) => {
            save(data, actions);
          }}
        >
          {(form) => (
            <form onSubmit={form.handleSubmit}>
              {
                !props.isModal &&
                <div styleName="row">
                  <div styleName="header">User Permissions</div>
                </div>
              }
              <div styleName="form-grid">
                <Field 
                  select
                  id="roleId"
                  name="roleId"
                  label="Role"
                  as={TextField}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => roleChanged(event, form)}
                  error={form.touched.roleId && Boolean(form.errors.roleId)}
                  helperText={form.touched.roleId && form.errors.roleId}
                >
                  {
                    props.allRoles.map(role => <MenuItem key={role.roleId} value={role.roleId}>{role.name}</MenuItem>)
                  }
                </Field>
              </div>

              <div>Individual Permissions</div>
              <ClaimCheckboxTree nodes={tree} checked={checkedNodes} onChecked={onNodeChecked} />
              
              <div styleName="row">
                <div styleName="button-row">
                  <Button variant="outlined" onClick={() => cancel(form)}>
                    Cancel
                  </Button>
                  <LoadingButton
                    variant="contained"
                    disabled={!hasDataChanged}
                    loading={isLoading}
                    onClick={() => form.handleSubmit()}
                  >
                    Save
                  </LoadingButton>
                </div>
              </div>            
            </form>
          )}
        </Formik>
      </>
    );
  }

  return (
    <>
      {
        !props.user
        ?
          <div>Error: User data failed to load</div>
        :
          renderPermissions()
      }
    </>
  );
}

export default EditPermissionsForm;

interface PermissionForm {
  roleId: string;
};