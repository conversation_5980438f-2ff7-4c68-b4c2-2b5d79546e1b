@import "../../../config/theme/vars.scss";


.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0 2rem;
  padding-top: 1rem;

  .full-width {
    // Start a column 2 and be 3 columns wide
    grid-column: 1 / span 2;
  }

  // Stop form field's validation growing/shinking the dialog
  & > div {
    min-height: 72px;
  }
}

.row {
  display: flex;
  flex-direction: row;

  .header {
    color: $primaryColor;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
  }
}

.button-row {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}