import { LoadingButton } from "@mui/lab";
import { AlertColor, Button, TextField } from "@mui/material";
import { Field, Formik, FormikProps } from "formik";
import { useState } from "react";
import * as yup from "yup";
import YesNoDialog from "../../../components/YesNoDialog/YesNoDialog";
import UserService from "../../../services/demoServices/user";
import "./styles.scss";

interface Props {
  userId: string;
  onCancel?: () => void;
  onSave?: (message: string, severity: AlertColor) => void;
}

function ResetPasswordForm(props: Props) {
  const initialValues: ResetPasswordFormDto = {
    newPassword: '',
    confirmPassword: ''
  };

  let schema = yup.object({
    newPassword: yup
                  .string()
                  .matches(
                      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{6,})/,
                      "Passwords must be at least 6 characters. Must have at least one non alphanumeric character. Must have at least one uppercase ('A'-'Z')")
                  .required('Enter New Password'),
    confirmPassword: yup
                  .string()
                  .matches(
                      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{6,})/,
                      "Passwords must be at least 6 characters. Must have at least one non alphanumeric character. Must have at least one uppercase ('A'-'Z')")
                  .test('confirm-password-test', () => {
                      return "Passwords do not match";
                  }, function (element: any) {
                      return element === this.parent.newPassword;
                  })
                  .required('Enter Confirm Password')
  });

  const [isPwResetLoading, setIsPwResetLoading] = useState(false);
  const [isPwChangeLoading, setIsPwChangeLoading] = useState(false);
  const [isPwResetDialogOpen, setIsPwResetDialogOpen] = useState(false);
  const [isPwChangeDialogOpen, setIsPwChangeDialogOpen] = useState(false);
  const [newPassword, setNewPassword] = useState<string>('');

  function openPwResetDialog() {
    setIsPwResetDialogOpen(true);
  }

  function closePwResetDialog() {
    setIsPwResetDialogOpen(false);
  } 

  function openPwChangeDialog() {
    setIsPwChangeDialogOpen(true);
  }

  function closePwChangeDialog() {
    setIsPwChangeDialogOpen(false);
  }

  function cancel(form: FormikProps<ResetPasswordFormDto>) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  async function resetPasswordForUser() {
    closePwResetDialog();
    setIsPwResetLoading(true);
    const response = await UserService.ChangePasswordForUser(props.userId);
    if (!response.error) {
      props.onSave && props.onSave('User\'s Password Successfully Reset', 'success');
    } else {
      props.onSave && props.onSave('Failed To Reset User\'s Password', 'error');
    }
    setIsPwResetLoading(false);
  }

  async function changePasswordForUser() {
    closePwChangeDialog();
    setIsPwChangeLoading(true);
    const response = await UserService.ChangePasswordForUser(props.userId, newPassword);
    if (!response.error) {
      props.onSave && props.onSave('User\'s Password Successfully Reset', 'success');
    } else {
      props.onSave && props.onSave('Failed To Reset User\'s Password', 'error');
    }
    setIsPwChangeLoading(false);
  }

  return (
    <>
      <Formik<ResetPasswordFormDto>
        enableReinitialize // Rerender when props changes (initialValues)
        validationSchema={schema}
        initialValues={initialValues}
        onSubmit={(data, actions) => {
          setNewPassword(data.newPassword);
          openPwChangeDialog();
        }}
      >
        {(form) => (
          <form onSubmit={form.handleSubmit}>
            <div styleName="form-grid">
              <Field
                type="password"
                variant="standard"
                label="New Password"
                id="newPassword"
                name="newPassword"
                as={TextField}
                error={form.touched.newPassword && Boolean(form.errors.newPassword)}
                helperText={form.touched.newPassword && form.errors.newPassword}
              />
              <Field
                type="password"
                variant="standard"
                label="Confirm Password"
                id="confirmPassword"
                name="confirmPassword"
                as={TextField}
                error={form.touched.confirmPassword && Boolean(form.errors.confirmPassword)}
                helperText={form.touched.confirmPassword && form.errors.confirmPassword}
              />
            </div>
            <div styleName="row">
              <div styleName="button-row">
                <Button variant="outlined" onClick={() => cancel(form)}>
                  Cancel
                </Button>
                <div style={{flex: 1}} />
                <LoadingButton
                  variant="outlined"
                  loading={isPwResetLoading}
                  onClick={openPwResetDialog}>
                  Reset with Generated Password
                </LoadingButton>
                <LoadingButton
                  variant="contained"
                  loading={isPwChangeLoading}
                  onClick={() => form.handleSubmit()}
                >
                  Reset Password
                </LoadingButton>
              </div>
            </div>
            <YesNoDialog
              title="Confirm Password Reset"
              bodyText="This will reset the current password with a randomly generated one. Do you want to continue?"
              isOpen={isPwResetDialogOpen}
              onNo={closePwResetDialog}
              onYes={resetPasswordForUser}
            />
            <YesNoDialog
              title="Confirm Password Change"
              bodyText="This will reset the current password with the one specified. Do you want to continue?"
              isOpen={isPwChangeDialogOpen}
              onNo={closePwChangeDialog}
              onYes={changePasswordForUser}
            />
          </form>
        )}
      </Formik>
    </>
  );
}

export default ResetPasswordForm;

interface ResetPasswordFormDto {
  newPassword: '',
  confirmPassword: ''
}