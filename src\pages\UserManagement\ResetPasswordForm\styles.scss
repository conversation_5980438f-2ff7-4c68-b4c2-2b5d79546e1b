@import "../../../config/theme/vars.scss";


.read-only-grid {
  display: grid;
  grid-template-columns: repeat(2, auto 1fr);
  gap: 0 0.5rem;
  padding-top: 0.5rem;

  
  .full-width {
    // Start a column 2 and be 3 columns wide
    grid-column: 2 / span 3;
  }

  *:nth-child(odd) {
    text-align: right;
    color: rgba(0, 0, 0, 0.6);
  }
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0 2rem;
  padding-top: 1rem;

  .full-width {
    // Start a column 2 and be 3 columns wide
    grid-column: 1 / span 2;
  }

  // Stop form field's validation growing/shinking the dialog
  & > div {
    min-height: 72px;
  }

  @media ($ltxs) {
    grid-template-columns: 1fr;
  }
}

.row {
  display: flex;
  flex-direction: row;
  margin-top: 1rem;
}

.button-row {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}