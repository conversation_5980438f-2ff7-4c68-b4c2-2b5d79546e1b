import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { LoadingButton } from "@mui/lab";
import { AlertColor, Button, Checkbox, FormControlLabel, IconButton, TextField } from "@mui/material";
import { Field, Formik, FormikErrors, FormikHelpers, FormikProps } from "formik";
import { useState } from "react";
import { RediAutocompleteField } from "redi-formik-material";
import { DataSourceResult } from "redi-query-builder";
import { ExtendedApplicationUserDto, GetListRoleCDto, GetListTenantCDto, GetListUserCDto, NewUserCDto } from "redi-types";
import { v4 as uuidv4 } from 'uuid';
import * as yup from "yup";
import FormDropdownSelectOnTab from "../../../components/FormDropdownSelectOnTab/FormDropdownSelectOnTab";
import TenantService from "../../../services/demoServices/tenant";
import UserService from "../../../services/demoServices/user";
import { PhoneValidator } from "../../../utils/phoneValidator";
import './styles.scss';

interface Props {
  initialValues: ExtendedApplicationUserDto | NewUserCDto;
  allRoles: GetListRoleCDto[];
  addUserMode?: boolean;
  onCancel?: () => void;
  onSave?: (message: string, severity: AlertColor, refreshData?: boolean) => void;
}

function UserForm(props: Props) {

  const baseValidation = {
    firstName: yup.string().required('Enter first name'),
    lastName: yup.string().required('Enter first name'),
    phone: yup.string().test('phoneTest', (data) => {
        return PhoneValidator.validate(data.value);
    }, (element:any) => {
        return !PhoneValidator.validate(element);
    }),
    email: yup.string().email('Enter a valid email').required('Email is required'),
    roleId: yup.string().required('Please select a role')
  }

  let schema = yup.object(
    props.addUserMode // tenantName does not exist on NewUserCDto
    ? baseValidation
    : {
        ...baseValidation,
        tenantId: yup.string().required('Please select a tenant')
      }
  );

  const [isLoading, setIsLoading] = useState(false);

  function cancel(form: FormikProps<ExtendedApplicationUserDto | NewUserCDto>) {
    form.resetForm();
    props.onCancel && props.onCancel();
  }

  async function save(data: ExtendedApplicationUserDto | NewUserCDto, actions: FormikHelpers<any>) {
    if (props.addUserMode) {
      setIsLoading(true);
      // Add User TODO: Backend needs to accept call without these fields being set
      data.userId = uuidv4();
      data.createdByName = 'test'; 
      data.modifiedByName = 'test';

      const response = await UserService.Create(data as NewUserCDto);
      if (!response.error) {
        props.onSave && props.onSave('Successfully Created User', 'success', true);
      } else {
        props.onSave && props.onSave('Failed to Create User', 'error');
      }
      setIsLoading(false);
    } else {
      const response = await UserService.UpdateUser(data as ExtendedApplicationUserDto);
      if (!response.error) {
        props.onSave && props.onSave('Successfully Updated User', 'success', true);
      } else {
        props.onSave && props.onSave('Failed to Update User', 'error');
      }
    }
  }

  return (
    <Formik<ExtendedApplicationUserDto | NewUserCDto>
      enableReinitialize // Rerender when props changes (initialValues)
      validationSchema={schema}
      initialValues={props.initialValues}
      onSubmit={(data, actions) => {
        save(data, actions);
      }}
    >
      {(form) => (
        <form onSubmit={form.handleSubmit}>
          <div styleName="container">
            {
              !props.addUserMode &&
              <div styleName="row">
                <div styleName="header">User Details</div>
                <div styleName="row">
                  <IconButton onClick={() => form.handleSubmit()}>
                    <FontAwesomeIcon icon="check" />
                  </IconButton>
                  <IconButton onClick={() => cancel(form)}>
                    <FontAwesomeIcon icon="close" />
                  </IconButton>
                </div>
              </div>
            }            
            <div styleName="form-grid">
              <Field
                variant="standard"
                id="firstName"
                name="firstName"
                label="First Name"
                as={TextField}
                error={form.touched.firstName && Boolean(form.errors.firstName)}
                helperText={form.touched.firstName && form.errors.firstName}
              />
              <Field
                variant="standard"
                id="lastName"
                name="lastName"
                label="Last Name"
                as={TextField}
                error={form.touched.lastName && Boolean(form.errors.lastName)}
                helperText={form.touched.lastName && form.errors.lastName}
              />
              <Field
                variant="standard"
                id="phone"
                name="phone"
                label="Phone"
                as={TextField}
                error={form.touched.phone && Boolean(form.errors.phone)}
                helperText={form.touched.phone && form.errors.phone}
              />
              <Field
                variant="standard"
                id="email"
                name="email"
                label="Email Address"
                as={TextField}
                error={form.touched.email && Boolean(form.errors.email)}
                helperText={form.touched.email && form.errors.email}
              />
              <FormDropdownSelectOnTab
                label="Role"
                listItems={props.allRoles}
                form={form}
                propertyKey="roleId"
              />
              {
                !props.addUserMode && // tenantName does not exist on NewUserCDto
                <Field
                  id="tenantId"
                  name="tenantId"
                  label="Tenant"
                  as={RediAutocompleteField}
                  initialValue={(props.initialValues as ExtendedApplicationUserDto)?.tenant?.name}
                  minLength={0}
                  autoSelectFirst
                  callService={(query: string) => new Promise<DataSourceResult<GetListTenantCDto>>((resolve, reject) => {
                    TenantService.GetListQuery(undefined, undefined, query)
                      .then((data) => {
                        if (data.data) {
                          const dsr: DataSourceResult<GetListTenantCDto> = {
                            data: data.data.list,
                            total: data.data.totalNumOfRows
                          };
                          resolve(dsr);
                        } else {
                          reject('No data');
                        }
                      })
                      .catch(error => error);
                  })}
                  fieldValue="tenantId"
                  displayValue="name"
                  onChange={(val: string) => {
                    form.setFieldValue("tenantId", val)
                  }}
                  error={Boolean((form.errors as FormikErrors<ExtendedApplicationUserDto>).tenantId)}
                  helperText={(form.errors as FormikErrors<ExtendedApplicationUserDto>).tenantId}
                />
              }
              <Field
                variant="standard"
                id="position"
                name="position"
                label="Position"
                as={TextField}
                error={form.touched.position && Boolean(form.errors.position)}
                helperText={form.touched.position && form.errors.position}
              />
              <FormControlLabel 
                control={
                  <Field
                    id="isPrimaryContact"
                    name="isPrimaryContact"
                    as={Checkbox}
                    checked={form.values.isPrimaryContact}
                    error={((form.touched.isPrimaryContact && Boolean(form.errors.isPrimaryContact)) ?? "").toString()} // Fix error for boolean fields
                  />
                }
                label="Primary Contact"
              />
              <Field
                id="managerUserId"
                name="managerUserId"
                label="Manager"
                as={RediAutocompleteField}
                initialValue={(props.initialValues as ExtendedApplicationUserDto)?.managerName}
                minLength={0}
                autoSelectFirst
                callService={(query: string) => new Promise<DataSourceResult<GetListUserCDto>>((resolve, reject) => {
                  UserService.List(undefined, undefined, query)
                    .then((data) => {
                      if (data.data) {
                        const dsr: DataSourceResult<GetListUserCDto> = {
                          data: data.data.list,
                          total: data.data.totalNumOfRows
                        };
                        resolve(dsr);
                      } else {
                        reject('No data');
                      }
                    })
                    .catch(error => error);
                })}
                fieldValue="userId"       // From callService response
                displayValue="fullName"   // From callService response
                onChange={(val: string, obj: GetListUserCDto) => {
                  // Save to form fields
                  form.setFieldValue("managerUserId", obj.userId);
                  form.setFieldValue("managerName", obj.fullName);
                }}
                error={Boolean(form.errors.managerUserId)}
                helperText={form.errors.managerUserId}
              />
              <Field
                multiline
                rows={4}
                variant="standard"
                id="note"
                name="note"
                label="Note"
                as={TextField}
                error={form.touched.note && Boolean(form.errors.note)}
                helperText={form.touched.note && form.errors.note}
              />
            </div>
            {
              props.addUserMode &&
              <div styleName="button-row">
                <Button variant="outlined" onClick={() => cancel(form)}>
                  Cancel
                </Button>
                <LoadingButton
                  variant="contained"
                  loading={isLoading}
                  onClick={() => form.handleSubmit()}
                >
                  Create
                </LoadingButton>
              </div>
            }
          </div>        
        </form>
      )}
    </Formik>
  );
}

export default UserForm;