import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { useEffect, useState } from "react";
import { useNavigate } from 'react-router-dom';
import { ApplicationUser, GetListClaimCDto, GetListRoleCDto, GetListUserManagementCDto, NewUserCDto } from "redi-types";
import DataTable, { TableHeader } from "../../components/DataTable/DataTable";
import DraggableDialog from "../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../components/YesNoDialog/YesNoDialog";
import RoleService from "../../services/demoServices/role";
import UserService from "../../services/demoServices/user";
import { dateFormatOptions } from "../../utils/dateFormOptions";
import EditNoteForm from "./EditNoteForm/EditNoteForm";
import EditPermissionsForm from "./EditPermissionsForm/EditPermissionsForm";
import ResetPasswordForm from "./ResetPasswordForm/ResetPasswordForm";
import UserForm from "./UserForm/UserForm";
import './styles.scss';

interface Props {}

function UserManagement(props: Props) {

  const navigate = useNavigate();
  
  const [clickedUser, setClickedUser] = useState<GetListUserManagementCDto>();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);

  // Data for dialogs
  const [allClaims, setAllClaims] = useState<GetListClaimCDto[]>([]);
  const [allRoles, setAllRoles] = useState<GetListRoleCDto[]>([]);

  // Dialog
  const [isPermissionsDialogOpen, setIsPermissionsDialogOpen] = useState(false);
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [isEditNoteDialogOpen, setIsEditNoteDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

  // Required fields + fields from add form
  const initialValues: NewUserCDto = {
    userId: '',
    email: '',
    phone: '',
    firstName: '',
    lastName: '',
    fullName: '',
    roleName: '',
    isPrimaryContact: true,
    note: '',
    password: 'Letsgo.123', // TODO: Should be able to send without a password?
    position: '',
    roleId: '',
    claims: []
  };

  useEffect(() => {
    // Modal Data
    (async () => {
      const allClaimsResponse = await RoleService.GetAllClaims();
      const allRolesResponse = await RoleService.GetAllRoles();

      if (!allClaimsResponse.error && allClaimsResponse.data) {
        setAllClaims(allClaimsResponse.data);
      }

      if (!allRolesResponse.error && allRolesResponse.data) {
        setAllRoles(allRolesResponse.data.list);
      }
    })();
  }, []);

  const tableHeaders: TableHeader[] = [
    { id: 'FullName', label: 'Name', isSortable: true},
    { id: 'Email', label: 'Email', isSortable: true},
    { id: 'Phone', label: 'Phone', isSortable: true},
    { id: 'isEnabled', label: 'Enabled'},
    { id: 'LastAccess', label: 'Last Access', isSortable: true},
    { id: 'LockoutEndDateUtc', label: 'Lockout End Date', isSortable: true},
    { id: 'RoleName', label: 'Role', isSortable: true},
    { id: 'Position', label: 'Position', isSortable: true},
    { id: 'isPrimaryContact', label: 'Primary Contact'},
    { id: 'ManagerName', label: 'Manager', isSortable: true},
    { id: 'note', label: 'Note'},
    { id: 'menu', label: ''} // Blank for row menu button
  ];

  function renderTableRow(data: GetListUserManagementCDto)
  {
    return (
      <>
        {/* Primary column should be bolded (Does not always mean the far left column) */}
        <TableCell>
          <div><b>{data.fullName}</b></div>
        </TableCell>              
        <TableCell>{data.email.toLowerCase()}</TableCell>
        <TableCell>{data.phone}</TableCell>
        <TableCell styleName={data.isEnabled ? "green" : "red"}>
          {data.isEnabled ? 'Yes' : 'No'}
        </TableCell>
        <TableCell>
          {
            data?.lastAccess
            ? new Date(data.lastAccess).toLocaleDateString('en-AU', dateFormatOptions)
            : 'N/A'
          }
        </TableCell>
        <TableCell>
          {
            data?.lockoutEndDateUtc
            ? new Date(data.lockoutEndDateUtc).toLocaleDateString('en-AU', dateFormatOptions)
            : 'N/A'
          }
        </TableCell>
        <TableCell>{data.roleName}</TableCell>
        <TableCell>{data.position}</TableCell>
        <TableCell>{data.isPrimaryContact ? 'Yes' : 'No'}</TableCell>
        <TableCell>{data.managerName}</TableCell>
        <TableCell>{data.note}</TableCell>
        <TableCell align="right">
          <RowMenu rowData={data} />
        </TableCell>
      </>
    );
  };

  /* Add User Dialog */

  async function onAddUserDialogSave(message: string, severity: AlertColor) {
    setIsAddUserDialogOpen(false);
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
  }

  /* User Permissions Dialog */

  function closePermissionsDialog() {
    setIsPermissionsDialogOpen(false);
    setClickedUser(undefined);
  }

  async function onPermissionsDialogSave(message: string, severity: AlertColor) {
    closePermissionsDialog();
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    // Refresh table data
    setRefreshTableTrigger(trigger => trigger + 1);
  }

  /* Reset Password Dialog */

  function closeResetPasswordDialog() {
    setIsResetPasswordDialogOpen(false);
    setClickedUser(undefined);
  }

  function onPasswordDialogSave(message: string, severity: AlertColor) {
    closeResetPasswordDialog();
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
  }

  /* Edit Note Dialog */

  function closeEditNoteDialog() {
    setIsEditNoteDialogOpen(false);
    setClickedUser(undefined);
  }

  async function onEditNoteDialogSave(message: string, severity: AlertColor) {
    closeEditNoteDialog();
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
      // Refresh table data
    setRefreshTableTrigger(trigger => trigger + 1);
  }

  /* Delete User Dialog */

  function closeDeleteDialog() {
    setIsDeleteDialogOpen(false);
  }

  async function deleteUser() {
    closeDeleteDialog();
    if (clickedUser) {
      const response = await UserService.DeleteUser(clickedUser.userId);
      if (!response.error) {
        setShowSnackbar(true);
        setSnackbarMessage('Successfully deleted user');
        setSnackbarSeverity('success');
        // Refresh table data
        setRefreshTableTrigger(trigger => trigger + 1);
      } else {
        setShowSnackbar(true);
        setSnackbarMessage('Failed to delete user');
        setSnackbarSeverity('error');
      }
    }
    setClickedUser(undefined);
  }

  const Dialogs = () => {
    return (
      <>
        {/* Add New User Dialog */}
        <DraggableDialog title="Add New User" isOpen={isAddUserDialogOpen} onCancel={() => setIsAddUserDialogOpen(false)}>
          <UserForm
            addUserMode
            initialValues={initialValues}
            allRoles={allRoles}
            onCancel={() => setIsAddUserDialogOpen(false)}
            onSave={onAddUserDialogSave}
          />
        </DraggableDialog>

        {/* Edit Permissions Dialog */}
        <DraggableDialog title="User Permissions" isOpen={isPermissionsDialogOpen} onCancel={closePermissionsDialog}>
          <EditPermissionsForm
            isModal
            user={clickedUser}
            allClaims={allClaims}
            allRoles={allRoles}
            onCancel={closePermissionsDialog}
            onSave={onPermissionsDialogSave}
          />
        </DraggableDialog>

        {/* Reset Password Dialog */}
        <DraggableDialog title="Reset User's Password" isOpen={isResetPasswordDialogOpen} onCancel={closeResetPasswordDialog}>
          <ResetPasswordForm
            userId={clickedUser?.userId ?? ''}
            onCancel={closeResetPasswordDialog}
            onSave={onPasswordDialogSave}
          />
        </DraggableDialog>

        {/* Edit Note Dialog */}
        <DraggableDialog title="Edit Note" isOpen={isEditNoteDialogOpen} onCancel={closeEditNoteDialog}>
          <EditNoteForm
            initialValues={clickedUser}
            onCancel={closeEditNoteDialog}
            onSave={onEditNoteDialogSave}
          />
        </DraggableDialog>

        {/* Delete User Dialog */}
        <YesNoDialog
          title="Delete User"
          bodyText="Are you sure? This cannot be reversed"
          isOpen={isDeleteDialogOpen}
          onNo={closeDeleteDialog}
          onYes={deleteUser}
        />
      </>
    );
  }

  const RowMenu = (props: { rowData: ApplicationUser }) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const isOpen = Boolean(anchorEl);
    const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };
    const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();
      setAnchorEl(null);
    };
    const handleEditPermissions = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedUser(props.rowData);
      setIsPermissionsDialogOpen(true);
    };
    const handleResetUserPassword = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedUser(props.rowData);
      setIsResetPasswordDialogOpen(true);
    };
    const handleEditNote = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedUser(props.rowData);
      setIsEditNoteDialogOpen(true)
    };
    const handleDelete = (event: React.MouseEvent) => {
      event.stopPropagation();
      setAnchorEl(null);
      setClickedUser(props.rowData);
      setIsDeleteDialogOpen(true);
    };

    return (
      <>
        <IconButton 
          id="actions-list"
          aria-controls={isOpen ? 'basic-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={isOpen ? 'true' : undefined}
          onClick={openMenu}
        >
          <FontAwesomeIcon styleName="row-menu-icon" icon="ellipsis-v" />
        </IconButton>
        <Menu
          id="actions-menu"
          anchorEl={anchorEl}
          open={isOpen}
          onClose={handleClose}
          MenuListProps={{'aria-labelledby': 'menu-button'}}
        >
          <MenuItem onClick={handleEditPermissions}>
            <ListItemText>Edit Permissions</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleResetUserPassword}>
            <ListItemText>Reset User's Password</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleEditNote}>
            <ListItemText>Edit Note</ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDelete}>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>
      </>
    );
  }

  return (
    <div style={{overflow: 'hidden'}}>
      <div styleName="card">
        <DataTable<GetListUserManagementCDto, "userId">
          primaryKeyProperty="userId"
          title="Users"
          tableId='base-userManagement-'
          pageSize={10}
          initialSortColumn="FullName"
          refreshTableTrigger={refreshTableTrigger}
          showFilters={false}
          tableHeight={600}
          addButtonLabel="New User"
          addButtonOnClick={() => setIsAddUserDialogOpen(true)}
          tableHeaders={tableHeaders}
          renderTableRow={renderTableRow}
          onRowClick={(row) => navigate(`/UserManagement/${row.userId}`)}
          callService={(inListParameters, inSearch) => UserService.ListForManagement(inListParameters, inSearch)}
        />

        <Dialogs />

        <Snackbar open={showSnackbar} 
          autoHideDuration={4000} 
          onClose={() => setShowSnackbar(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default UserManagement;