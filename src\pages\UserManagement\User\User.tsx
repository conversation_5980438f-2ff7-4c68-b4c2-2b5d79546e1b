import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Alert, AlertColor, CircularProgress, IconButton, ListItemIcon, ListItemText, Menu, MenuItem, Snackbar } from "@mui/material";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ExtendedApplicationUserDto, GetListClaimCDto, GetListRoleCDto } from "redi-types";
import RoleService from "../../../services/demoServices/role";
import UserService from "../../../services/demoServices/user";
import { dateFormatOptions } from "../../../utils/dateFormOptions";
import EditPermissionsForm from "../EditPermissionsForm/EditPermissionsForm";
import UserDetails from "./UserDetails/UserDetails";
import './styles.scss';
import UserAudit from "./UserAudit/UserAudit";

interface Props {}

function User(props: Props) {

  const { id } = useParams();   // Url Params
  
  const [tabIndex, setTabIndex] = useState(0);

  // User Details
  const [user, setUser] = useState<ExtendedApplicationUserDto>();
  const [allClaims, setAllClaims] = useState<GetListClaimCDto[]>([]);
  const [allRoles, setAllRoles] = useState<GetListRoleCDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Snackbar
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>();

  const [hasTab2BeenClicked, setHasTab2BeenClicked] = useState(false);

  useEffect(() => {
    // Get Data from API
    (async () => {
      await getUserDetails();

      const allClaimsResponse = await RoleService.GetAllClaims();
      if (!allClaimsResponse.error && allClaimsResponse.data) {
        setAllClaims(allClaimsResponse.data);
      }
      const allRolesResponse = await RoleService.GetAllRoles();
      if (!allRolesResponse.error && allRolesResponse.data) {
        setAllRoles(allRolesResponse.data.list);
      }
      setIsLoading(false);
    })();
  }, []);

  async function getUserDetails() {
    if (id) {
      const getUserResponse = await UserService.GetUser(id);
      if (!getUserResponse.error && getUserResponse.data) {
        setUser(getUserResponse?.data);
      }
    }
  }

  async function updateSnackbar(message: string, severity: AlertColor, refreshData: boolean = false) {
    setShowSnackbar(true);
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    if (refreshData) {
      await getUserDetails();
    }
  }

  const ActionsButton = () => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    };
    function handleClose() {
      setAnchorEl(null);
    };

    return (
      <>
        <IconButton
          id="actions-list"
          aria-controls={open ? 'user-details-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
        >
          <FontAwesomeIcon styleName="menu-icon" icon="ellipsis-v" />
        </IconButton>

        <Menu
          id="actions-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={() => handleClose()}
          MenuListProps={{'aria-labelledby': 'menu-button'}}
        >
          <MenuItem onClick={() => handleClose()}>
            <ListItemIcon>
              <FontAwesomeIcon icon="user-pen" />
            </ListItemIcon>
            <ListItemText>Action 1</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleClose()}>
            <ListItemIcon>
              <FontAwesomeIcon icon="triangle-exclamation" />
            </ListItemIcon>
            <ListItemText>Action 2</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleClose()}>
            <ListItemIcon>
              <FontAwesomeIcon icon="lock" />
            </ListItemIcon>
            <ListItemText>Action 3</ListItemText>
          </MenuItem>
        </Menu>
      </>
    );
  }

  return (
    <div styleName="page">
      {
        isLoading
        ?
          <div styleName="loading">
            <CircularProgress color="primary" size={50} />
          </div>
        :
          <>
            <div styleName="container">
              {/* Note: Having this section in its own component will cause <UserAudit/> to remount on tab change */}
              <div styleName="content">
                <div styleName="summary-bar">
                  <div styleName="column">
                    <div styleName="top">User</div>
                    <div styleName="bottom">{user?.fullName}</div>
                  </div>
                  <div styleName="column">
                    <div styleName="middle lowercase">{user?.email}</div>
                  </div>
                  <div styleName="column">
                    <div styleName="middle">{user?.phone}</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">Last Access</div>
                    <div styleName="bottom">
                      {
                        user?.lastAccess
                        ? new Date(user.lastAccess).toLocaleDateString('en-AU', dateFormatOptions)
                        : 'N/A'
                      }
                    </div>
                  </div>
                  <div styleName="column">
                    <div styleName="middle">{user?.isEnabled ? 'Enabled' : 'Disabled'}</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">Role</div>
                    <div styleName="bottom">{user?.role?.name ?? 'N/A'}</div>
                  </div>
                  <div styleName="column">
                    <div styleName="top">Note</div>
                    <div styleName="bottom">{user?.note}</div>
                  </div>
                  <ActionsButton />
                </div>

                <div styleName="main-content">
                  <div styleName="row">
                    <div styleName={tabIndex === 0 ? "tab active" : "tab"} onClick={() => setTabIndex(0)}>
                      User Details
                    </div>
                    <div styleName={tabIndex === 1 ? "tab active" : "tab"} onClick={() => { setTabIndex(1); setHasTab2BeenClicked(true); }}>
                      User Audit
                    </div>
                  </div>

                  <div styleName="tab-body">
                    <div styleName="tab-grid-1" style={{display: tabIndex === 0 ? 'grid' : 'none'}}>
                      <div>
                        <UserDetails
                          initialValues={user as ExtendedApplicationUserDto}
                          allClaims={allClaims}
                          allRoles={allRoles}
                          onSave={updateSnackbar}
                        />
                      </div>
                      <div>
                        {/* This needs to somehow update the Role in the header bar above */}
                        <EditPermissionsForm
                          user={user as ExtendedApplicationUserDto}
                          allClaims={allClaims}
                          allRoles={allRoles}
                          onSave={updateSnackbar}
                        />
                      </div>
                    </div>
                    <div styleName="tab-grid-2" style={{display: tabIndex === 1 ? 'grid' : 'none'}}>
                      {
                        // Don't want to load until first visible, otherwise auto scroll fails cause no dom
                        hasTab2BeenClicked && <UserAudit userId={id} />
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Snackbar open={showSnackbar} 
              autoHideDuration={4000} 
              onClose={() => setShowSnackbar(false)}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}>
              <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
                {snackbarMessage}
              </Alert>
            </Snackbar>
          </>
      }
    </div>
  )
}

export default User;