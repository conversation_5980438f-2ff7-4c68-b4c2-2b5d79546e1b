import { TableCell } from "@mui/material";
import { GetListUserAuditCDto } from "redi-types";
import DataTable, { TableHeader } from "../../../../components/DataTable/DataTable";
import UserAuditService from "../../../../services/demoServices/userAudit";
import { dateFormatOptions } from "../../../../utils/dateFormOptions";
import './styles.scss';

interface Props {
  userId?: string;
}

function UserAudit(props: Props) {
  
  const tableHeaders: TableHeader[] = [
    { id: 'Name', label: 'User'},
    { id: 'CreatedOn', label: 'Date', isSortable: true},
    { id: 'EventType', label: 'Event Type', isSortable: true},
    { id: 'IpAddress', label: 'IP Address'},
    { id: 'BrowserDescription', label: 'Browser'}
  ];

  function renderTableRow(data: GetListUserAuditCDto)
  {
    return (
      <>
        {/* Primary column should be bolded (Does not always mean the far left column) */}
        <TableCell>
          <div><b>{data.userName}</b></div>
        </TableCell>
        <TableCell>
          {
            data.createdOn
            ? new Date(data.createdOn).toLocaleDateString('en-AU', dateFormatOptions)
            : 'N/A'
          }
        </TableCell>
        <TableCell>{data.eventType}</TableCell>
        <TableCell>{data.ipAddress}</TableCell>
        <TableCell>{data.browserDescription}</TableCell>
      </>
    );
  }

  return (
    <DataTable<GetListUserAuditCDto, "userAuditId">
      primaryKeyProperty="userAuditId"
      title="User Audit"
      tableId='base-userAudit-'
      pageSize={10}
      initialSortColumn="-CreatedOn"
      showFilters={false}
      tableHeight={540}
      tableHeaders={tableHeaders}
      renderTableRow={renderTableRow}
      callService={(inListParameters, inSearch) => UserAuditService.GetListQuery(inListParameters, inSearch, props.userId)}
    />
  );
}

export default UserAudit;