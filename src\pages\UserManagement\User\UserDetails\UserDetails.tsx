import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { AlertColor, IconButton, ListItemIcon, ListItemText, Menu, MenuItem } from "@mui/material";
import { useState } from "react";
import { ExtendedApplicationUserDto, GetListClaimCDto, GetListRoleCDto } from "redi-types";
import DraggableDialog from "../../../../components/DraggableDialog/DraggableDialog";
import YesNoDialog from "../../../../components/YesNoDialog/YesNoDialog";
import UserService from "../../../../services/demoServices/user";
import EditNoteForm from "../../EditNoteForm/EditNoteForm";
import ResetPasswordForm from "../../ResetPasswordForm/ResetPasswordForm";
import UserForm from "../../UserForm/UserForm";
import './styles.scss';

interface Props {
  initialValues: ExtendedApplicationUserDto;
  allClaims: GetListClaimCDto[];
  allRoles: GetListRoleCDto[];
  onSave?: (message: string, severity: AlertColor, refreshData?: boolean) => void;
}

function UserDetails(props: Props) {

  const [isEdit, setIsEdit] = useState(false);
  // Dialogs
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false);
  const [isEditNoteDialogOpen, setIsEditNoteDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  /* Edit User Dialog */

  async function save(message: string, severity: AlertColor, refreshData: boolean = false) {
    setIsEdit(false);
    props.onSave && props.onSave(message, severity, refreshData);
  }

  /* Reset Password Dialog */

  function onPasswordDialogSave(message: string, severity: AlertColor) {
    setIsResetPasswordDialogOpen(false);
    props.onSave && props.onSave(message, severity);
  }

  /* Edit Note Dialog */

  async function onEditNoteDialogSave(message: string, severity: AlertColor) {
    setIsEditNoteDialogOpen(false);
    props.onSave && props.onSave(message, 'success', true);
  }

  /* Toggle User Enabled */

  async function toggleUserEnabled() {
    const isEnabled = !props.initialValues.isEnabled;
    const updatedUser: ExtendedApplicationUserDto = {
      ...props.initialValues,
      isEnabled
    };
    const response = await UserService.UpdateUser(updatedUser);
    const enabledString = isEnabled ? 'enabled' : 'disabled';
    if (!response.error) {
      props.onSave && props.onSave(`Successfully ${enabledString} user`, 'success', true);
    } else {
      props.onSave && props.onSave(`Failed to ${enabledString} user`, 'error');
    }
  }

  /* Clear User Lockout */

  async function clearUserLockout() {
    const updatedUser: ExtendedApplicationUserDto = {
      ...props.initialValues,
      lockoutEnabled: false,
      lockoutEndDateUtc: undefined
    };
    const response = await UserService.UpdateUser(updatedUser);
    if (!response.error) {
      props.onSave && props.onSave('Successfully cleared user lockout', 'success', true);
    } else {
      props.onSave && props.onSave('Failed to cleared user lockout', 'error');
    }
  }

  /* Delete User Dialog */

  async function deleteUser() {
    setIsDeleteDialogOpen(false);
    const response = await UserService.DeleteUser(props.initialValues.userId);
    if (!response.error) {
      props.onSave && props.onSave('Successfully deleted user', 'success', true);
    } else {
      props.onSave && props.onSave('Failed to delete user', 'error');
    }
  }

  const ActionsButton = () => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
    };
    function handleClose(menuItem?: DropdownMenuItems) {
      setAnchorEl(null);

      switch (menuItem) {     
        case DropdownMenuItems.ResetUserPassword:
          setIsResetPasswordDialogOpen(true);
          break;      
        case DropdownMenuItems.EditNote:
          setIsEditNoteDialogOpen(true);
          break;
        case DropdownMenuItems.ToggleUserEnabled:
          toggleUserEnabled();
          break;
        case DropdownMenuItems.ClearUserLockout:
          clearUserLockout();
          break;
        case DropdownMenuItems.DeleteUser:
          setIsDeleteDialogOpen(true);
          break;
        default:
          break;
      }
    };

    return (
      <>
        <IconButton
          id="actions-list"
          aria-controls={open ? 'user-details-menu' : undefined}
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
        >
          <FontAwesomeIcon styleName="menu-icon" icon="ellipsis-v" />
        </IconButton>

        <Menu
          id="actions-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={() => handleClose()}
          MenuListProps={{'aria-labelledby': 'menu-button'}}
        >
          <MenuItem onClick={() => handleClose(DropdownMenuItems.ResetUserPassword)}>
            <ListItemIcon>
              <FontAwesomeIcon icon="key" />
            </ListItemIcon>
            <ListItemText>Reset Password</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleClose(DropdownMenuItems.EditNote)}>
            <ListItemIcon>
              <FontAwesomeIcon icon="note" />
            </ListItemIcon>
            <ListItemText>Edit Note</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleClose(DropdownMenuItems.ToggleUserEnabled)}>
            <ListItemIcon>
              <FontAwesomeIcon icon={props.initialValues.isEnabled ? "user-lock" : "user-unlock"} />
            </ListItemIcon>
            <ListItemText>{props.initialValues.isEnabled ? "Disable User" : "Enable User"}</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleClose(DropdownMenuItems.ToggleUserEnabled)}>
            <ListItemIcon>
              <FontAwesomeIcon icon="lock" />
            </ListItemIcon>
            <ListItemText>Clear Lockout</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleClose(DropdownMenuItems.DeleteUser)}>
            <ListItemIcon>
              <FontAwesomeIcon icon="trash" />
            </ListItemIcon>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>
      </>
    );
  }

  const Dialogs = () => {
    return (
      <>
        {/* Reset Password Dialog */}
        <DraggableDialog title="Reset User's Password" isOpen={isResetPasswordDialogOpen} onCancel={() => setIsResetPasswordDialogOpen(false)}>
          <ResetPasswordForm
            userId={props.initialValues?.userId ?? ''}
            onCancel={() => setIsResetPasswordDialogOpen(false)}
            onSave={onPasswordDialogSave}
          />
        </DraggableDialog>

        {/* Edit Note Dialog */}
        <DraggableDialog title="Edit Note" isOpen={isEditNoteDialogOpen} onCancel={() => setIsEditNoteDialogOpen(false)}>
          <EditNoteForm
            initialValues={props.initialValues}
            onCancel={() => setIsEditNoteDialogOpen(false)}
            onSave={onEditNoteDialogSave}
          />
        </DraggableDialog>

        {/* Delete User Dialog */}
        <YesNoDialog
          title="Delete User"
          bodyText="Are you sure? This cannot be reversed"
          isOpen={isDeleteDialogOpen}
          onNo={() => setIsDeleteDialogOpen(false)}
          onYes={deleteUser}
        />
      </>
    );
  }

  function renderView() {
    const user = props.initialValues;
    
    if (!user) {
      return null;
    }

    return (
      <div styleName="container">

        <div styleName="row">
          <div styleName="header">User Details</div>
          <div styleName="row">
            <IconButton onClick={() => setIsEdit((prev) => !prev)}>
              <FontAwesomeIcon icon="pen-to-square" />
            </IconButton>
            <ActionsButton />
          </div>
        </div>      

        <div styleName="grid">

          <div styleName="column">
            <div styleName="label">Name</div>
            <div styleName="value">{user.fullName}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Email</div>
            <div styleName="value lowercase">{user.email}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Phone</div>
            <div styleName="value">{user.phone}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Enabled</div>
            <div styleName={user.isEnabled ? "value green" : "value red"}>{user.isEnabled ? 'Yes' : 'No'}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Role</div>
            <div styleName="value">{user.roleName}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Tenant</div>
            <div styleName="value">{user.tenant?.name}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Position</div>
            <div styleName="value">{user.position}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Primary Contact</div>
            <div styleName="value">{user.isPrimaryContact ? 'Yes' : 'No'}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Manager</div>
            <div styleName="value">{user.managerName}</div>
          </div>

          <div styleName="column">
            <div styleName="label">Note</div>
            <div styleName="value">{user.note}</div>
          </div>

        </div>

      </div>
    )
  }

  function renderEdit() {
    return (
      <UserForm
        initialValues={props.initialValues}
        allRoles={props.allRoles}
        onCancel={() => setIsEdit(false)}
        onSave={save}
      />
    );
  }

  return (
    <>
      {
        !props.initialValues
        ?
          <div>Error: User data failed to load</div>
        :
          isEdit
          ?
            renderEdit()
          :
            renderView()
      }
      <Dialogs />
    </>
  );
}

export default UserDetails;

enum DropdownMenuItems {
  ResetUserPassword = 'resetUserPassword',
  EditNote = 'editNote',
  ToggleUserEnabled = 'ToggleUserEnabled',
  ClearUserLockout = 'ClearUserLockout',
  DeleteUser = 'deleteUser'
}