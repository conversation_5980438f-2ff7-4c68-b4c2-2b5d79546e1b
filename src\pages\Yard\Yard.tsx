import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import { CircularProgress } from "@mui/material";
import './styles.scss';
import { YardDetails } from "./YardDetails/YardDetails";
import { YardHeader } from "./YardHeader/YardHeader";
import { YardEvents } from "./YardEvents/YardEvents";
import ManageService from "../../services/manage";
import LoadingDataComponent from "../../components/LoadingDataComponent/LoadingDataComponent";

interface Props {}

export function Yard(props: Props) {
    const { id } = useParams();
    const [tabIndex, setTabIndex] = useState(0);
    const [yard, setYard] = useState<ManageListingWithDisplayGroupedDataAndMediaDto>();
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (id) {
            ManageService.get(Number(id), false, true, "JobDetail", undefined, undefined, true).then((data) => {
                if (!data.error && data.data) {
                    setYard(data.data);
                }
                setIsLoading(false);
            });
        }
    }, [id]);

    return (
        <div styleName="page">
            {isLoading ? (
                <div styleName="loading">
                    <CircularProgress color="primary" size={50} />
                </div>
            ) : (
                <>
                    <div styleName="container">
                        <div styleName="content">
                            <LoadingDataComponent isLoading={isLoading} data={yard}>
                                <YardHeader yard={yard!} />
                            </LoadingDataComponent>
                            <div styleName="main-content">
                                <div styleName="row">
                                    <div 
                                        styleName={`tab ${tabIndex === 0 ? 'active' : ''}`} 
                                        onClick={() => setTabIndex(0)}
                                    >
                                        Details
                                    </div>
                                    <div 
                                        styleName={`tab ${tabIndex === 1 ? 'active' : ''}`} 
                                        onClick={() => setTabIndex(1)}
                                    >
                                        Events
                                    </div>
                                </div>
                                <div styleName="tab-body">
                                    <div styleName="tab-grid-1" style={{ display: tabIndex === 0 ? 'grid' : 'none' }}>
                                        <LoadingDataComponent isLoading={isLoading} data={yard}>
                                            <YardDetails
                                                yard={yard!}
                                            />
                                        </LoadingDataComponent>
                                    </div>
                                    <div style={{ display: tabIndex === 1 ? 'block' : 'none' }}>
                                        <LoadingDataComponent isLoading={isLoading} data={yard}>
                                            <YardEvents yardId={id!} />
                                        </LoadingDataComponent>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
}

export default Yard;


