import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import './styles.scss';
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";

interface Props {
    yard: ManageListingWithDisplayGroupedDataAndMediaDto;
}

const JobSummaryDetails = withAsyncLoad<any>(() => import('jobcomponents/JobSummaryDetails'));

export function YardDetails(props: Props) {
    const { yard } = props;

    return (
        <div styleName="container">
            <div styleName="grid">
                <div styleName="column">
                    <div styleName="label">Rego</div>
                    <div styleName="value">{yard.fields?.carRegistration}</div>
                </div>
                {/* <div styleName="column">
                    <div styleName="label">Car Make/Model</div>
                    <div styleName="value">{yard.fields?.carMakeModel}</div>
                </div> */}
                <div styleName="column">
                    <div styleName="label">Customer Name</div>
                    <div styleName="value">{yard.fields?.customerName}</div>
                </div>
                <div styleName="column">
                    <div styleName="label">Customer Phone</div>
                    <div styleName="value">{yard.fields?.customerPhone}</div>
                </div>
            </div>

            <div styleName="section">
                <div styleName="sub-header">Transfer Details</div>
                <div styleName="grid">
                    <div styleName="column">
                        <div styleName="label">Date of Transfer</div>
                        <div styleName="value">{yard.fields?.transferDate}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Location Transfer</div>
                        <div styleName="value">{yard.fields?.towedTo}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Truck Allocated</div>
                        <div styleName="value">{yard.fields?.truckId}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Allocated By</div>
                        <div styleName="value">{yard.fields?.allocatedBy}</div>
                    </div>
                </div>
            </div>

            <div styleName="section">
                <JobSummaryDetails jobId={yard.parentEntityId!} />
            </div>
        </div>
    );
}

export default YardDetails;
