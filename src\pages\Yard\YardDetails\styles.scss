@import "../../../config/theme/vars.scss";

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.25rem;
}

.container {
  display: flex;
  flex-direction: column;

  .header {
    color: $primaryColor;
    font-size: 1.3rem;
    font-weight: 600;
  }

  .exit-button {
    margin-left: auto;
  }
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.column {
  color: #4E4949;
  font-size: 1rem;

  .label {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .value {
    font-weight: 400;

    a {
      margin-left: 8px;
      color: $primaryColor;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.section {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }

  .sub-header {
    color: $primaryColor;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
  }
}
