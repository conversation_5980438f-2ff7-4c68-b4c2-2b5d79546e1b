@import "../../../config/theme/vars.scss";

.row {
  display: flex;
  flex-direction: row;
}

.column {
  display: flex;
  flex-direction: column;
}

.field-container {
  margin-left: 4px;
  margin-right: 4px;
  
  &:first-child {
    margin-left: 0px;
  }
  
  &:last-child {
    margin-right: 0px;
  }
  
  > div {
    display: flex;
    flex: 1;
  }
}

.field-container.size-1 {
  flex: 1;
}

.field-container.size-2 {
  flex: 2;
}

.field-container.size-3 {
  flex: 3;
}

.field-container.size-4 {
  flex: 4;
}

.container {
  display: flex;
  flex-direction: column;
  
  > .row {
    justify-content: space-between;
    margin-bottom: 1.25rem;
    
    .header {
      color: $primaryColor;
      font-size: 1.3rem;
      font-weight: 600;
    }
    
    button {
      color: $primaryColor;
      
      svg {
        font-size: 1.1rem;
        width: 1.1rem;
        height: 1.1rem;
      }
    }
  }
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0 2rem;
  padding-top: 1rem;
  
  // Stop form field's validation growing/shrinking the dialog
  & > div {
    min-height: 72px;
  }
  
  & > label {
    margin: 0 0 1.8rem 0;
  }
}

.button-row {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}
