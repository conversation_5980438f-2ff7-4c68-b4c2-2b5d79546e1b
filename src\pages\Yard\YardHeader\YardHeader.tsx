import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import './styles.scss';
import DateTime from "../../../utils/datetime/dateTime";

interface Props {
    yard: ManageListingWithDisplayGroupedDataAndMediaDto;
}

export function YardHeader(props: Props) {
    const { yard } = props;

    return (
        <div styleName="summary-bar">
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.createdOn ? DateTime.format(yard.createdOn, "dd/MM/yyyy") : "N/A"}</div>
                    <div styleName="bottom">Created Date</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.fields?.jobDate ? DateTime.format(yard.fields?.jobDate, "dd/MM/yyyy") : "N/A"}</div>
                    <div styleName="bottom">Job Date</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.fields?.customerName ?? "N/A"}</div>
                    <div styleName="bottom">Customer Name</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.fields?.carRegistration ?? "N/A"}</div>
                    <div styleName="bottom">Rego</div>
                </div>
            </div>
            {/* <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.statusCode ?? "N/A"}</div>
                    <div styleName="bottom">Status</div>
                </div>
            </div> */}
        </div>
    );
}