import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import './styles.scss';

interface Props {
    yard: ManageListingWithDisplayGroupedDataAndMediaDto;
}

export function YardHeader(props: Props) {
    const { yard } = props;

    return (
        <div styleName="summary-bar">
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.fields?.JobNumber ?? "N/A"}</div>
                    <div styleName="bottom">Job Number</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.fields?.CarRegistration ?? "N/A"}</div>
                    <div styleName="bottom">Rego</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.fields?.Yard ?? "N/A"}</div>
                    <div styleName="bottom">Current Yard</div>
                </div>
            </div>
            <div styleName="column">
                <div styleName="align-left">
                    <div styleName="top">{yard.statusCode ?? "N/A"}</div>
                    <div styleName="bottom">Status</div>
                </div>
            </div>
        </div>
    );
}