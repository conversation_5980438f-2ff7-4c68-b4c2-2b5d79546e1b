import { Alert, AlertColor, IconButton, ListItemText, Menu, MenuItem, Snackbar, TableCell } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ListingWithDataAndMediaCDto } from "redi-types";
import './styles.scss';
import DataTable, { TableHeader } from "../../../components/DataTable/DataTable";
import DraggableDialog from "../../../components/DraggableDialog/DraggableDialog";
import { YardForm } from "../YardForm/YardForm";
import ListingService from "../../../services/listing";
import { ListingTypeEnum } from "../../../enum/listingTypeEnum";

interface Props {}

export function YardList(props: Props) {
  const navigate = useNavigate();
  const [refreshTableTrigger, setRefreshTableTrigger] = useState(0);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedYardLog, setSelectedYardLog] = useState<ListingWithDataAndMediaCDto>();
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<AlertColor | undefined>("success");

  const tableHeaders: TableHeader[] = [
    { id: "jobDate", label: "Job Date", isSortable: true },
    { id: "jobNumber", label: "Job Number" },
    { id: "towType", label: "Tow Type" },
    { id: "carRegistration", label: "Rego" },
    { id: "customerName", label: "Customer Name" },
    { id: "towedTo", label: "Location Transfer" },
    { id: "truckId", label: "Truck Allocated" },
    { id: "allocatedBy", label: "Allocated By" },
    { id: "actions", label: "Actions", isSortable: false, align: "right" }
  ];

  function closeAddDialog() {
    setIsAddDialogOpen(false);
    setSelectedYardLog(undefined);
  }

  function onAddDialogSave() {
    setRefreshTableTrigger(prev => prev + 1);
    setIsAddDialogOpen(false);
    setSnackbarMessage("Yard log created successfully");
    setSnackbarSeverity("success");
    setShowSnackbar(true);
  }

  function RowMenu({ rowData }: { rowData: ListingWithDataAndMediaCDto }) {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    };

    const handleClose = (event: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
      event?.stopPropagation();
      setAnchorEl(null);
    };

    const handleEdit = (event: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
      handleClose(event);
      event.stopPropagation();
      setAnchorEl(null);
      setSelectedYardLog(rowData);
      setIsAddDialogOpen(true);
    };

    return (
      <>
        <IconButton onClick={handleClick}>
          <FontAwesomeIcon icon="ellipsis-v" />
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
        >
          <MenuItem onClick={handleEdit}><ListItemText>Edit</ListItemText></MenuItem>
        </Menu>
      </>
    );
  }

  function renderTableRow(data: ListingWithDataAndMediaCDto) {
    return (
      <>
        <TableCell>{data.fields?.jobDate}</TableCell>
        <TableCell>{data.fields?.jobNumber}</TableCell>
        <TableCell>{data.fields?.towType}</TableCell>
        <TableCell>{data.fields?.carRegistration}</TableCell>
        <TableCell>{data.fields?.customerName}</TableCell>
        <TableCell>{data.fields?.towedTo}</TableCell>
        <TableCell>{data.fields?.truckId}</TableCell>
        <TableCell>{data.fields?.allocatedBy}</TableCell>
        <TableCell align="right">
          <RowMenu rowData={data} />
        </TableCell>
      </>
    );
  }

  return (
    <div styleName="no-overflow">
      <div styleName="card">
        <DataTable<ListingWithDataAndMediaCDto, "listingId">
          primaryKeyProperty="listingId"
          title="Yard Log"
          tableId="base-yard-list"
          pageSize={10}
          initialSortColumn="jobDate"
          refreshTableTrigger={refreshTableTrigger}
          tableHeight={600}
          tableHeaders={tableHeaders}
          renderTableRow={renderTableRow}
          addButtonLabel="New Yard Log"
          addButtonOnClick={() => setIsAddDialogOpen(true)}
          onRowClick={(row) => navigate(`/Yard/${row.listingId}`)}
          callService={(params, search) => ListingService.getList(
              undefined,
              undefined,
              "JobDetail",
              undefined,
              undefined,
              undefined,
              undefined,
              'Active',
              undefined,
              ListingTypeEnum.Job,
              undefined,
              undefined,
              undefined,
              undefined,
              params,
              undefined,
              undefined,
              true,
              undefined,
              undefined,
              undefined,
              true,
              undefined,
              search ? {
                "TruckId": { values: [search], label: "truckId", operator: "contains"  },
                "CarRegistration": { values: [search], label: "carRegistration", operator: "contains"  },
                "CarMakeModel": { values: [search], label: "carMakeModel", operator: "contains"  },
                "TowedTo": { values: [search], label: "towedTo", operator: "contains"  },
                "CustomerName": { values: [search], label: "customerName", operator: "contains"  },
                "AllocatedBy": { values: [search], label: "allocatedBy", operator: "contains"  },
                "JobNumber": { values: [search], label: "jobNumber", operator: "contains"  }
              } : undefined,
              undefined
          )}
        />

        {isAddDialogOpen ?
        <DraggableDialog
          maxWidth="xl"
          title={selectedYardLog ? "Edit Yard Log" : "Add New Yard Log"}
          isOpen={isAddDialogOpen}
          onCancel={closeAddDialog}
          fullWidth={true}
        >
          <YardForm
            referenceNo={selectedYardLog?.referenceNo}
            onCancel={closeAddDialog}
            onSave={onAddDialogSave}
          />
        </DraggableDialog> : null}

        <Snackbar
          open={showSnackbar}
          autoHideDuration={6000}
          onClose={() => setShowSnackbar(false)}
        >
          <Alert onClose={() => setShowSnackbar(false)} severity={snackbarSeverity}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </div>
    </div>
  );
}

export default YardList;