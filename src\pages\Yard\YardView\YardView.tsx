import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconButton } from "@mui/material";
import { ManageListingWithDisplayGroupedDataAndMediaDto } from "redi-types";
import "./styles.scss";
import withAsyncLoad from "../../../components/HOC/withAsyncLoad";
import DateTime from "../../../utils/datetime/dateTime";

interface Props {
    yard: ManageListingWithDisplayGroupedDataAndMediaDto;
    onEdit: () => void;
    onSave?: (data?: ManageListingWithDisplayGroupedDataAndMediaDto) => void | undefined;
}

const JobSummaryDetails = withAsyncLoad<any>(() => import('jobcomponents/JobSummaryDetails'));

export function YardView(props: Props) {
    const { yard, onEdit, onSave } = props;

    return (
        <div styleName="container">
            <div styleName="row">
                <div styleName="header">Yard Log</div>
                <div styleName="row">
                    <IconButton onClick={onEdit}>
                        <FontAwesomeIcon icon={["fas", "edit"]} />
                    </IconButton>
                </div>
            </div>
            <div styleName="section">
                <div styleName="grid">
                    <div styleName="column">
                        <div styleName="label">Rego</div>
                        <div styleName="value">{yard.fields?.carRegistration}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Customer Name</div>
                        <div styleName="value">{yard.fields?.customerName}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Customer Phone</div>
                        <div styleName="value">{yard.fields?.customerPhone}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Invoice No</div>
                        <div styleName="value">{yard.fields?.invoiceNumber}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Job No</div>
                        <div styleName="value">{yard.fields?.jobNumber}</div>
                    </div>
                </div>
            </div>

            <div styleName="section">
                <div styleName="sub-header">Transfer Details</div>
                <div styleName="grid">
                    <div styleName="column">
                        <div styleName="label">Date of Transfer</div>
                        <div styleName="value">{yard.fields?.transferDate ? DateTime.format(yard.fields?.transferDate, "dd/MM/yyyy") : "N/A"}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Location Transfer</div>
                        <div styleName="value">{yard.fields?.towedTo}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Truck Allocated</div>
                        <div styleName="value">{yard.fields?.truckId}</div>
                    </div>
                    <div styleName="column">
                        <div styleName="label">Allocated By</div>
                        <div styleName="value">{yard.fields?.allocatedBy}</div>
                    </div>
                </div>
            </div>

            {/* <div styleName="section">
                <JobSummaryDetails jobId={yard.parentEntityId!} />
            </div> */}
        </div>
    );
}

export default YardView;
