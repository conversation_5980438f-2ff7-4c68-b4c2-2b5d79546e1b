@import "../../config/theme/vars.scss";

.loading {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page {
  display: flex;
  flex-direction: column;
}

.row {
  display: flex;
  align-items: center;
}

.lowercase {
  text-transform: lowercase;
}

.container {
  display: flex;

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .summary-bar {
      background-color: #E7E7E7;
      border-radius: 15px;
      margin: 1rem;
      padding: 0.8rem 0;
      display: flex;

      .column {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
      }

      .top {
        font-size: 16px;
        color: $black;
        font-weight: 600;
      }

      .middle {
        font-size: 16px;
        color: $primaryColor;
        font-weight: 600;
      }

      .bottom {
        font-size: 13px;
        color: #4E4949;
        font-weight: 600;
      }

      .menu-icon {
        height: 25px;
        width: 25px;
        padding: 0;
        color: rgba(0, 0, 0, 0.6);
      }
    }

    .main-content {
      margin: 1rem 1rem 0 1rem;

      div.row {
        .tab {
          margin-right: 1px;
          padding: 0.5rem;
          width: 8rem;
          background-color: #EFEFEF;
          font-weight: 500;
          font-size: 1rem;
          border-radius: 15px 15px 0px 0px;
          text-align: center;
          cursor: pointer;
          color: $primaryColor;
          &.active {
            background-color: #E7E7E7;
            cursor: default;
          }
        }
      }

      .tab-body {
        min-height: 36rem;
        background-color: #E7E7E7;
        padding: 1rem;
        display: flex;
        min-width: 200px;

        .tab-grid-1,
        .tab-grid-2,
        .tab-grid-3 {
          flex: 1;
          display: grid;
          grid-gap: 1rem;

          >div {
            align-items: stretch;
            background-color: $white;
            border-radius: 10px;
          }
        }

        .tab-grid-1 {
          grid-template-columns: 1fr 1fr;

          >div {
            padding: 1rem;
          }
        }

        .tab-grid-2 {
          grid-template-columns: 1fr;
        }

        .tab-grid-3 {
          grid-template-columns: 1fr 1fr 1fr;
        }

        // Must be below standard grid-template-columns
        .tab-grid-1,
        .tab-grid-2,
        .tab-grid-3 {
          @media ($ltxs) {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}

.icon {
  color: $primaryColor;
}

.min-width {
  min-width: 200px;
}

