import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { GetAddressCDto, GetListAddressCDto, ListResponseDto, StandardListParameters } from "redi-types";

const route = "Address/";

export default class addressService {

  // Returns address
  static Get(
    Id: string
  ): Promise<HttpResult<ListResponseDto<GetAddressCDto>>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", Id })
            .catch(error => error);
  }

  // Returns addresses for given partyId
  static ListForPartyId(
    partyId: string,
    standardListParameters?: StandardListParameters,
    addressTypeCode?: string
  ): Promise<HttpResult<ListResponseDto<GetListAddressCDto>>> {
    let url = `${config.apiURL + route}ListForPartyId`;

	  return http({ url, method: "GET", standardListParameters, partyId, addressTypeCode })
            .catch(error => error);
  }

  // Returns addresses for given parentId
  static ListForParentEntityId(
    parentEntityId: string,
    parentEntityType: string,
    standardListParameters?: StandardListParameters,
    addressTypeCode?: string
  ): Promise<HttpResult<ListResponseDto<GetListAddressCDto>>> {
    let url = `${config.apiURL + route}ListForParentEntityId`;

	  return http({ url, method: "GET", standardListParameters, parentEntityId, parentEntityType, addressTypeCode })
            .catch(error => error);
  }

  // Returns addresses for given parent type
  static ListForParentEntityType(
    parentEntityType: string,
    standardListParameters?: StandardListParameters,
    addressTypeCode?: string
  ): Promise<HttpResult<ListResponseDto<GetListAddressCDto>>> {
    let url = `${config.apiURL + route}ListForParentEntityType`;

	  return http({ url, method: "GET", standardListParameters, parentEntityType, addressTypeCode })
            .catch(error => error);
  }

  static Create(data: GetAddressCDto): Promise<HttpResult> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Update(data: GetAddressCDto): Promise<HttpResult> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Delete(addressId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", addressId })
			      .catch(error => error);
	}

	static GetDefault() {
		return {
			lines: "",
			country: "Australia",
			countryCode: "AU",
			postalCode: "",
			stateOrProvince: "",
			isPreferredAddress: false,
			parentEntityType: "Party",
			city: ""
		} as GetListAddressCDto;
	}
}
