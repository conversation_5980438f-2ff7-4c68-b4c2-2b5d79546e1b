import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { AddressTypeCDto } from "redi-types";

const route = "AddressType/";

export default class AddressTypeService {

  // Returns list of addresses
  static GetList(query: string = ""): Promise<HttpResult<Array<AddressTypeCDto>>> {
    let url = `${config.apiURL + route}GetList`;

      return http({ url, method: "GET", query })
            .catch(error => error);
  }
}
