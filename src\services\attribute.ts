import { http, HttpResult } from "redi-http";
import { BaseExampleDto, GetAttributeCDto, ListResponseDto, StandardListParameters } from "redi-types";
import config from "../config/config";

import { showToast } from "redi-formik-material";

const route = "Attribute/";

export default class AttributeService {

    static get(code: string): Promise<HttpResult<GetAttributeCDto>> {
        let url = `${config.apiURL + route}Get`;

        return http({ url, method: "GET", code })
            .then(data => data)
            .catch(error => error);
    }
    static create(data: GetAttributeCDto): Promise<HttpResult<GetAttributeCDto>> {
        let url = `${config.apiURL + route}Create`;
        return http({ url, method: "POST", data })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully created");
                } else {
                    showToast("error", "Error creating");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error creating");
                return error;
            });
    }

    static update(data: GetAttributeCDto): Promise<HttpResult<BaseExampleDto>> {
        let url = `${config.apiURL + route}Update`;
        return http({ url, method: "POST", data })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully Updated");
                } else {
                    showToast("error", "Error updating");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error updating");
                return error;
            });
    }
    static delete(code: string): Promise<HttpResult> {
        let url = `${config.apiURL + route}Delete`;

        return http({ url, method: "POST", code })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully deleted");
                } else {
                    showToast("error", "Error deleting");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error deleting");
                return error;
            });
    }
    static enable(code: string): Promise<HttpResult> {
        let url = `${config.apiURL + route}Enable`;

        return http({ url, method: "POST", code })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully enabled");
                } else {
                    showToast("error", "Error enabling");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error enabling");
                return error;
            });
    }

    static disable(code: string): Promise<HttpResult> {
        let url = `${config.apiURL + route}Enable`;

        return http({ url, method: "POST", code })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully disabled");
                } else {
                    showToast("error", "Error disabling");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error disabling");
                return error;
            });
    }

    static listAttribute(standardListParameters: StandardListParameters, attributeGroupCode?: string, allowDisabled?: boolean): Promise<HttpResult<ListResponseDto<GetAttributeCDto>>> {
        let url = `${config.apiURL + route}ListAttribute`;

        return http({ url, method: "GET", standardListParameters, attributeGroupCode, allowDisabled })
            .then(data => data)
            .catch(error => error);
    }
}
