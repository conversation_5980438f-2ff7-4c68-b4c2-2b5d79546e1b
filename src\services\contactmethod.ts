import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { GetContactMethodCDto, GetListContactMethodCDto, ListResponseDto, StandardListParameters } from "redi-types";

const route = "ContactMethod/";

export default class ContactMethodService {

  // Returns contact method
  static Get(
    Id: string
  ): Promise<HttpResult<ListResponseDto<GetContactMethodCDto>>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", Id })
            .catch(error => error);
  }

  // Returns contact methods for given partyId
  static ListForPartyId(
    partyId: string,
    standardListParameters?: StandardListParameters,
    contactMethodTypeCode?: string,
    contactMethodSubTypeCode?: string,
    isPrimaryForMethodType?: boolean
  ): Promise<HttpResult<ListResponseDto<GetListContactMethodCDto>>> {
    let url = `${config.apiURL + route}ListForPartyId`;

      return http({ url, method: "GET", standardListParameters, partyId, contactMethodTypeCode, contactMethodSubTypeCode, isPrimaryForMethodType })
            .catch(error => error);
  }

  // Returns contact methods for given parentId
  static ListForParentEntityId(
    parentEntityId: string,
    parentEntityType: string,
    standardListParameters?: StandardListParameters,
    contactMethodTypeCode?: string,
    contactMethodSubTypeCode?: string,
    isPrimaryForMethodType?: boolean
  ): Promise<HttpResult<ListResponseDto<GetListContactMethodCDto>>> {
    let url = `${config.apiURL + route}ListForParentEntityId`;

      return http({ url, method: "GET", standardListParameters, parentEntityId, parentEntityType, contactMethodTypeCode, contactMethodSubTypeCode, isPrimaryForMethodType })
            .catch(error => error);
  }

  // Returns contact methods for given parent type
  static ListForParentEntityType(
    parentEntityType: string,
    standardListParameters?: StandardListParameters,
    contactMethodTypeCode?: string,
    contactMethodSubTypeCode?: string,
    isPrimaryForMethodType?: boolean
  ): Promise<HttpResult<ListResponseDto<GetListContactMethodCDto>>> {
    let url = `${config.apiURL + route}ListForParentEntityType`;

      return http({ url, method: "GET", standardListParameters, parentEntityType, contactMethodTypeCode, contactMethodSubTypeCode, isPrimaryForMethodType })
            .catch(error => error);
    }

    // Validate a contact method value meets rules defined for the type
    static ValidateContactMethodValue(
        contactMethodTypeCode: string,
        value: string
    ): Promise<HttpResult<boolean>> {
        let url = `${config.apiURL + route}ValidateContactMethodValue`;

        return http({ url, method: "GET", contactMethodTypeCode, value })
            .catch(error => error);
    }

  static Create(data: GetContactMethodCDto): Promise<HttpResult> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Update(data: GetContactMethodCDto): Promise<HttpResult> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Delete(contactMethodId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", contactMethodId })
			      .catch(error => error);
	}
}
