import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { CountryCDto, CountryListCDto } from "redi-types";


const route = "Country/";

export default class countryService {

  static Get(
    countryCode: string
  ): Promise<HttpResult<CountryCDto>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", countryCode })
            .catch(error => error);
  }

  static List(
  ): Promise<HttpResult<Array<CountryListCDto>>> {
    let url = `${config.apiURL + route}List`;

      return http({ url, method: "GET" })
            .catch(error => error);
  }
  
  static Create(
    dto: CountryCDto
  ): Promise<HttpResult<CountryCDto>> {
    let queryUrl = `${config.apiURL + route}Create`;

      return http({ url: queryUrl, method: "POST", dto })
            .catch(error => error);
  }

  static Update(
    dto: CountryCDto
  ): Promise<HttpResult<CountryCDto>> {
    let queryUrl = `${config.apiURL + route}Update`;

      return http({ url: queryUrl, method: "POST", dto })
            .catch(error => error);
  }

}