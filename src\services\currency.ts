import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { CurrencyCDto, CurrencyListCDto } from "redi-types";


const route = "Currency/";

export default class currencyService {

  static Get(
    currencyCode: string
  ): Promise<HttpResult<CurrencyCDto>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", currencyCode })
            .catch(error => error);
  }

  static List(
  ): Promise<HttpResult<Array<CurrencyListCDto>>> {
    let url = `${config.apiURL + route}List`;

      return http({ url, method: "GET" })
            .catch(error => error);
  }

  static Create(
    dto: CurrencyCDto
    ): Promise<HttpResult<CurrencyCDto>> {
      let url = `${config.apiURL + route}Create`;
  
        return http({ url, method: "POST", dto })
              .catch(error => error);
    }

    static Update(
        dto: CurrencyCDto
        ): Promise<HttpResult<CurrencyCDto>> {
          let url = `${config.apiURL + route}Update`;
      
            return http({ url, method: "POST", dto })
                  .catch(error => error);
        }

}