import { http, HttpResult } from "redi-http";
import { BaseExampleDto, ListResponseDto, StandardListParameters } from "redi-types";
import config from "../../config/config";
import { showToast } from "redi-formik-material";

const route = "Example/";

export default class ExampleService {

	static get(exampleId: string): Promise<HttpResult<BaseExampleDto>> {
		let url = `${config.apiURL + route}Get`;

		return http({ url, method: "GET", exampleId })
            .then(data => data)
            .catch(error => error);
	}

	static getList(query: string): Promise<HttpResult<ListResponseDto<BaseExampleDto>>> {
		let url = `${config.apiURL + route}GetList`;

		return http({ url, method: "GET", query })
            .then(data => data)
            .catch(error => error);
	}

	static getListQuery(standardListParameters?: StandardListParameters): Promise<HttpResult<ListResponseDto<BaseExampleDto>>> {
		let url = `${config.apiURL + route}GetListQuery`;

		return http({ url, method: "GET", standardListParameters })
            .then(data => data)
            .catch(error => error);
	}

	static create(data: BaseExampleDto): Promise<HttpResult<BaseExampleDto>> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
        .then(data => {
            if (!data.error && data.data) {
                showToast("success", "Successfully created");
            } else {
                showToast("error", "Error creating");
            }
            return data;
        })
        .catch(error => {
            showToast("error", "Error creating");
            return error;
        });
	}

	
	static Update(data: BaseExampleDto): Promise<HttpResult<BaseExampleDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
        .then(data => {
            if (!data.error && data.data) {
                showToast("success", "Successfully updated");
            } else {
                showToast("error", "Error updating");
            }
            return data;
        })
        .catch(error => {
            showToast("error", "Error updating");
            return error;
        });
	}

	static delete(exampleId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", exampleId })
        .then(data => {
            if (!data.error && data.data) {
                showToast("success", "Successfully deleted");
            } else {
                showToast("error", "Error deleting");
            }
            return data;
        })
        .catch(error => {
            showToast("error", "Error deleting");
            return error;
        });
	}

	static getDefault() {
		return {
			exampleId: "",
      exampleName: ""
		} as BaseExampleDto;
	}
}
