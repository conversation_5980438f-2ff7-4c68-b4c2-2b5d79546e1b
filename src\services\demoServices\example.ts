import { http, HttpResult } from "redi-http";
import { BaseExampleDto} from "redi-types";
import { DataSourceResult, PagingParameters } from "redi-query-builder";
import config from "../../config/config";

const route = "Example/";

export default class ExampleService {

	static Get(exampleId: string): Promise<HttpResult<BaseExampleDto>> {
		let url = `${config.apiURL + route}Get`;

		return http({ url, method: "GET", exampleId })
            .then(data => data)
            .catch(error => error);
	}

	static GetList(query: string): Promise<HttpResult<BaseExampleDto[]>> {
		let url = `${config.apiURL + route}GetList`;

		return http({ url, method: "GET", query })
            .then(data => data)
            .catch(error => error);
	}

	static GetListQuery(query: PagingParameters): Promise<HttpResult<DataSourceResult<BaseExampleDto>>> {
		let url = `${config.apiURL + route}GetListQuery`;

		return http({ url, method: "GET", query })
            .then(data => data)
            .catch(error => error);
	}

	static Create(data: BaseExampleDto): Promise<HttpResult<BaseExampleDto>> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
            .then(data => data)
            .catch(error => error);
	}

	
	static Update(data: BaseExampleDto): Promise<HttpResult<BaseExampleDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
            .then(data => data)
            .catch(error =>  error);
	}

	static Delete(exampleId: string): Promise<HttpResult> {
		let url = `${config.apiURL + route}Delete`;

		return http({ url, method: "POST", exampleId })
            .then(data => data)
            .catch(error => error);
	}

	static GetDefault() {
		return {
			exampleId: "",
      exampleName: ""
		} as BaseExampleDto;
	}
}
