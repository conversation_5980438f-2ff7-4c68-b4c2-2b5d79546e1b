import { http, HttpResult } from "redi-http";
import { DataSourceResult } from "redi-query-builder";
import { GetListingDto, ListResponseDto} from "redi-types";
import config from "../../config/config";

const route = "Listing/";

export default class ListingService {

  static Get(listingId: string): Promise<HttpResult<GetListingDto>> {
		let url = `${config.apiURL + route}Get`;

		return http({ url, method: "GET", listingId })
            .then(data => data)
            .catch(error => error);
	}

  static Create(data: GetListingDto): Promise<HttpResult<GetListingDto>> {
		let url = `${config.apiURL + route}Create`;

		return http({ url, method: "POST", data })
            .then(data => data)
            .catch(error => error);
	}

  static Update(data: GetListingDto): Promise<HttpResult<GetListingDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
            .then(data => data)
            .catch(error => error);
	}
  
	static SearchAsync(subject?: string, sortby?: string, sortDirection?: string, pageSize?: number, pageIndex?: number): Promise<HttpResult<ListResponseDto<GetListingDto>>> {
		let url = `${config.apiURL + route}Search`;

		return http({ url, method: "GET", subject, sortby, sortDirection, pageSize, pageIndex })
            .then(data => data)
            .catch(error => error);
	}

  // Only for Redi-Autocomplete
  static GetListFilter(query?: string): Promise<HttpResult<GetListingDto[]>> {
		let url = `${config.apiURL + route}GetListFilter`;

		return http({ url, method: "GET", query })
            .then(data => data)
            .catch(error => error);
	}
}
