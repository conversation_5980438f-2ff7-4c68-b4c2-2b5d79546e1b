import { http, HttpResult } from "redi-http";
import { ManageListingDisplayContainerCDto } from "redi-types";
import config from "../config/config";

const route = "Editor/";

export default class EditorManager {
  static GetDisplayContainer(
    displayContainerCodes: string,
    returnAttributeSelections: boolean
  ): Promise<HttpResult<ManageListingDisplayContainerCDto[]>> {
    let url = `${config.apiURL + route}GetDisplayContainer`;

    return http({
      url,
      method: "GET",
      displayContainerCodes,
      returnAttributeSelections,
    })
      .then((data) => data)
      .catch((error) => error);
  }
}
