import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { EventCDto, EventListCDto, ListResponseDto, StandardListParameters } from "redi-types";


const route = "Event/";

export default class EventService {

  static Get(
    eventId: number
  ): Promise<HttpResult<EventCDto>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", eventId })
            .catch(error => error);
  }

  static List(
    standardListParameters?: StandardListParameters,
    parentEntityId?: string,
    parentEntityType?: string,
    parentEntityIntId?: number,
    parentEntityId2?: string,
    parentEntityType2?: string,
    parentEntityIntId2?: number,
    description?: string,
    beforeDate?: Date,
    afterDate?: Date
  ): Promise<HttpResult<ListResponseDto<EventListCDto>>> {
    let url = `${config.apiURL + route}List`;

    return http({ 
      url, 
      method: "GET", 
      standardListParameters, 
      parentEntityId,
      parentEntityType,
      parentEntityIntId,
      parentEntityId2,
      parentEntityType2,
      parentEntityIntId2,
      description,
      beforeDate,
      afterDate 
    }).catch(error => error);
  }
  
  static Create(
    dto: EventCDto
    ): Promise<HttpResult<EventCDto>> {
      let url = `${config.apiURL + route}Create`;
  
        return http({ url, method: "GET", dto })
              .catch(error => error);
    }

}
