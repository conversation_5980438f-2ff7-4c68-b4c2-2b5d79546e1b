import { http, HttpResult } from "redi-http";
import { BaseExampleDto, FavouriteCDto, FavouriteSetCDto, GetAttributeCDto, ListResponseDto, StandardListParameters } from "redi-types";
import config from "../config/config";
import { showToast } from "redi-formik-material";

const route = "Favourite/";

export default class FavouriteService {
    /// Get a single favourite
    static get(favouriteId: string): Promise<HttpResult<FavouriteCDto>> {
        let url = `${config.apiURL + route}Get`;

        return http({ url, method: "GET", favouriteId })
            .then(data => data)
            .catch(error => error);
    }
    /// Gets a list of favourites with optional parameters for favouriteSetId, listingId and parentEntityId.
    static list(standardListParameters: StandardListParameters, favouriteSetId?: number, referenceNo?: string, parentEntityId?: number): Promise<HttpResult<ListResponseDto<FavouriteCDto>>> {
        let url = `${config.apiURL + route}List`;

        return http({ url, method: "GET", standardListParameters, favouriteSetId, referenceNo, parentEntityId })
            .then(data => data)
            .catch(error => error);
    }
    /// Get the user's list of favourite tagged entries. Optional parameters for SetId allows grouping favourites into sets for a user.
    static getList(standardListParameters: StandardListParameters, parentEntityId?: number, setId?: number): Promise<HttpResult<ListResponseDto<FavouriteCDto>>> {
        let url = `${config.apiURL + route}GetList`;

        return http({ url, method: "GET", standardListParameters, parentEntityId, setId })
            .then(data => data)
            .catch(error => error);
    }

    ///Add a Listing to the user's favourite listing to the user's favourites. Optional SetId allows grouping favourites into sets for a user.
    //todo may need to put this into the data object if it doesnt work
    static add(referenceNo: string, parentEntityId: number, parentEntityType: string, sortOrder: number, setId?: number): Promise<HttpResult<FavouriteCDto>> {
        let url = `${config.apiURL + route}Add`;

        return http({ url, method: "POST", referenceNo, parentEntityId, parentEntityType, sortOrder, setId })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully added to favourites");
                } else {
                    showToast("error", "Error adding to favourites");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error adding to favourites");
                return error;
            });
    }
    /// Remove a Listing from the user's favourite listing to the user's favourites.
    //todo may need to put this into the data object if it doesnt work
    static remove(referenceNo: string, parentEntityId: number): Promise<HttpResult> {
        let url = `${config.apiURL + route}Remove`;

        return http({ url, method: "POST", referenceNo, parentEntityId })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully removed from favourites");
                } else {
                    showToast("error", "Error removing from favourites");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error removing from favourites");
                return error;
            });
    }
    /// Create a new Favourite set for a user and fix deletion
    static addSet(data: FavouriteCDto): Promise<HttpResult<FavouriteSetCDto>> {
        let url = `${config.apiURL + route}AddSet`;
        return http({ url, method: "POST", data })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully removed from favourites");
                } else {
                    showToast("error", "Error removing from favourites");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error removing from favourites");
                return error;
            });
    }

    /// Delete a set
    static deleteSet(setId: number): Promise<HttpResult> {
        let url = `${config.apiURL + route}DeleteSet`;
        return http({ url, method: "POST", setId })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully removed from favourites");
                } else {
                    showToast("error", "Error removing from favourites");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error removing from favourites");
                return error;
            });
    }

    /// Update the name of a Favourite Set
    static updateSet(data: FavouriteSetCDto): Promise<HttpResult> {
        let url = `${config.apiURL + route}UpdateSet`;
        return http({ url, method: "POST", data })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully updated favourites");
                } else {
                    showToast("error", "Error updating favourites");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error updating favourites");
                return error;
            });
    }

    /// Get a list of Favourite Sets for a user
    static getSetList(parentEntityId: number): Promise<HttpResult<ListResponseDto<FavouriteCDto>>> {
        let url = `${config.apiURL + route}GetSetList`;
        return http({ url, method: "GET", parentEntityId })
            .then(data => data)
            .catch(error => error);
    }

    /// Add a private note to a listing favourited by a user. Only the user can see the note, or someone they have shared a favourite with.
    static addListingNote(favouriteId: number, note: string): Promise<HttpResult> {
        let url = `${config.apiURL + route}AddListingNote`;
        return http({ url, method: "POST", favouriteId, note })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully added note");
                } else {
                    showToast("error", "Error adding note");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error adding note");
                return error;
            });
    }

    /// Update a note on a listing favourited by a user.
    static updateListingNote(favouriteId: number, note: string): Promise<HttpResult> {
        let url = `${config.apiURL + route}UpdateListingNote`;
        return http({ url, method: "POST", favouriteId, note })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully updated note");
                } else {
                    showToast("error", "Error updating note");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error updating note");
                return error;
            });
    }

    /// Delete a note on a listing favourited by a user.
    static deleteListingNote(favouriteId: number): Promise<HttpResult> {
        let url = `${config.apiURL + route}DeleteListingNote`;
        return http({ url, method: "POST", favouriteId })
            .then(data => {
                if (!data.error && data.data) {
                    showToast("success", "Successfully deleted note");
                } else {
                    showToast("error", "Error deleting note");
                }
                return data;
            })
            .catch(error => {
                showToast("error", "Error deleting note");
                return error;
            });
    }
}
