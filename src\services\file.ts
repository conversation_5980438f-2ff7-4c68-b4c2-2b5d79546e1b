import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { FileCDto, FileListCDto, ListResponseDto, StandardListParameters } from "redi-types";


const route = "File/";

export default class fileService {

  static Get(
    fileId: string
  ): Promise<HttpResult<FileCDto>> {
    let url = `${config.apiURL + route}Get`;

    return http({ url, method: "GET", fileId })
      .catch(error => error);
  }
  static GetByParentEntityId(
    ParentEntityId: string
  ): Promise<HttpResult<FileCDto>> {
    let url = `${config.apiURL + route}GetByParentId`;

    return http({ url, method: "GET", ParentEntityId })
      .catch(error => error);
  }

  static GetList(
    query?: string
  ): Promise<HttpResult<Array<FileListCDto>>> {
    let url = `${config.apiURL + route}GetList`;

    return http({ url, method: "GET", query })
      .catch(error => error);
  }

  static GetListQuery(
    linkId: string,
    standardListParameters?: StandardListParameters
  ): Promise<HttpResult<ListResponseDto<FileListCDto>>> {
    let url = `${config.apiURL + route}GetListQuery`;

    return http({ url, method: "GET", linkId, standardListParameters })
      .catch(error => error);
  }

  static Create(
    file: FileCDto
  ): Promise<HttpResult<FileCDto>> {
    let url = `${config.apiURL + route}Create`;

    return http({ url, method: "POST", file })
      .catch(error => error);
  }

  static Update(
    file: FileCDto
  ): Promise<HttpResult<FileCDto>> {
    let url = `${config.apiURL + route}Update`;

    return http({ url, method: "POST", file })
      .catch(error => error);
  }

  static Delete(
    fileId: string,
    deleteAllVersions?: boolean,
  ): Promise<HttpResult<FileCDto>> {
    let url = `${config.apiURL + route}Delete`;

    return http({ url, method: "POST", fileId, deleteAllVersions })
      .catch(error => error);
  }

}