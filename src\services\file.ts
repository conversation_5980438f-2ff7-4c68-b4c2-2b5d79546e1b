import { http, HttpResult } from "redi-http";
import {
  BaseExampleDto,
  GetAttributeCDto,
  ListResponseDto,
  ManageListingAttributeDto,
  ManageListingCDto,
  ManageListingMediaCDto,
  ManageListingWithDataAndMediaCDto,
  ManageListingWithDisplayGroupedDataAndMediaDto,
  StandardListParameters,
} from "redi-types";
import config from "../config/config";
import { showToast } from "redi-formik-material";

const route = "FileManager/";

export default class FileManager {
  static UploadFile(
    file: File,
    cloudFilename: string,
    fileCategoryCode: string,
    isPublic: boolean,
    listingId?: number,
    sortOrder?: number
  ): Promise<HttpResult> {
    let queryUrl = `${config.apiURL + route}UploadFile`;
    // Need to use formData to generate the correct headers that can't be manually set ( I think)
    // sending through just the file does not work.
    // FormData seems to have some special sauce that allows the backend to interpret it properly.
    let formData = new FormData();
    formData.append("file", file, cloudFilename);

    return http({
      url: queryUrl,
      method: "POST",
      data: formData,
      headers: {
        "Content-Disposition": 'attachment; filename="' + cloudFilename + '"',
        // Accept: "*/*",
        // "Data-Type": "image/png",
      },
      cloudFilename,
      fileCategoryCode,
      isPublic,
      listingId,
      sortOrder,
    })
      .catch((error) => error)
      .then((data) => {
        //console.log(data.error);
        if (!data.error) {
          showToast("success", "Successfully added");
        } else {
          showToast("error", "Error adding");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error adding");
        return error;
      });
  }
}
