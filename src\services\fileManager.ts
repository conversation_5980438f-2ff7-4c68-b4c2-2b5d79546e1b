import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { FileCDto } from "redi-types";


const route = "FileManager/";

export default class fileManagerService {

  // Returns address
  static GetAuthenticatedUrl(
    baseUrl: string
  ): Promise<HttpResult<string>> {
    let url = `${config.apiURL + route}GetAuthenticatedUrl`;

    return http({ url, method: "GET", baseUrl })
      .catch(error => error);
  }
  static GetAuthenticatedUrlByParentId(
    parentEntityId: string
  ): Promise<HttpResult<string>> {
    let url = `${config.apiURL + route}GetAuthenticatedUrlByParentId`;

    return http({ url, method: "GET", parentEntityId })
      .catch(error => error);
  }

  static Delete(
    fileId: string,
    deleteAllVersions?: boolean,
    deleteThumbnail?: boolean,
  ): Promise<HttpResult> {
    let queryUrl = `${config.apiURL + route}Delete`;

    return http({ url: queryUrl, method: "POST", fileId, deleteAllVersions, deleteThumbnail })
      .catch(error => error);
  }

  static UploadFile(
    file: File,
    cloudFilename: string,
    fileCategoryCode: string,
    parentEntityId: string,
    parentEntityType: string,
    isPublic: boolean,
    createThumbnail?: boolean,
    sortOrder?: number,
  ): Promise<HttpResult<FileCDto>> {
    let queryUrl = `${config.apiURL + route}UploadFile`;
    // Need to use formData to generate the correct headers that can't be manually set ( I think)
    // sending through just the file does not work.
    // FormData seems to have some special sauce that allows the backend to interpret it properly.
    let formData = new FormData();
    formData.append("file", file, cloudFilename);

    return http({ url: queryUrl, method: "POST", data: formData, headers: { "Content-Disposition": 'attachment; filename="' + cloudFilename + '"', "Accept": "*/*", "Data-Type": "image/png", }, cloudFilename, fileCategoryCode, parentEntityId, parentEntityType, isPublic, createThumbnail, sortOrder })
      .catch(error => error);
  }

}