import { http, HttpPromise, HttpResult } from "redi-http";
import { FilterExcludeAttributesDto, ListingDisplayContainerCDto, ListingFiltersResponseCDto, ListingSortOptionCDto, ListingWithDataAndMediaCDto, ListResponseDto, StandardListParameters } from "redi-types";
import config from "../config/config";

const route = "Listing/";
export interface GetListQuery {
    filterArray: FilterExcludeAttributesDto[],
    excludeArray: FilterExcludeAttributesDto[],
    displayContainerCodes?: string;
    displayGroupCode: string;
    includeTagIdArray: number[];
    excludeTagIdArray: number[];
    parentEntityIntId?: number | undefined;
    parentEntityId?: string | undefined;
    parentEntityType?: string | undefined;
    parentListingId?: number;
    statusCode?: string | undefined;
    visibility?: number | undefined;
    subject?: string | undefined;
    description?: string | undefined;
    beforeDate?: Date | undefined;
    afterDate?: Date | undefined;
    standardListParameters?: StandardListParameters;
    includeMedia?: boolean;
    mediaCategoryCodes?: string | undefined;
    includeAttributes?: boolean;
    fromLocationLat?: number | undefined;
    fromLocationLong?: number | undefined;
    searchDistanceKm?: number | undefined;
}

export default class ListingService {

    static get(
        referenceNo: string,
        includeMedia: boolean,
        includeAttributes: boolean,
        displayContainerCodes?: string,
        excludeAttributesWithNoData?: boolean
      ): HttpPromise<ListingWithDataAndMediaCDto> {
        const url = `${config.apiURL + route}Get`;
        const promise = http<ListingWithDataAndMediaCDto>({ url, method: "GET", referenceNo, includeMedia, includeAttributes, displayContainerCodes, excludeAttributesWithNoData });
        promise.catch(error => error);
        return promise;
      }

    static getData(referenceNo: string, displayContainerCodes: string, excludeAttributesWithNoData: boolean): Promise<HttpResult<ListingDisplayContainerCDto>> {
        let url = `${config.apiURL + route}Get`;

        return http({ url, method: "GET", referenceNo, displayContainerCodes, excludeAttributesWithNoData })
            .then(data => data)
            .catch(error => error);
    }
    /// Get a List of Active Listings



    static getList(
        filtersObject?: FilterExcludeAttributesDto,
        excludesObject?: FilterExcludeAttributesDto,
        displayContainerCodes?: string,
        parentEntityIntId?: number | undefined,
        parentEntityId?: string | undefined,
        parentEntityType?: string | undefined,
        parentListingId?: number,
        statusCode?: string | undefined,
        visibility?: number | undefined,
        listingTypeId?: number | undefined,
        subject?: string | undefined,
        description?: string | undefined,
        beforeDate?: Date | undefined,
        afterDate?: Date | undefined,
        standardListParameters?: StandardListParameters,
        includeMedia?: boolean,
        mediaCategoryCodes?: string | undefined,
        includeAttributes?: boolean,
        fromLocationLat?: number | undefined,
        fromLocationLong?: number | undefined,
        searchDistanceKm?: number | undefined,
        flattenAttributesAndReturnAsFields?: boolean,
        relationshipTypeIds?: string | undefined,
        orFiltersObject?: FilterExcludeAttributesDto,
        orFilterListingPartyRelationshipObject?: FilterExcludeAttributesDto
    ): Promise<HttpResult<ListResponseDto<ListingWithDataAndMediaCDto>>> {
        //loop through the filter and exclude key values and add them to the query string
        let filter: any = []; // Initialize an empty filter object
        let exclude: any = []; // Initialize an empty exclude object
        let orFilter: any = []; // Initialize an empty orFilter object
        let orFilterListingPartyRelationship: any = []; // Initialize an empty orFilterListingPartyRelationship object
        
        if (filtersObject) {
            filter = ListingService.buildParams(filtersObject, 'filter');
        }
        // Loop through the excludesObject if it exists
        if (excludesObject) {
            exclude = ListingService.buildParams(excludesObject, 'exclude');
        }
        if (orFiltersObject) {
            orFilter = ListingService.buildParams(orFiltersObject, 'orFilter');
        }
        if (orFilterListingPartyRelationshipObject) {
            orFilterListingPartyRelationship = ListingService.buildParams(orFilterListingPartyRelationshipObject, 'orFilterListingPartyRelationship');
        }
        
        let url = `${config.apiURL + route}GetList`;


        return http({ url, method: "GET", filter, exclude, orFilter, orFilterListingPartyRelationship, displayContainerCodes, parentEntityIntId, parentEntityId, parentEntityType, parentListingId, statusCode, visibility, listingTypeId, subject, description, beforeDate, afterDate, standardListParameters, includeMedia, mediaCategoryCodes, includeAttributes, fromLocationLat, fromLocationLong, searchDistanceKm, flattenAttributesAndReturnAsFields, relationshipTypeIds })
            .then(data => data)
            .catch(error => error);
    }
    
    static getListQuery(
        filtersObject?: FilterExcludeAttributesDto,
        excludesObject?: FilterExcludeAttributesDto,
        displayContainerCodes?: string,
        parentEntityIntId?: number | undefined,
        parentEntityId?: string | undefined,
        parentEntityType?: string | undefined,
        parentListingId?: number,
        statusCode?: string | undefined,
        visibility?: number | undefined,
        listingTypeId?: number | undefined,
        subject?: string | undefined,
        description?: string | undefined,
        beforeDate?: Date | undefined,
        afterDate?: Date | undefined,
        standardListParameters?: StandardListParameters,
        includeMedia?: boolean,
        mediaCategoryCodes?: string | undefined,
        includeAttributes?: boolean,
        fromLocationLat?: number | undefined,
        fromLocationLong?: number | undefined,
        searchDistanceKm?: number | undefined,
    ): HttpPromise<ListResponseDto<ListingWithDataAndMediaCDto>> {
        //loop through the filter and exclude key values and add them to the query string
        let filter: any = []; // Initialize an empty filter object
        let exclude: any = []; // Initialize an empty exclude object

        if (filtersObject) {
            filter = ListingService.buildParams(filtersObject, 'filter');
        }

        // Loop through the excludesObject if it exists
        if (excludesObject) {
            exclude = ListingService.buildParams(excludesObject, 'exclude');
        }
        
        let url = `${config.apiURL + route}GetList`;
        const promise = http<ListResponseDto<ListingWithDataAndMediaCDto>>({ url, method: "GET", filter, exclude, displayContainerCodes, parentEntityIntId, parentEntityId, parentEntityType, parentListingId, statusCode, visibility, listingTypeId, subject, description, beforeDate, afterDate, standardListParameters, includeMedia, mediaCategoryCodes, includeAttributes, fromLocationLat, fromLocationLong, searchDistanceKm })
        promise.catch(error => error);
        return promise;
    }

    /// Get the filters to display to the user.
    static getFilters(
        displayGroupCode?: string,
        filtersObject?: FilterExcludeAttributesDto,
        excludesObject?: FilterExcludeAttributesDto,
        displayContainerCodes?: string,
        includeTagIdArray?: number[],
        excludeTagIdArray?: number[],
        parentEntityIntId?: number | undefined,
        parentEntityId?: string | undefined,
        parentEntityType?: string,
        parentListingId?: number,
        listingTypeId?: number,
        statusCode?: string,
        visibility?: number,
        returnCountsPerAttribute?: boolean,
        beforeDate?: Date,
        afterDate?: Date,
        fromLocationLat?: number,
        fromLocationLong?: number,
        searchDistanceKm?: number
    ): Promise<HttpResult<ListingFiltersResponseCDto>> {
        //loop through the filter and exclude key values and add them to the query string
        let filter: any = []; // Initialize an empty filter object
        let exclude: any = []; // Initialize an empty exclude object

        if (filtersObject) {
            filter = ListingService.buildParams(filtersObject, 'filter');
        }

        // Loop through the excludesObject if it exists
        if (excludesObject) {
            exclude = ListingService.buildParams(excludesObject, 'exclude');
        }

        let url = `${config.apiURL + route}GetFilters`;

        return http({ url, method: "GET", displayGroupCode, ...filter, ...exclude, displayContainerCodes, includeTagIdArray, excludeTagIdArray, parentEntityIntId, parentEntityId, parentEntityType, parentListingId, listingTypeId, statusCode, visibility, returnCountsPerAttribute, beforeDate, afterDate, fromLocationLat, fromLocationLong, searchDistanceKm })
            .then(data => data)
            .catch(error => error);
    }


    static getSortOptions(
        displayContainerCodes?: string
    ): Promise<HttpResult<ListingSortOptionCDto[]>> {
        let url = `${config.apiURL + route}GetSortOptions`;

        return http({ url, method: "GET", displayContainerCodes })
            .then(data => data)
            .catch(error => error);
    }


    // A dictionary of Attribute based filters.Support unlimited number of filters
    /// Specify as
    /// &amp;filter.attrCode|expression=theValue
    /// The expression if not provided defaults to equals.
    /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset (in a set of values), startswith, endswith. contains
    /// range requires 2 values on theValue side split with |
    /// inset allows 1 to many values on the theValue side split with |</param>
    static buildParams(filtersObject: FilterExcludeAttributesDto, filterOrExclude: string) {
        let filter: any = []; // Initialize an empty filter object

        for (const [key, values] of Object.entries(filtersObject)) {
            // For each entry in the filtersObject, push the filter object to the filter array
            let valueString = ''
            values.values.forEach((value, index) => {
                valueString += value;
                if (index < values.values.length - 1) {
                    valueString += '|';
                }
            });
            //If we have an operator use it
            if (values.operator) {
                filter.push({ [`${filterOrExclude}.${key}|${values.operator}`]: valueString });
            }
            //If we have multiple values use the inset operator
            else if (values.values.length > 1) {
                filter.push({ [`${filterOrExclude}.${key}|inset`]: valueString });
            }
            //If we have a single value use the eq operator
            else {
                filter.push({ [`${filterOrExclude}.${key}|eq`]: valueString });
            }

        }
        return filter;
    }
}
