import { http, HttpPromise, HttpResult } from "redi-http";
import config from "../config/config";
import { ListResponseDto, LocationCDto, StandardListParameters } from "redi-types";


const route = "Location/";

export default class LocationService {

  static get(
    locationId: string
  ): HttpPromise<LocationCDto> {
    const url = `${config.apiURL + route}Get`;
    const promise = http<LocationCDto>({ url, method: "GET", locationId });
    promise.catch(error => error);
    return promise;
  }

  static getList(
    standardListParameters: StandardListParameters,
    search?: string,
    type?: string,
  ): HttpPromise<ListResponseDto<LocationCDto>> {
    const url = `${config.apiURL + route}GetList`;
    const promise = http<ListResponseDto<LocationCDto>>({ url, method: "GET", search, type, standardListParameters });
    promise.catch(error => error);
    return promise;
  }

  static Create(
    dto: LocationCDto
  ): Promise<HttpResult<LocationCDto>> {
    let queryUrl = `${config.apiURL + route}Create`;

    return http({ url: queryUrl, method: "POST", dto })
      .catch(error => error);
  }

  static Update(
    dto: LocationCDto
  ): Promise<HttpResult<LocationCDto>> {
    let queryUrl = `${config.apiURL + route}Update`;

    return http({ url: queryUrl, method: "POST", dto })
      .catch(error => error);
  }

}