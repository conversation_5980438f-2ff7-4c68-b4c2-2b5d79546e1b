import { http, HttpResult } from "redi-http";
import {
  BaseExampleDto,
  GetAttributeCDto,
  ListingDisplayContainerCDto,
  ListingMediaCDto,
  ListResponseDto,
  ManageListingAttributeDto,
  ManageListingCDto,
  ManageListingDisplayContainerCDto,
  ManageListingMediaCDto,
  ManageListingWithDataAndMediaCDto,
  ManageListingWithDisplayGroupedDataAndMediaDto,
  StandardListParameters,
} from "redi-types";
import config from "../config/config";
import { showToast } from "redi-formik-material";
import EditorManager from "./editor";
import { useEffect, useState } from "react";

const route = "Manage/";

export default class ManageService {
  static get(
    listingId: number,
    includeMedia?: boolean,
    includeAttributes?: boolean,
    displayContainerCodes?: string,
    excludeAttributesWithNoData?: boolean,
    displayGroupCode?: string,
    flattenToFields?: boolean
  ): Promise<HttpResult<ManageListingWithDisplayGroupedDataAndMediaDto>> {
    let url = `${config.apiURL + route}GetListing`;

    return http({
      url,
      method: "GET",
      listingId,
      includeMedia,
      includeAttributes,
      displayContainerCodes,
      excludeAttributesWithNoData,
      displayGroupCode,
      flattenToFields
    })
      .then((data) => data)
      .catch((error) => error);
  }
  static getListingByRef(
    referenceNo: string,
    includeMedia: boolean,
    includeAttributes: boolean,
    flattenToFields?: boolean
  ): Promise<HttpResult<ManageListingWithDisplayGroupedDataAndMediaDto>> {
    let url = `${config.apiURL + route}GetListingByRef`;

    return http({
      url,
      method: "GET",
      referenceNo,
      includeMedia,
      includeAttributes,
      flattenToFields
    })
      .then((data) => data)
      .catch((error) => error);
  }
  static getListingData(
    listingId: number
  ): Promise<HttpResult<ManageListingAttributeDto[]>> {
    let url = `${config.apiURL + route}GetListingData`;

    return http({ url, method: "GET", listingId })
      .then((data) => data)
      .catch((error) => error);
  }
  static getListingDataByRef(
    referenceNo: string
  ): Promise<HttpResult<ManageListingWithDataAndMediaCDto[]>> {
    let url = `${config.apiURL + route}GetListingData`;

    return http({ url, method: "GET", referenceNo })
      .then((data) => data)
      .catch((error) => error);
  }
  static createListing(
    data: ManageListingWithDataAndMediaCDto,
    supportOfflineReferenceCreate: boolean
  ): Promise<HttpResult<ManageListingCDto>> {
    let url = `${config.apiURL + route}CreateListing`;
    return http({ url, method: "POST", data, supportOfflineReferenceCreate })
      .then((data) => {
        if (!data.error && data.data) {
          showToast("success", "Successfully created");
        } else {
          showToast("error", "Error creating");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error creating");
        return error;
      });
  }

  static createListingWDP(
    data: ManageListingWithDisplayGroupedDataAndMediaDto,
    supportOfflineReferenceCreate: boolean
  ): Promise<HttpResult<ManageListingCDto>> {
    let url = `${config.apiURL + route}CreateListingWithDisplayContainers`;
    return http({ url, method: "POST", data, supportOfflineReferenceCreate })
      .then((data) => {
        if (!data.error && data.data) {
          showToast("success", "Successfully created");
        } else {
          showToast("error", "Error creating");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error creating");
        return error;
      });
  }

  static updateListing(
    data: ManageListingWithDataAndMediaCDto
  ): Promise<HttpResult<ManageListingCDto>> {
    let url = `${config.apiURL + route}UpdateListing`;
    return http({ url, method: "POST", data })
      .then((data) => {
        if (!data.error) {
          showToast("success", "Successfully updated");
        } else {
          showToast("error", "Error updating");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error updating");
        return error;
      });
  }

  // with displaycontainers listing update
  static updateListingWDP(
    data: ManageListingWithDisplayGroupedDataAndMediaDto
  ): Promise<HttpResult<ManageListingCDto>> {
    let url = `${config.apiURL + route}UpdateListingWithDisplayContainers`;
    return http({ url, method: "POST", data })
      .then((data) => {
        if (!data.error) {
          showToast("success", "Successfully updated");
        } else {
          showToast("error", "Error updating");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error updating");
        return error;
      });
  }

  static updateListingStatus(
    listingId: number,
    newStatusCode: string
  ): Promise<HttpResult> {
    let url = `${config.apiURL + route}UpdateListingStatus`;
    return http({ url, method: "POST", listingId, newStatusCode })
      .then((data) => {
        if (!data.error) {
          showToast("success", "Successfully updated");
        } else {
          showToast("error", "Error updating");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error updating");
        return error;
      });
  }
  static addMedia(
    listingId: number,
    data: ManageListingMediaCDto[]
  ): Promise<HttpResult> {
    let url = `${config.apiURL + route}AddMedia`;
    return http({ url, method: "POST", listingId, data })
      .then((data) => {
        console.log(data.error);
        if (!data.error) {
          showToast("success", "Successfully added");
        } else {
          showToast("error", "Error adding");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error adding");
        return error;
      });
  }
  static deleteMedia(
    listingId: number,
    mediaIdList: number[]
  ): Promise<HttpResult> {
    let data = mediaIdList;
    let url = `${config.apiURL + route}DeleteMedia`;
    return http({ url, method: "POST", listingId, data })
      .then((data) => {
        if (!data.error) {
          showToast("success", "Successfully deleted");
        } else {
          showToast("error", "Error deleting");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error deleting");
        return error;
      });
  }

  static updateMedia(
    listingId: number,
    data: ListingMediaCDto[]
  ): Promise<HttpResult> {
    let url = `${config.apiURL + route}UpdateMedia`;
    return http({ url, method: "POST", listingId, data })
      .then((data) => {
        console.log(data.error);
        if (!data.error) {
          showToast("success", "Successfully updated");
        } else {
          showToast("error", "Error updating");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error updating");
        return error;
      });
  }

  static deleteListing(listingId: number): Promise<HttpResult> {
    let url = `${config.apiURL + route}DeleteListing`;
    return http({ url, method: "POST", listingId })
      .then((data) => {
        if (!data.error) {
          showToast("success", "Successfully deleted");
        } else {
          showToast("error", "Error deleting");
        }
        return data;
      })
      .catch((error) => {
        showToast("error", "Error deleting");
        return error;
      });
  }
  static getList(
    standardListParameters: StandardListParameters,
    parentEntityId: string,
    returnAttributeCodes: string,
    includeMedia: boolean,
    mediaCategoryCode: string,
    statusCode?: string
  ): Promise<HttpResult<ListResponseDto<ManageListingWithDataAndMediaCDto>>> {
    let url = `${config.apiURL + route}GetList`;

    return http({
      url,
      method: "GET",
      standardListParameters,
      parentEntityId,
      returnAttributeCodes,
      includeMedia,
      mediaCategoryCode,
      statusCode,
    })
      .then((data) => data)
      .catch((error) => error);
  }

  static getInitialValues() {
    return {
      statusCode: "Active",
      media: [],
    } as unknown as ManageListingWithDisplayGroupedDataAndMediaDto;
  }
}
