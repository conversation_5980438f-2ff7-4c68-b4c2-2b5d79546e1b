import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { OrganisationTypeCDto } from "redi-types";

const route = "OrganisationType/";

export default class OrganisationTypeService {

  // Returns list of parties
  static GetList(query: string = ""): Promise<HttpResult<Array<OrganisationTypeCDto>>> {
    let url = `${config.apiURL + route}GetList`;

      return http({ url, method: "GET", query })
            .catch(error => error);
  }
}
