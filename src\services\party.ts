import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { GetPartyCDto, GetListPartyCDto, ListResponseDto, StandardListParameters, PartyPersonListCDto, PartyOrganisationListCDto, GetExtendedPartyCDto, GetExtendedListPartyCDto, InsurancePartyCDto } from "redi-types";
import { NIL as NIL_UUID, v4 as uuidv4 } from 'uuid';
import { PartyTypeEnum } from "../enum/PartyTypeEnum";
import { default_attribute_list } from "../pages/Contractor/default_values/default_values";
import { RoleTypeEnum } from "../enum/RoleTypeCodeEnum";

const route = "Party/";

type FilterAttributesDto = { [key: string]: { values: string[], operator?: string } };

export default class PartyService {

  // Returns party
  static Get(
    partyId: string,
    getPartyAttributes?: boolean,
    getDeductions?: boolean,
  ): Promise<HttpResult<GetExtendedPartyCDto>> {
    let url = `${config.apiURL + route}Get`;

    return http({ url, method: "GET", partyId, getPartyAttributes, getDeductions })
      .catch(error => error);
  }
  static getDefaultValues(partyType: PartyTypeEnum, roleType: string, options?: Partial<GetExtendedPartyCDto>, includeAddress: boolean = true) {
    var dto = {
      partyId: NIL_UUID,
      name: '',
      statusCode: 'Active',
      partyType: partyType,
      userId: '',
      roleTypeCode: roleType,
      ...options
    }
    if (roleType === RoleTypeEnum.Contractor) {
      dto.partyAttributes = default_attribute_list,
        dto.deductions = [
          {
            deductionId: NIL_UUID,
            parentEntityId: NIL_UUID,
            type: "Radio Fee",
            amount: 0,
          },
        ]
      dto.contactMethods = [
        {
          contactMethodId: NIL_UUID,
          contactMethodTypeCode: 'Phone',
          value: '',
          isPreferredContactMethod: true,
          isPrimaryForMethodType: true,
          parentEntityId: NIL_UUID,
          parentEntityType: partyType,
          sortOrder: 0,
          contactMethodTypeLabel: "",
        },
        {
          contactMethodId: NIL_UUID,
          contactMethodTypeCode: 'Email',
          value: '',
          isPreferredContactMethod: false,
          isPrimaryForMethodType: true,
          parentEntityId: NIL_UUID,
          parentEntityType: partyType,
          sortOrder: 1,
          contactMethodTypeLabel: "",
        }
      ]
      dto.addresses = includeAddress ?
        [
          {
            addressId: NIL_UUID,
            isPreferredAddress: true,
            parentEntityId: NIL_UUID,
            parentEntityType: partyType,
            sortOrder: 0,
            lines: "",
            city: "",
            stateOrProvince: "",
            postalCode: "",
            countryCode: "AU",
            addressTypeCode: "Billing",
            fullAddress: ""
          }
        ] : []
    }

    if (partyType === PartyTypeEnum.Organisation) {
      dto.organisation = {
        organisationId: NIL_UUID,
        name: "",
        organisationReference: "",
        description: "",
        organisationTypeCode: "Company",
        organisationTypeDescription: "",
        ...options?.organisation
      }
    }

    return dto as GetPartyCDto;
  }

  // Returns party with attributes
  static GetWithPartyAttributes(
    partyId: string,
  ): Promise<HttpResult<GetExtendedPartyCDto>> {
    let url = `${config.apiURL + route}GetWithPartyAttributes`;

    return http({ url, method: "GET", partyId })
      .catch(error => error);
  }

  // Returns list of parties
  static List(
    standardListParameters?: StandardListParameters,
    organisationTypeCode?: string,
    statusCode?: string,
    query?: string,
    hasRoleCode?: string,
    returnContactMethods?: boolean,
    returnAddress?: boolean,
    getPartyAttributes?: boolean,
    relationshipTypeIds?: string | undefined,
    listingRelationshipTypeReturnField?: string,
    orFilterListingPartyRelationshipObject?: FilterAttributesDto
  ): Promise<HttpResult<ListResponseDto<GetExtendedListPartyCDto>>> {
    let orFilterListingPartyRelationship: any = []; // Initialize an empty orFilterListingPartyRelationship object
    if (orFilterListingPartyRelationshipObject) {
        orFilterListingPartyRelationship = PartyService.buildParams(orFilterListingPartyRelationshipObject, 'orFilterListingPartyRelationship');
    }

    let url = `${config.apiURL + route}List`;

    return http({ url, method: "GET", standardListParameters, orFilterListingPartyRelationship, organisationTypeCode, statusCode, query, hasRoleCode, returnContactMethods, returnAddress, getPartyAttributes, relationshipTypeIds, listingRelationshipTypeReturnField  })
      .catch(error => error);
  }

  // Returns list of people
  static ListPersons(
    standardListParameters?: StandardListParameters,
    query?: string,
    hasRoleCode?: string,
    statusCode?: string,
    returnContactMethods?: boolean,
    returnAddress?: boolean
  ): Promise<HttpResult<ListResponseDto<PartyPersonListCDto>>> {
    let url = `${config.apiURL + route}ListPersons`;

    return http({ url, method: "GET", standardListParameters, query, hasRoleCode, statusCode, returnContactMethods, returnAddress })
      .catch(error => error);
  }

  // Returns list of organisations
  static ListOrganisations(
    standardListParameters?: StandardListParameters,
    query?: string,
    hasRoleCode?: string,
    organisationTypeCode?: string,
    statusCode?: string,
    returnContactMethods?: boolean,
    returnAddress?: boolean
  ): Promise<HttpResult<ListResponseDto<PartyOrganisationListCDto>>> {
    let url = `${config.apiURL + route}ListOrganisations`;

    return http({ url, method: "GET", standardListParameters, query, hasRoleCode, organisationTypeCode, statusCode, returnContactMethods, returnAddress })
      .catch(error => error);
  }

  static Create(data: GetExtendedPartyCDto, roleTypeCode?: string, relatedPartyId?: string, relatedPartyRoleTypeCode?: string): Promise<HttpResult<GetExtendedPartyCDto>> {
    let url = `${config.apiURL + route}Create`;

    return http({ url, method: "POST", data, roleTypeCode, relatedPartyId, relatedPartyRoleTypeCode })
      .catch(error => error);
  }

  static CreateInsurance(data: InsurancePartyCDto, roleTypeCode?: string, relatedPartyId?: string, relatedPartyRoleTypeCode?: string): Promise<HttpResult<InsurancePartyCDto>> {
    let url = `${config.apiURL + route}Create`;

    return http({ url, method: "POST", data, roleTypeCode, relatedPartyId, relatedPartyRoleTypeCode })
      .catch(error => error);
  }

  static Update(data: GetExtendedPartyCDto, relatedPartyId?: string): Promise<HttpResult<GetExtendedPartyCDto>> {
    let url = `${config.apiURL + route}Update`;

    return http({ url, method: "POST", data, relatedPartyId })
      .catch(error => error);
  }

  static UpdateInsurance(data: InsurancePartyCDto, relatedPartyId?: string): Promise<HttpResult<InsurancePartyCDto>> {
    let url = `${config.apiURL + route}Update`;

    return http({ url, method: "POST", data, relatedPartyId })
      .catch(error => error);
  }

  //Add or remove a user from party
  static UpdateUserId(partyId: string, userId?: string): Promise<HttpResult<GetPartyCDto>> {
    let url = `${config.apiURL + route}UpdateUserId`;

    return http({ url, method: "POST", partyId, userId })
      .catch(error => error);
  }

  static UpdateStatus(partyId: string, statusCode: string): Promise<HttpResult<GetPartyCDto>> {
    let url = `${config.apiURL + route}UpdateStatus`;

    return http({ url, method: "POST", partyId, statusCode })
      .catch(error => error);
  }

  static Delete(partyId: string): Promise<HttpResult> {
    let url = `${config.apiURL + route}Delete`;

    return http({ url, method: "POST", partyId })
      .catch(error => error);
  }

  static ListByRelationship(
    relatedPartyRoleTypeCode: string,
    relationshipTypeCode: string,
    standardListParameters?: StandardListParameters,
    query?: string,
    partyId?: string,
    includeActiveOnly?: boolean,
    returnContactMethods?: boolean,
    returnAddress?: boolean,
    statusCode?: string,
    organisationTypeCode?: string
  ): Promise<HttpResult<ListResponseDto<GetListPartyCDto>>> {
    let url = `${config.apiURL + route}ListByRelationship`;

    return http({ url, method: "GET", standardListParameters, relatedPartyRoleTypeCode, relationshipTypeCode, partyId, includeActiveOnly, query, returnContactMethods, returnAddress, statusCode, organisationTypeCode })
      .catch(error => {
        console.log("ERROR", error);
        return error;
      });
  }

    // A dictionary of Attribute based filters.Support unlimited number of filters
    /// Specify as
    /// &amp;filter.attrCode|expression=theValue
    /// The expression if not provided defaults to equals.
    /// Available expressions types are: eq, ne, gt, lt, lte, gte, range, inset (in a set of values), startswith, endswith. contains
    /// range requires 2 values on theValue side split with |
    /// inset allows 1 to many values on the theValue side split with |</param>
    static buildParams(filtersObject: FilterAttributesDto, filterOrExclude: string) {
        let filter: any = []; // Initialize an empty filter object

        for (const [key, values] of Object.entries(filtersObject)) {
            // For each entry in the filtersObject, push the filter object to the filter array
            let valueString = ''
            values.values.forEach((value, index) => {
                valueString += value;
                if (index < values.values.length - 1) {
                    valueString += '|';
                }
            });
            //If we have an operator use it
            if (values.operator) {
                filter.push({ [`${filterOrExclude}.${key}|${values.operator}`]: valueString });
            }
            //If we have multiple values use the inset operator
            else if (values.values.length > 1) {
                filter.push({ [`${filterOrExclude}.${key}|inset`]: valueString });
            }
            //If we have a single value use the eq operator
            else {
                filter.push({ [`${filterOrExclude}.${key}|eq`]: valueString });
            }

        }
        return filter;
    }
}
