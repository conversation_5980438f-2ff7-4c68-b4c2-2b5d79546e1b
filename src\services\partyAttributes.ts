import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { GetListPartyCDto, ListResponseDto, StandardListParameters, GetPartyAttributeCDto, GetListPartyAttributesCDto } from "redi-types";

const route = "PartyAttribute/";

export default class PartyAttributeService {
  //todo: get single, get List, 
  // Returns party
  static Get(
    Id: string
  ): Promise<HttpResult<GetListPartyCDto>> {
    let url = `${config.apiURL + route}Get`;

    return http({ url, method: "GET", Id })
      .catch(error => error);
  }

  // Returns list of party attributes
  static List(
    standardListParameters?: StandardListParameters,
    partyId?: string
  ): Promise<HttpResult<ListResponseDto<GetPartyAttributeCDto>>> {
    let url = `${config.apiURL + route}ListForPartyId`;

    return http({ url, method: "GET", standardListParameters, partyId })
      .catch(error => error);
  }

  static Create(data: GetPartyAttributeCDto): Promise<HttpResult<GetPartyAttributeCDto>> {
    let url = `${config.apiURL + route}Create`;

    return http({ url, method: "POST", data })
      .catch(error => error);
  }

  static Update(data: GetPartyAttributeCDto): Promise<HttpResult<GetPartyAttributeCDto>> {
    let url = `${config.apiURL + route}Update`;

    return http({ url, method: "POST", data })
      .catch(error => error);
  }


  static Delete(partyAttributeId: string): Promise<HttpResult> {
    let url = `${config.apiURL + route}Delete`;

    return http({ url, method: "POST", partyAttributeId })
      .catch(error => error);
  }
}
