import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { GetPartyRelationshipCDto, GetListPartyRelationshipCDto, ListResponseDto, StandardListParameters } from "redi-types";

const route = "PartyRelationship/";

export default class PartyRelationshipService {

  // Returns party relation
  static Get(
    Id: string
  ): Promise<HttpResult<ListResponseDto<GetPartyRelationshipCDto>>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", Id })
            .catch(error => error);
  }

  // Returns list of party relations
  static ListForPartyId(
    partyId: string,
    standardListParameters?: StandardListParameters,
    roleTypeCode?: string,
    relationshipTypeCode?: string,
    includeActiveOnly?: boolean,
    query?: string,
    returnParties?: boolean
  ): Promise<HttpResult<ListResponseDto<GetListPartyRelationshipCDto>>> {
    let url = `${config.apiURL + route}ListForPartyId`;

	  return http({ url, method: "GET", standardListParameters, partyId, roleTypeCode, relationshipTypeCode, includeActiveOnly, query, returnParties })
            .catch(error => error);
  }

  static Create(data: GetPartyRelationshipCDto): Promise<HttpResult<GetPartyRelationshipCDto>> {
		let url = `${config.apiURL + route}AddRelationship`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Update(data: GetPartyRelationshipCDto): Promise<HttpResult<GetPartyRelationshipCDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Delete(partyRelationshipId: string, toDate?: Date): Promise<HttpResult> {
		let url = `${config.apiURL + route}RemoveRelationship`;

		return http({ url, method: "POST", partyRelationshipId, toDate })
			      .catch(error => error);
	}
}
