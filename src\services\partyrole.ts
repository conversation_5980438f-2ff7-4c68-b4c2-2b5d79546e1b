import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { GetPartyRoleCDto, GetListPartyRoleCDto, ListResponseDto, StandardListParameters } from "redi-types";

const route = "PartyRole/";

export default class PartyRoleService {

  // Returns party role
  static Get(
    partyRoleId: string
  ): Promise<HttpResult<ListResponseDto<GetPartyRoleCDto>>> {
    let url = `${config.apiURL + route}Get`;

      return http({ url, method: "GET", partyRoleId })
            .catch(error => error);
  }

  // Returns list of parties
  static ListForPartyId(
    partyId: string,
    standardListParameters?: StandardListParameters,
    includeActiveOnly?: boolean
  ): Promise<HttpResult<ListResponseDto<GetListPartyRoleCDto>>> {
    let url = `${config.apiURL + route}ListForPartyId`;

      return http({ url, method: "GET", standardListParameters, partyId, includeActiveOnly })
            .catch(error => error);
  }

  static Create(data: GetPartyRoleCDto): Promise<HttpResult<GetPartyRoleCDto>> {
	  let url = `${config.apiURL + route}AddRoleToParty`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

  static Update(data: GetPartyRoleCDto): Promise<HttpResult<GetPartyRoleCDto>> {
		let url = `${config.apiURL + route}Update`;

		return http({ url, method: "POST", data })
			      .catch(error => error);
	}

	static Delete(partyRoleId: string, toDate?: Date): Promise<HttpResult> {
		let url = `${config.apiURL + route}RemoveRoleFromParty`;

		return http({ url, method: "POST", partyRoleId, toDate })
			      .catch(error => error);
	}
}
