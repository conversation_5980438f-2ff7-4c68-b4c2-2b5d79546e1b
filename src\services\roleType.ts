import { http, HttpResult } from "redi-http";
import config from "../config/config";
import { RoleTypeCDto } from "redi-types";

const route = "RoleType/";

export default class RoleTypeService {

  // Returns list of roles
  static GetList(query: string = ""): Promise<HttpResult<Array<RoleTypeCDto>>> {
    let url = `${config.apiURL + route}GetList`;

      return http({ url, method: "GET", query })
            .catch(error => error);
  }
}
