import { ThemeProvider } from "@emotion/react";
import { Link, Route, Routes } from "react-router-dom";
import logo from "../assets/rediLogo.png";
import { mainTheme } from "../config/theme/mainTheme";
import "./styles.scss";
import { MenuItem } from "redi-types";
import { Libraries, LoadScript } from "@react-google-maps/api";
import config from "../config/config";
import react from "react";

function Shell(props: Props) {
  const { routes } = props;
  const libraries: Libraries = ["places"];

  return (
    <ThemeProvider theme={mainTheme}>
      <header>
        <img src={logo} styleName="app-logo" alt="logo" title="Josh1" />
        <p>Micro Frontend Base</p>
        <Link styleName="link" to="">
          Components Demo
        </Link>
        <Link styleName="link" to="/Trucks">
          Trucks
        </Link>
        <Link styleName="link" to="/YardLog">
          Yard Log
        </Link>
      </header>
      <LoadScript googleMapsApiKey={config.googleApiKey} libraries={["places"]}>
        <div>
          <Routes>
            {routes.map((route) => (
              <react.Fragment key={route.path}>
                <Route
                  key={route.path}
                  path={route.path}
                  element={route.element}
                />
                {route.children &&
                  route.children.map((childRoute: MenuItem) =>
                    recursiveRoute(childRoute)
                  )}
              </react.Fragment>
            ))}
          </Routes>
        </div>
      </LoadScript>
    </ThemeProvider>
  );
  function recursiveRoute(route: MenuItem) {
    return (
      <Route key={route.path} path={route.path} element={route.element}>
        {route.children &&
          route.children.map((childRoute) => recursiveRoute(childRoute))}
      </Route>
    );
  }
}

interface Props {
  routes: MenuItem[];
}

export default Shell; //To test Authenticate add withAuthenticate(Shell, { options here... })
