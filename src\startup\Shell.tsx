import { ThemeProvider } from "@emotion/react";
import React from 'react';
import { Route, Routes } from "react-router-dom";
import { MenuItem, SideMenuProps } from "redi-types";
import logo from "../assets/AutoTow_logo_colour.webp";
import { mainTheme } from "../config/theme/mainTheme";
import "./styles.scss";
import { withAuthenticate } from "redi-security-components";

const SideMenu = React.lazy(() => import('sidemenu/SideMenu'));

interface Props {
  routes: MenuItem[];
}

function Shell(props: Props) {

  const { routes } = props;

  const sideMenuProps: SideMenuProps = {
    routes: routes,
    projectName: "AutoTow",
    requireLogin: false, // TODO: Get from config
    hide: false,
    companyLogo: logo,
    showRedi: true
  };
    
  return (
    <div styleName="page">
      <ThemeProvider theme={mainTheme}>        
        <SideMenu {...sideMenuProps}>
          <Routes>
            {
              routes.map((route) => (
                <Route key={route.path} path={route.path} element={route.element} />
              ))
            }
          </Routes>
        </SideMenu>
      </ThemeProvider>
    </div>
  );
}

export default withAuthenticate(Shell, {defaultRoute: "/Login"});