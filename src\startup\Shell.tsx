import { ThemeProvider } from "@emotion/react";
import { Link, Route, Routes } from "react-router-dom";
import logo from '../assets/rediLogo.png';
import withAuthenticate from "../components/Authenticate/withAuthenticate";
import { mainTheme } from "../config/theme/mainTheme";
import './styles.scss';

function Shell(props: Props) { 

  const { routes } = props;

  return (
    <ThemeProvider theme={mainTheme}>
      <header>
        <img src={logo} styleName="app-logo" alt="logo" />
        <p>CRM </p>
        <Link styleName="link" to="/">Component Demos</Link>
        <Link styleName="link" to="/Drivers">Drivers</Link>
        {/* <Link styleName="link" to="/Drivers">Drivers</Link> */}
      </header>
      <div>
        <Routes>
          {
            routes.map((a) => (
              <Route key={a.path} path={a.path} element={a.element}/>
            ))
          }
        </Routes>
      </div>
    </ThemeProvider>
  );
}

interface Props {
  routes: {
      path: string;
      name: string;
      element: JSX.Element;
  }[];
}

export default withAuthenticate(Shell);
//export default Shell;