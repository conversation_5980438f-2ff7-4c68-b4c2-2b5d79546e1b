import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { RouterProvider } from 'react-router-dom';
import { configureHttp } from "redi-http";
import config from '../config/config';
import '../config/fa_icons';
import router from '../config/router';
import { SecurityStoreProvider } from "redi-security-components";

configureHttp({ tokenKey: config.tokenKey, defaultHttpOptions: { dateParseOptions: { parseDate: false, parseTime: false, parseDateWithoutLocal: false }, useAuthentication: true } });

const SECURITY_DEFAULTS = {
  appId: "redi-microfrontend-base",
  tokenKey: config.tokenKey,
  tokenExpDateKey: config.tokenExpDateKey,
  defaultClaims: config.defaultClaims,
  defaultRoute: "/"
};

function Startup() {
  return (
    <div>
        <SecurityStoreProvider options={SECURITY_DEFAULTS}>
          <LocalizationProvider dateAdapter={AdapterDayjs as any} adapterLocale={'en'}>
            <RouterProvider router={router} fallbackElement={<div>Just loading</div>}/>
          </LocalizationProvider>
        </SecurityStoreProvider>
    </div>
  );
}

export default Startup;
