import { ThemeProvider } from "@emotion/react";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { RouterProvider } from "react-router-dom";
import { configureHttp } from "redi-http";
import config from '../config/config';
import '../config/fa_icons';
import router from '../config/router';
import { mainTheme } from "../config/theme/mainTheme";
import { SecurityStoreProvider } from "redi-security-components";
import { Toast, ModalDialog } from "redi-formik-material";

configureHttp({ tokenKey: config.tokenKey, defaultHttpOptions: { dateParseOptions: { parseDate: false, parseTime: false, parseDateWithoutLocal: false }, useAuthentication: true } });

const SECURITY_DEFAULTS = {
  appId: "redi-microfrontend-base",
  tokenKey: config.tokenKey,
  tokenExpDateKey: config.tokenExpDateKey,
  defaultClaims: config.defaultClaims,
  defaultRoute: "/JobManagement"
};

function Startup() {
  return (
    <SecurityStoreProvider options={SECURITY_DEFAULTS}>
      <LocalizationProvider dateAdapter={AdapterDayjs as any} adapterLocale={'en'}>
        <ThemeProvider theme={mainTheme}>
          <RouterProvider router={router} fallbackElement={<div>Just loading</div>}/>
          <ModalDialog />
          <Toast />
        </ThemeProvider>
      </LocalizationProvider>
    </SecurityStoreProvider>
  );
}

export default Startup;
