import React from 'react';
import { RouterProvider } from 'react-router-dom';
import router from '../config/router';

import config from '../config/config';
import { configureHttp } from "redi-http";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import '../config/fa_icons';
import { LoadScript } from "@react-google-maps/api";
import SecurityStoreProvider from '../components/Security/SecurityStoreProvider';

localStorage.setItem("security-store-redi-microfrontend-crm", JSON.stringify({"state":{"user":{"sub":"<EMAIL>","jti":"2d660ec3-f416-4dbc-89c8-595ec7518c2d","email":"<EMAIL>","http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier":"5185360c-020f-44b0-91fc-06ecae730531","userId":"5185360c-020f-44b0-91fc-06ecae730531","amr":"pwd","firstName":"Josh","lastName":"Colyn","fullName":"Josh Colyn","partyId":"","twofactorenabled":"false","exp":**********,"iss":"http://rediapps.com.au","aud":"http://rediapps.com.au","claims":["sub","jti","email","http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier","userId","amr","firstName","lastName","fullName","partyId","twofactorenabled","exp","iss","aud"]},"token":"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwianRpIjoiMmQ2NjBlYzMtZjQxNi00ZGJjLTg5YzgtNTk1ZWM3NTE4YzJkIiwiZW1haWwiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJ1c2VySWQiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJhbXIiOiJwd2QiLCJmaXJzdE5hbWUiOiJKb3NoIiwibGFzdE5hbWUiOiJDb2x5biIsImZ1bGxOYW1lIjoiSm9zaCBDb2x5biIsInBhcnR5SWQiOiIiLCJ0d29mYWN0b3JlbmFibGVkIjoiZmFsc2UiLCJleHAiOjE3NDcyNzIxMzAsImlzcyI6Imh0dHA6Ly9yZWRpYXBwcy5jb20uYXUiLCJhdWQiOiJodHRwOi8vcmVkaWFwcHMuY29tLmF1In0.RIm-3EnFsT7d8sCQidrMCJvZhvKDmGBb0Sc6bPqfN1i9IekmXS4f7W1rJYiGlSOX5kH7uxRJGBA_gqqWzTCpsA","tokenExpDate":"2025-05-15T01:22:12.150Z","isLoggedIn":true},"version":0}));
localStorage.setItem("token-key", "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwianRpIjoiMmQ2NjBlYzMtZjQxNi00ZGJjLTg5YzgtNTk1ZWM3NTE4YzJkIiwiZW1haWwiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJ1c2VySWQiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJhbXIiOiJwd2QiLCJmaXJzdE5hbWUiOiJKb3NoIiwibGFzdE5hbWUiOiJDb2x5biIsImZ1bGxOYW1lIjoiSm9zaCBDb2x5biIsInBhcnR5SWQiOiIiLCJ0d29mYWN0b3JlbmFibGVkIjoiZmFsc2UiLCJleHAiOjE3NDcyNzIxMzAsImlzcyI6Imh0dHA6Ly9yZWRpYXBwcy5jb20uYXUiLCJhdWQiOiJodHRwOi8vcmVkaWFwcHMuY29tLmF1In0.RIm-3EnFsT7d8sCQidrMCJvZhvKDmGBb0Sc6bPqfN1i9IekmXS4f7W1rJYiGlSOX5kH7uxRJGBA_gqqWzTCpsA");
localStorage.setItem("tokenExpDateKey", "2025-05-15T01:22:12.150Z");

configureHttp({ getToken: () => localStorage.getItem("token-key") as string, tokenKey: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwianRpIjoiMmQ2NjBlYzMtZjQxNi00ZGJjLTg5YzgtNTk1ZWM3NTE4YzJkIiwiZW1haWwiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJ1c2VySWQiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJhbXIiOiJwd2QiLCJmaXJzdE5hbWUiOiJKb3NoIiwibGFzdE5hbWUiOiJDb2x5biIsImZ1bGxOYW1lIjoiSm9zaCBDb2x5biIsInBhcnR5SWQiOiIiLCJ0d29mYWN0b3JlbmFibGVkIjoiZmFsc2UiLCJleHAiOjE3NDcyNzIxMzAsImlzcyI6Imh0dHA6Ly9yZWRpYXBwcy5jb20uYXUiLCJhdWQiOiJodHRwOi8vcmVkaWFwcHMuY29tLmF1In0.RIm-3EnFsT7d8sCQidrMCJvZhvKDmGBb0Sc6bPqfN1i9IekmXS4f7W1rJYiGlSOX5kH7uxRJGBA_gqqWzTCpsA", defaultHttpOptions: { dateParseOptions: { parseDate: false, parseTime: false, parseDateWithoutLocal: false }, useAuthentication: true } });

const SECURITY_DEFAULTS = {
  appId: "redi-microfrontend-crm",
  tokenKey: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwianRpIjoiMmQ2NjBlYzMtZjQxNi00ZGJjLTg5YzgtNTk1ZWM3NTE4YzJkIiwiZW1haWwiOiJKT1NIQ0BSRURJU09GVFdBUkUuQ09NLkFVIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZWlkZW50aWZpZXIiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJ1c2VySWQiOiI1MTg1MzYwYy0wMjBmLTQ0YjAtOTFmYy0wNmVjYWU3MzA1MzEiLCJhbXIiOiJwd2QiLCJmaXJzdE5hbWUiOiJKb3NoIiwibGFzdE5hbWUiOiJDb2x5biIsImZ1bGxOYW1lIjoiSm9zaCBDb2x5biIsInBhcnR5SWQiOiIiLCJ0d29mYWN0b3JlbmFibGVkIjoiZmFsc2UiLCJleHAiOjE3NDcyNzIxMzAsImlzcyI6Imh0dHA6Ly9yZWRpYXBwcy5jb20uYXUiLCJhdWQiOiJodHRwOi8vcmVkaWFwcHMuY29tLmF1In0.RIm-3EnFsT7d8sCQidrMCJvZhvKDmGBb0Sc6bPqfN1i9IekmXS4f7W1rJYiGlSOX5kH7uxRJGBA_gqqWzTCpsA",
  tokenExpDateKey: config.tokenExpDateKey,
  defaultClaims: config.defaultClaims,
  defaultRoute: "/",
  isLoggedIn: true
};


function Startup() {
  return (
    <div>
        <SecurityStoreProvider options={SECURITY_DEFAULTS}>
          <LocalizationProvider dateAdapter={AdapterDayjs as any} adapterLocale={'en'}>
            <LoadScript
              googleMapsApiKey={config.googleApiKey}
              libraries={["places"]}
            >
                      <RouterProvider router={router} fallbackElement={<div>Just loading</div>}/>
            </LoadScript>
          </LocalizationProvider>
        </SecurityStoreProvider>
    </div>
  );
}

export default Startup;
