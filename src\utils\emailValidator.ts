export class EmailValidator {

	static correctFormatMsg: string = "(Example: <EMAIL>)";

	static validate(val: string) {
		if (val !== null && val !== undefined && val !== "" && val !== " ") {
			if(val.length < 4) {
				return "too short";
			} else if(val.length > 255) {
				return "too long";
			} else {
				let at = val.indexOf('@');
				if(at != -1) {
					let prefix = val.substring(0, at);
					let domain = val.substring(at + 1);
					if(prefix.length == 0) {
						return "Invalid Prefix (<EMAIL>)";
					}

					let lastDot = domain.lastIndexOf('.');
					if(lastDot == -1) {
						return "Invalid Domain (.com, .org, .cc)";
					}
					let lastPortion = domain.substring(lastDot + 1);
					if(lastPortion.length < 2) {
						return "Invalid Domain (.com, .org, .cc)";
					}
					return null;
				}
			}
			
			return "Invalid Email: " + EmailValidator.correctFormatMsg;
		}
	}
}
