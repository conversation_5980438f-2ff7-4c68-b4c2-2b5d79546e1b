{"name": "ui", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"analyze": "source-map-explorer build/static/js/main.*", "start": "yarn install && node --max-old-space-size=4096 scripts/start.js", "build": "node scripts/build.js", "test": "node scripts/test.js  --passWithNoTests"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "resolutions": {"browserslist": "4.16.3", "caniuse-lite": "1.0.30001181"}, "private": true, "devDependencies": {"@babel/core": "7.1.0", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-decorators": "^7.1.2", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/preset-env": "^7.4.3", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.3.3", "@svgr/webpack": "2.4.1", "@types/jest": "^24.0.11", "@types/node": "^11.13.4", "@typescript-eslint/eslint-plugin": "^2.12.0", "@typescript-eslint/parser": "^4.28.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.7.1", "babel-loader": "8.0.4", "babel-plugin-named-asset-import": "^0.2.2", "babel-plugin-transform-decorators": "^6.24.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-polyfill": "^6.26.0", "babel-preset-react-app": "^7.0.2", "bfj": "6.1.1", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001043", "case-sensitive-paths-webpack-plugin": "2.1.2", "chalk": "2.4.1", "circular-dependency-plugin": "^5.0.2", "core-js": "^2.5.7", "css-loader": "1.0.0", "dotenv": "6.0.0", "dotenv-expand": "4.2.0", "dts-gen": "^0.5.7", "eslint": "^6.7.2", "eslint-loader": "^3.0.3", "eslint-plugin-react": "^7.17.0", "file-loader": "2.0.0", "fs-extra": "7.0.0", "html-webpack-plugin": "^4.0.0-beta.8", "identity-obj-proxy": "3.0.0", "jest": "^24.7.1", "jest-pnp-resolver": "^1.2.1", "jest-resolve": "^24.7.1", "mini-css-extract-plugin": "0.4.3", "node-sass": "^4.9.3", "optimize-css-assets-webpack-plugin": "5.0.1", "pnp-webpack-plugin": "1.1.0", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-preset-env": "6.0.6", "postcss-safe-parser": "4.0.1", "prettier": "^2.0.4", "raf-polyfill": "^1.0.0", "react-app-polyfill": "^0.1.3", "react-codemod": "^5.1.0", "react-dev-utils": "^6.0.4", "resolve": "1.8.1", "sass-loader": "7.1.0", "source-map-explorer": "^1.6.0", "source-map-loader": "^0.2.4", "style-loader": "0.23.0", "terser-webpack-plugin": "1.1.0", "ts-jest": "^24.0.1", "ts-loader": "^5.3.3", "typescript": "4.3.5", "url-loader": "1.1.1", "webpack": "4.29.6", "webpack-dev-server": "3.1.9", "webpack-filter-warnings-plugin": "^1.2.1", "webpack-manifest-plugin": "2.0.4", "worker-loader": "^2.0.0"}, "dependencies": {"@date-io/date-fns": "1.3.13", "@fortawesome/fontawesome-free": "^5.11.2", "@fortawesome/fontawesome-svg-core": "^1.2.25", "@fortawesome/free-regular-svg-icons": "^5.11.2", "@fortawesome/free-solid-svg-icons": "^5.11.2", "@fortawesome/react-fontawesome": "^0.1.7", "@material-ui/core": "^4.12.1", "@material-ui/icons": "4.11.2", "@material-ui/lab": "4.0.0-alpha.60", "@material-ui/pickers": "3.3.10", "@types/draft-js": "^0.10.38", "@types/history": "^4.7.3", "@types/react": "^17.0.14", "@types/react-css-modules": "^4.6.2", "@types/react-dom": "^16.9.3", "@types/react-html-parser": "^2.0.1", "@types/react-router": "^5.1.2", "@types/react-router-dom": "^5.1.2", "@types/uuid": "^3.4.6", "channel-event": "^3.1.9", "channel-store": "1.1.2", "date-fns": "^2.9.0", "downloadjs": "^1.4.7", "draft-js": "^0.11.4", "draft-js-anchor-plugin": "^2.0.3", "draft-js-drag-n-drop-plugin": "^2.0.4", "draft-js-export-html": "^1.4.1", "draft-js-focus-plugin": "^3.0.1", "draft-js-image-plugin": "^2.0.7", "draft-js-import-html": "^1.4.1", "draft-js-inline-toolbar-plugin": "^3.0.1", "draft-js-linkify-plugin": "^2.0.2", "draft-js-plugins-editor": "^3.0.0", "draft-js-resizeable-plugin": "^2.0.9", "draft-js-side-toolbar-plugin": "^3.0.2", "draft-js-static-toolbar-plugin": "^3.0.1", "draft-js-video-plugin": "^2.0.2", "formik": "^2.2.9", "history": "^4.10.1", "image-compressor.js": "^1.1.4", "libphonenumber-js": "^1.7.30", "memoize-one": "5.2.1", "react": "^17.0.2", "react-channel-event": "^2.0.13", "react-channel-store": "^1.0.6", "react-css-modules": "^4.7.11", "react-dom": "^16.11.0", "react-html-parser": "^2.0.2", "react-router": "^5.1.2", "react-router-dom": "^5.1.2", "react-toastify": "^5.4.0", "redi-auth": "^2.0.2", "redi-click-outside": "^1.0.2", "redi-component-utils": "^1.0.21", "redi-drag-drop": "^1.0.10", "redi-drawer": "^1.0.6", "redi-element-anchor": "^1.0.4", "redi-formik-material": "^2.0.10", "redi-http": "^2.2.10", "redi-login": "^1.0.6", "redi-query-builder": "^1.1.17", "redi-security-components": "1.0.7", "redi-shell-portal": "^1.0.0", "redi-smart-table": "^3.0.20", "redi-tooltip": "^1.1.1", "redi-ui-utils": "^2.0.2", "redi-user-management": "^1.0.16", "uuid": "^3.4.0", "webpack-service-worker": "^2.0.1", "yup": "^0.32.9"}}