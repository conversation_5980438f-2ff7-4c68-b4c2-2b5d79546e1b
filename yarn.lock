# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@ampproject/remapping/-/@ampproject/remapping-2.2.1.tgz"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@apideck/better-ajv-errors@^0.2.7":
  version "0.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@apideck/better-ajv-errors/-/@apideck/better-ajv-errors-0.2.7.tgz"
  dependencies:
    json-schema "^0.3.0"
    jsonpointer "^5.0.0"
    leven "^3.1.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.16.0", "@babel/code-frame@^7.22.5", "@babel/code-frame@^7.8.3":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/code-frame/-/@babel/code-frame-7.22.5.tgz"
  dependencies:
    "@babel/highlight" "^7.22.5"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/compat-data/-/@babel/compat-data-7.22.5.tgz"

"@babel/core@^7.11.1", "@babel/core@^7.12.3", "@babel/core@^7.16.0":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/core/-/@babel/core-7.22.5.tgz"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.22.5"
    "@babel/generator" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.22.5"
    "@babel/helper-module-transforms" "^7.22.5"
    "@babel/helpers" "^7.22.5"
    "@babel/parser" "^7.22.5"
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.5"
    "@babel/types" "^7.22.5"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.2"
    semver "^6.3.0"

"@babel/eslint-parser@^7.16.3":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/eslint-parser/-/@babel/eslint-parser-7.22.5.tgz"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.0"

"@babel/generator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/generator/-/@babel/generator-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6", "@babel/helper-annotate-as-pure@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-annotate-as-pure/-/@babel/helper-annotate-as-pure-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-builder-binary-assignment-operator-visitor/-/@babel/helper-builder-binary-assignment-operator-visitor-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-compilation-targets/-/@babel/helper-compilation-targets-7.22.5.tgz"
  dependencies:
    "@babel/compat-data" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.5"
    browserslist "^4.21.3"
    lru-cache "^5.1.1"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.21.0", "@babel/helper-create-class-features-plugin@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-create-class-features-plugin/-/@babel/helper-create-class-features-plugin-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-member-expression-to-functions" "^7.22.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.5"
    semver "^6.3.0"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-create-regexp-features-plugin/-/@babel/helper-create-regexp-features-plugin-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    regexpu-core "^5.3.1"
    semver "^6.3.0"

"@babel/helper-define-polyfill-provider@^0.4.0":
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-define-polyfill-provider/-/@babel/helper-define-polyfill-provider-0.4.0.tgz"
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-environment-visitor/-/@babel/helper-environment-visitor-7.22.5.tgz"

"@babel/helper-function-name@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-function-name/-/@babel/helper-function-name-7.22.5.tgz"
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-hoist-variables/-/@babel/helper-hoist-variables-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-member-expression-to-functions@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-member-expression-to-functions/-/@babel/helper-member-expression-to-functions-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-module-imports@^7.10.4", "@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-module-imports/-/@babel/helper-module-imports-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-module-transforms@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-module-transforms/-/@babel/helper-module-transforms-7.22.5.tgz"
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-simple-access" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-optimise-call-expression@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-optimise-call-expression/-/@babel/helper-optimise-call-expression-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-plugin-utils/-/@babel/helper-plugin-utils-7.22.5.tgz"

"@babel/helper-remap-async-to-generator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-remap-async-to-generator/-/@babel/helper-remap-async-to-generator-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-wrap-function" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-replace-supers@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-replace-supers/-/@babel/helper-replace-supers-7.22.5.tgz"
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-member-expression-to-functions" "^7.22.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helper-simple-access@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-simple-access/-/@babel/helper-simple-access-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0", "@babel/helper-skip-transparent-expression-wrappers@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-skip-transparent-expression-wrappers/-/@babel/helper-skip-transparent-expression-wrappers-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-split-export-declaration/-/@babel/helper-split-export-declaration-7.22.5.tgz"
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-string-parser/-/@babel/helper-string-parser-7.22.5.tgz"

"@babel/helper-validator-identifier@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-validator-identifier/-/@babel/helper-validator-identifier-7.22.5.tgz"

"@babel/helper-validator-option@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-validator-option/-/@babel/helper-validator-option-7.22.5.tgz"

"@babel/helper-wrap-function@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helper-wrap-function/-/@babel/helper-wrap-function-7.22.5.tgz"
  dependencies:
    "@babel/helper-function-name" "^7.22.5"
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/helpers@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/helpers/-/@babel/helpers-7.22.5.tgz"
  dependencies:
    "@babel/template" "^7.22.5"
    "@babel/traverse" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/highlight@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/highlight/-/@babel/highlight-7.22.5.tgz"
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.5"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/parser/-/@babel/parser-7.22.5.tgz"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-transform-optional-chaining" "^7.22.5"

"@babel/plugin-proposal-class-properties@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-class-properties/-/@babel/plugin-proposal-class-properties-7.18.6.tgz"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.16.4":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-decorators/-/@babel/plugin-proposal-decorators-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.5"
    "@babel/plugin-syntax-decorators" "^7.22.5"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-nullish-coalescing-operator/-/@babel/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-numeric-separator/-/@babel/plugin-proposal-numeric-separator-7.18.6.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-optional-chaining@^7.16.0":
  version "7.21.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-optional-chaining/-/@babel/plugin-proposal-optional-chaining-7.21.0.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-private-methods/-/@babel/plugin-proposal-private-methods-7.18.6.tgz"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-private-property-in-object/-/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"

"@babel/plugin-proposal-private-property-in-object@^7.21.0-placeholder-for-preset-env.2":
  version "7.21.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-private-property-in-object/-/@babel/plugin-proposal-private-property-in-object-7.21.11.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-proposal-unicode-property-regex/-/@babel/plugin-proposal-unicode-property-regex-7.18.6.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-async-generators/-/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-class-properties/-/@babel/plugin-syntax-class-properties-7.12.13.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-class-static-block/-/@babel/plugin-syntax-class-static-block-7.14.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-decorators/-/@babel/plugin-syntax-decorators-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-dynamic-import/-/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-export-namespace-from/-/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-flow@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-flow/-/@babel/plugin-syntax-flow-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-import-assertions@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-import-assertions/-/@babel/plugin-syntax-import-assertions-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-import-attributes@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-import-attributes/-/@babel/plugin-syntax-import-attributes-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-import-meta/-/@babel/plugin-syntax-import-meta-7.10.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-json-strings/-/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.21.4", "@babel/plugin-syntax-jsx@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-jsx/-/@babel/plugin-syntax-jsx-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-logical-assignment-operators/-/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-nullish-coalescing-operator/-/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-numeric-separator/-/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-object-rest-spread/-/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-optional-catch-binding/-/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-optional-chaining/-/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-private-property-in-object/-/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-top-level-await/-/@babel/plugin-syntax-top-level-await-7.14.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-typescript/-/@babel/plugin-syntax-typescript-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-syntax-unicode-sets-regex/-/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-arrow-functions/-/@babel/plugin-transform-arrow-functions-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-async-generator-functions@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-async-generator-functions/-/@babel/plugin-transform-async-generator-functions-7.22.5.tgz"
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-remap-async-to-generator" "^7.22.5"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-transform-async-to-generator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-async-to-generator/-/@babel/plugin-transform-async-to-generator-7.22.5.tgz"
  dependencies:
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-remap-async-to-generator" "^7.22.5"

"@babel/plugin-transform-block-scoped-functions@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-block-scoped-functions/-/@babel/plugin-transform-block-scoped-functions-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-block-scoping@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-block-scoping/-/@babel/plugin-transform-block-scoping-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-class-properties@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-class-properties/-/@babel/plugin-transform-class-properties-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-class-static-block@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-class-static-block/-/@babel/plugin-transform-class-static-block-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-transform-classes@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-classes/-/@babel/plugin-transform-classes-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.5"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-computed-properties/-/@babel/plugin-transform-computed-properties-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/template" "^7.22.5"

"@babel/plugin-transform-destructuring@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-destructuring/-/@babel/plugin-transform-destructuring-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-dotall-regex@^7.22.5", "@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-dotall-regex/-/@babel/plugin-transform-dotall-regex-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-duplicate-keys@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-duplicate-keys/-/@babel/plugin-transform-duplicate-keys-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-dynamic-import@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-dynamic-import/-/@babel/plugin-transform-dynamic-import-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-transform-exponentiation-operator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-exponentiation-operator/-/@babel/plugin-transform-exponentiation-operator-7.22.5.tgz"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-export-namespace-from@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-export-namespace-from/-/@babel/plugin-transform-export-namespace-from-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-transform-flow-strip-types@^7.16.0":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-flow-strip-types/-/@babel/plugin-transform-flow-strip-types-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-flow" "^7.22.5"

"@babel/plugin-transform-for-of@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-for-of/-/@babel/plugin-transform-for-of-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-function-name@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-function-name/-/@babel/plugin-transform-function-name-7.22.5.tgz"
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-json-strings@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-json-strings/-/@babel/plugin-transform-json-strings-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-transform-literals@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-literals/-/@babel/plugin-transform-literals-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-logical-assignment-operators@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-logical-assignment-operators/-/@babel/plugin-transform-logical-assignment-operators-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-member-expression-literals/-/@babel/plugin-transform-member-expression-literals-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-modules-amd@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-amd/-/@babel/plugin-transform-modules-amd-7.22.5.tgz"
  dependencies:
    "@babel/helper-module-transforms" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-modules-commonjs@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-commonjs/-/@babel/plugin-transform-modules-commonjs-7.22.5.tgz"
  dependencies:
    "@babel/helper-module-transforms" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-simple-access" "^7.22.5"

"@babel/plugin-transform-modules-systemjs@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-systemjs/-/@babel/plugin-transform-modules-systemjs-7.22.5.tgz"
  dependencies:
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-module-transforms" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"

"@babel/plugin-transform-modules-umd@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-modules-umd/-/@babel/plugin-transform-modules-umd-7.22.5.tgz"
  dependencies:
    "@babel/helper-module-transforms" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-named-capturing-groups-regex@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-named-capturing-groups-regex/-/@babel/plugin-transform-named-capturing-groups-regex-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-new-target@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-new-target/-/@babel/plugin-transform-new-target-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-nullish-coalescing-operator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-nullish-coalescing-operator/-/@babel/plugin-transform-nullish-coalescing-operator-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-transform-numeric-separator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-numeric-separator/-/@babel/plugin-transform-numeric-separator-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-transform-object-rest-spread@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-object-rest-spread/-/@babel/plugin-transform-object-rest-spread-7.22.5.tgz"
  dependencies:
    "@babel/compat-data" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.22.5"

"@babel/plugin-transform-object-super@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-object-super/-/@babel/plugin-transform-object-super-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-replace-supers" "^7.22.5"

"@babel/plugin-transform-optional-catch-binding@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-optional-catch-binding/-/@babel/plugin-transform-optional-catch-binding-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-transform-optional-chaining@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-optional-chaining/-/@babel/plugin-transform-optional-chaining-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-transform-parameters@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-parameters/-/@babel/plugin-transform-parameters-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-private-methods@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-private-methods/-/@babel/plugin-transform-private-methods-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-private-property-in-object@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-private-property-in-object/-/@babel/plugin-transform-private-property-in-object-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-property-literals/-/@babel/plugin-transform-property-literals-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-react-constant-elements@^7.12.1":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-constant-elements/-/@babel/plugin-transform-react-constant-elements-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-react-display-name@^7.16.0", "@babel/plugin-transform-react-display-name@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-display-name/-/@babel/plugin-transform-react-display-name-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-react-jsx-development@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-jsx-development/-/@babel/plugin-transform-react-jsx-development-7.22.5.tgz"
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.22.5"

"@babel/plugin-transform-react-jsx@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-jsx/-/@babel/plugin-transform-react-jsx-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/plugin-transform-react-pure-annotations@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-react-pure-annotations/-/@babel/plugin-transform-react-pure-annotations-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-regenerator@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-regenerator/-/@babel/plugin-transform-regenerator-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    regenerator-transform "^0.15.1"

"@babel/plugin-transform-reserved-words@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-reserved-words/-/@babel/plugin-transform-reserved-words-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-runtime@^7.15.8", "@babel/plugin-transform-runtime@^7.16.4":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-runtime/-/@babel/plugin-transform-runtime-7.22.5.tgz"
  dependencies:
    "@babel/helper-module-imports" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    babel-plugin-polyfill-corejs2 "^0.4.3"
    babel-plugin-polyfill-corejs3 "^0.8.1"
    babel-plugin-polyfill-regenerator "^0.5.0"
    semver "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-shorthand-properties/-/@babel/plugin-transform-shorthand-properties-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-spread@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-spread/-/@babel/plugin-transform-spread-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"

"@babel/plugin-transform-sticky-regex@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-sticky-regex/-/@babel/plugin-transform-sticky-regex-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-template-literals@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-template-literals/-/@babel/plugin-transform-template-literals-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-typeof-symbol@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-typeof-symbol/-/@babel/plugin-transform-typeof-symbol-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-typescript@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-typescript/-/@babel/plugin-transform-typescript-7.22.5.tgz"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-typescript" "^7.22.5"

"@babel/plugin-transform-unicode-escapes@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-escapes/-/@babel/plugin-transform-unicode-escapes-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-unicode-property-regex@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-property-regex/-/@babel/plugin-transform-unicode-property-regex-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-unicode-regex@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-regex/-/@babel/plugin-transform-unicode-regex-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-unicode-sets-regex@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/plugin-transform-unicode-sets-regex/-/@babel/plugin-transform-unicode-sets-regex-7.22.5.tgz"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/preset-env@^7.11.0", "@babel/preset-env@^7.12.1", "@babel/preset-env@^7.15.8", "@babel/preset-env@^7.16.4":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-env/-/@babel/preset-env-7.22.5.tgz"
  dependencies:
    "@babel/compat-data" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.22.5"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.22.5"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.22.5"
    "@babel/plugin-syntax-import-attributes" "^7.22.5"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.22.5"
    "@babel/plugin-transform-async-generator-functions" "^7.22.5"
    "@babel/plugin-transform-async-to-generator" "^7.22.5"
    "@babel/plugin-transform-block-scoped-functions" "^7.22.5"
    "@babel/plugin-transform-block-scoping" "^7.22.5"
    "@babel/plugin-transform-class-properties" "^7.22.5"
    "@babel/plugin-transform-class-static-block" "^7.22.5"
    "@babel/plugin-transform-classes" "^7.22.5"
    "@babel/plugin-transform-computed-properties" "^7.22.5"
    "@babel/plugin-transform-destructuring" "^7.22.5"
    "@babel/plugin-transform-dotall-regex" "^7.22.5"
    "@babel/plugin-transform-duplicate-keys" "^7.22.5"
    "@babel/plugin-transform-dynamic-import" "^7.22.5"
    "@babel/plugin-transform-exponentiation-operator" "^7.22.5"
    "@babel/plugin-transform-export-namespace-from" "^7.22.5"
    "@babel/plugin-transform-for-of" "^7.22.5"
    "@babel/plugin-transform-function-name" "^7.22.5"
    "@babel/plugin-transform-json-strings" "^7.22.5"
    "@babel/plugin-transform-literals" "^7.22.5"
    "@babel/plugin-transform-logical-assignment-operators" "^7.22.5"
    "@babel/plugin-transform-member-expression-literals" "^7.22.5"
    "@babel/plugin-transform-modules-amd" "^7.22.5"
    "@babel/plugin-transform-modules-commonjs" "^7.22.5"
    "@babel/plugin-transform-modules-systemjs" "^7.22.5"
    "@babel/plugin-transform-modules-umd" "^7.22.5"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.22.5"
    "@babel/plugin-transform-new-target" "^7.22.5"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.22.5"
    "@babel/plugin-transform-numeric-separator" "^7.22.5"
    "@babel/plugin-transform-object-rest-spread" "^7.22.5"
    "@babel/plugin-transform-object-super" "^7.22.5"
    "@babel/plugin-transform-optional-catch-binding" "^7.22.5"
    "@babel/plugin-transform-optional-chaining" "^7.22.5"
    "@babel/plugin-transform-parameters" "^7.22.5"
    "@babel/plugin-transform-private-methods" "^7.22.5"
    "@babel/plugin-transform-private-property-in-object" "^7.22.5"
    "@babel/plugin-transform-property-literals" "^7.22.5"
    "@babel/plugin-transform-regenerator" "^7.22.5"
    "@babel/plugin-transform-reserved-words" "^7.22.5"
    "@babel/plugin-transform-shorthand-properties" "^7.22.5"
    "@babel/plugin-transform-spread" "^7.22.5"
    "@babel/plugin-transform-sticky-regex" "^7.22.5"
    "@babel/plugin-transform-template-literals" "^7.22.5"
    "@babel/plugin-transform-typeof-symbol" "^7.22.5"
    "@babel/plugin-transform-unicode-escapes" "^7.22.5"
    "@babel/plugin-transform-unicode-property-regex" "^7.22.5"
    "@babel/plugin-transform-unicode-regex" "^7.22.5"
    "@babel/plugin-transform-unicode-sets-regex" "^7.22.5"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.22.5"
    babel-plugin-polyfill-corejs2 "^0.4.3"
    babel-plugin-polyfill-corejs3 "^0.8.1"
    babel-plugin-polyfill-regenerator "^0.5.0"
    core-js-compat "^3.30.2"
    semver "^6.3.0"

"@babel/preset-modules@^0.1.5":
  version "0.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-modules/-/@babel/preset-modules-0.1.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.12.5", "@babel/preset-react@^7.14.5", "@babel/preset-react@^7.16.0":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-react/-/@babel/preset-react-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.5"
    "@babel/plugin-transform-react-display-name" "^7.22.5"
    "@babel/plugin-transform-react-jsx" "^7.22.5"
    "@babel/plugin-transform-react-jsx-development" "^7.22.5"
    "@babel/plugin-transform-react-pure-annotations" "^7.22.5"

"@babel/preset-typescript@^7.10.4", "@babel/preset-typescript@^7.16.0":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/preset-typescript/-/@babel/preset-typescript-7.22.5.tgz"
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/helper-validator-option" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.22.5"
    "@babel/plugin-transform-modules-commonjs" "^7.22.5"
    "@babel/plugin-transform-typescript" "^7.22.5"

"@babel/regjsgen@^0.8.0":
  version "0.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/regjsgen/-/@babel/regjsgen-0.8.0.tgz"

"@babel/runtime@^7.11.2", "@babel/runtime@^7.12.5", "@babel/runtime@^7.16.3", "@babel/runtime@^7.18.3", "@babel/runtime@^7.18.9", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.8.7", "@babel/runtime@^7.9.2":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/runtime/-/@babel/runtime-7.22.5.tgz"
  dependencies:
    regenerator-runtime "^0.13.11"

"@babel/template@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/template/-/@babel/template-7.22.5.tgz"
  dependencies:
    "@babel/code-frame" "^7.22.5"
    "@babel/parser" "^7.22.5"
    "@babel/types" "^7.22.5"

"@babel/traverse@^7.22.5":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/traverse/-/@babel/traverse-7.22.5.tgz"
  dependencies:
    "@babel/code-frame" "^7.22.5"
    "@babel/generator" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.5"
    "@babel/helper-function-name" "^7.22.5"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.5"
    "@babel/parser" "^7.22.5"
    "@babel/types" "^7.22.5"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.12.6", "@babel/types@^7.21.4", "@babel/types@^7.22.5", "@babel/types@^7.4.4":
  version "7.22.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@babel/types/-/@babel/types-7.22.5.tgz"
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.5"
    to-fast-properties "^2.0.0"

"@csstools/normalize.css@*":
  version "12.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/normalize.css/-/@csstools/normalize.css-12.0.0.tgz"

"@csstools/postcss-cascade-layers@^1.1.1":
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-cascade-layers/-/@csstools/postcss-cascade-layers-1.1.1.tgz"
  dependencies:
    "@csstools/selector-specificity" "^2.0.2"
    postcss-selector-parser "^6.0.10"

"@csstools/postcss-color-function@^1.1.1":
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-color-function/-/@csstools/postcss-color-function-1.1.1.tgz"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-font-format-keywords@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-font-format-keywords/-/@csstools/postcss-font-format-keywords-1.0.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-hwb-function@^1.0.2":
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-hwb-function/-/@csstools/postcss-hwb-function-1.0.2.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-ic-unit@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-ic-unit/-/@csstools/postcss-ic-unit-1.0.1.tgz"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-is-pseudo-class@^2.0.7":
  version "2.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-is-pseudo-class/-/@csstools/postcss-is-pseudo-class-2.0.7.tgz"
  dependencies:
    "@csstools/selector-specificity" "^2.0.0"
    postcss-selector-parser "^6.0.10"

"@csstools/postcss-nested-calc@^1.0.0":
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-nested-calc/-/@csstools/postcss-nested-calc-1.0.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-normalize-display-values@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-normalize-display-values/-/@csstools/postcss-normalize-display-values-1.0.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-oklab-function@^1.1.1":
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-oklab-function/-/@csstools/postcss-oklab-function-1.1.1.tgz"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-progressive-custom-properties@^1.1.0", "@csstools/postcss-progressive-custom-properties@^1.3.0":
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-progressive-custom-properties/-/@csstools/postcss-progressive-custom-properties-1.3.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-stepped-value-functions@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-stepped-value-functions/-/@csstools/postcss-stepped-value-functions-1.0.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-text-decoration-shorthand@^1.0.0":
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-text-decoration-shorthand/-/@csstools/postcss-text-decoration-shorthand-1.0.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-trigonometric-functions@^1.0.2":
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-trigonometric-functions/-/@csstools/postcss-trigonometric-functions-1.0.2.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-unset-value@^1.0.2":
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/postcss-unset-value/-/@csstools/postcss-unset-value-1.0.2.tgz"

"@csstools/selector-specificity@^2.0.0", "@csstools/selector-specificity@^2.0.2":
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@csstools/selector-specificity/-/@csstools/selector-specificity-2.2.0.tgz"

"@date-io/core@^2.15.0", "@date-io/core@^2.16.0":
  version "2.16.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@date-io/core/-/@date-io/core-2.16.0.tgz"

"@date-io/date-fns@^2.15.0":
  version "2.16.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@date-io/date-fns/-/@date-io/date-fns-2.16.0.tgz"
  dependencies:
    "@date-io/core" "^2.16.0"

"@date-io/dayjs@^2.15.0":
  version "2.16.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@date-io/dayjs/-/@date-io/dayjs-2.16.0.tgz"
  dependencies:
    "@date-io/core" "^2.16.0"

"@date-io/luxon@^2.15.0":
  version "2.16.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@date-io/luxon/-/@date-io/luxon-2.16.1.tgz"
  dependencies:
    "@date-io/core" "^2.16.0"

"@date-io/moment@^2.15.0":
  version "2.16.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@date-io/moment/-/@date-io/moment-2.16.1.tgz"
  dependencies:
    "@date-io/core" "^2.16.0"

"@discoveryjs/json-ext@^0.5.0":
  version "0.5.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@discoveryjs/json-ext/-/@discoveryjs/json-ext-0.5.7.tgz"

"@dnd-kit/accessibility@^3.0.0":
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dnd-kit/accessibility/-/@dnd-kit/accessibility-3.0.1.tgz"
  dependencies:
    tslib "^2.0.0"

"@dnd-kit/core@^6.0.8":
  version "6.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dnd-kit/core/-/@dnd-kit/core-6.0.8.tgz"
  dependencies:
    "@dnd-kit/accessibility" "^3.0.0"
    "@dnd-kit/utilities" "^3.2.1"
    tslib "^2.0.0"

"@dnd-kit/sortable@^7.0.2":
  version "7.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dnd-kit/sortable/-/@dnd-kit/sortable-7.0.2.tgz"
  dependencies:
    "@dnd-kit/utilities" "^3.2.0"
    tslib "^2.0.0"

"@dnd-kit/utilities@^3.2.0", "@dnd-kit/utilities@^3.2.1":
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dnd-kit/utilities/-/@dnd-kit/utilities-3.2.1.tgz"
  dependencies:
    tslib "^2.0.0"

"@dr.pogodin/babel-plugin-react-css-modules@^6.9.3":
  version "6.9.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dr.pogodin/babel-plugin-react-css-modules/-/@dr.pogodin/babel-plugin-react-css-modules-6.9.4.tgz"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.21.4"
    "@babel/types" "^7.21.4"
    "@dr.pogodin/postcss-modules-parser" "^1.2.9"
    ajv "^8.12.0"
    ajv-keywords "^5.0.0"
    cssesc "^3.0.0"
    loader-utils "^3.2.1"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.0"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"

"@dr.pogodin/postcss-modules-parser@^1.2.9":
  version "1.2.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@dr.pogodin/postcss-modules-parser/-/@dr.pogodin/postcss-modules-parser-1.2.9.tgz"
  dependencies:
    icss-utils "^5.1.0"

"@emotion/babel-plugin@^11.11.0":
  version "11.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/babel-plugin/-/@emotion/babel-plugin-11.11.0.tgz"
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/serialize" "^1.1.2"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0":
  version "11.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/cache/-/@emotion/cache-11.11.0.tgz"
  dependencies:
    "@emotion/memoize" "^0.8.1"
    "@emotion/sheet" "^1.2.2"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    stylis "4.2.0"

"@emotion/hash@^0.9.1":
  version "0.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/hash/-/@emotion/hash-0.9.1.tgz"

"@emotion/is-prop-valid@^1.2.1":
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/is-prop-valid/-/@emotion/is-prop-valid-1.2.1.tgz"
  dependencies:
    "@emotion/memoize" "^0.8.1"

"@emotion/memoize@^0.8.1":
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/memoize/-/@emotion/memoize-0.8.1.tgz"

"@emotion/react@^11.10.6":
  version "11.11.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/react/-/@emotion/react-11.11.1.tgz"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/cache" "^11.11.0"
    "@emotion/serialize" "^1.1.2"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.1.2":
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/serialize/-/@emotion/serialize-1.1.2.tgz"
  dependencies:
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/unitless" "^0.8.1"
    "@emotion/utils" "^1.2.1"
    csstype "^3.0.2"

"@emotion/sheet@^1.2.2":
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/sheet/-/@emotion/sheet-1.2.2.tgz"

"@emotion/styled@^11.10.6":
  version "11.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/styled/-/@emotion/styled-11.11.0.tgz"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.11.0"
    "@emotion/is-prop-valid" "^1.2.1"
    "@emotion/serialize" "^1.1.2"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.0.1"
    "@emotion/utils" "^1.2.1"

"@emotion/unitless@^0.8.1":
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/unitless/-/@emotion/unitless-0.8.1.tgz"

"@emotion/use-insertion-effect-with-fallbacks@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/use-insertion-effect-with-fallbacks/-/@emotion/use-insertion-effect-with-fallbacks-1.0.1.tgz"

"@emotion/utils@^1.2.1":
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/utils/-/@emotion/utils-1.2.1.tgz"

"@emotion/weak-memoize@^0.3.1":
  version "0.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@emotion/weak-memoize/-/@emotion/weak-memoize-0.3.1.tgz"

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@eslint-community/eslint-utils/-/@eslint-community/eslint-utils-4.4.0.tgz"
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0":
  version "4.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@eslint-community/regexpp/-/@eslint-community/regexpp-4.5.1.tgz"

"@eslint/eslintrc@^2.0.3":
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@eslint/eslintrc/-/@eslint/eslintrc-2.0.3.tgz"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.5.2"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.43.0":
  version "8.43.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@eslint/js/-/@eslint/js-8.43.0.tgz"

"@fortawesome/fontawesome-common-types@6.4.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/fontawesome-common-types/-/6.4.0/fontawesome-common-types-6.4.0.tgz"

"@fortawesome/fontawesome-svg-core@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/fontawesome-svg-core/-/6.4.0/fontawesome-svg-core-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/pro-duotone-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-duotone-svg-icons/-/6.4.0/pro-duotone-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/pro-light-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-light-svg-icons/-/6.4.0/pro-light-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/pro-regular-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-regular-svg-icons/-/6.4.0/pro-regular-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/pro-solid-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-solid-svg-icons/-/6.4.0/pro-solid-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/pro-thin-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/pro-thin-svg-icons/-/6.4.0/pro-thin-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/react-fontawesome@^0.2.0":
  version "0.2.0"
  resolved "https://npm.fontawesome.com/@fortawesome/react-fontawesome/-/0.2.0/react-fontawesome-0.2.0.tgz"
  dependencies:
    prop-types "^15.8.1"

"@fortawesome/sharp-regular-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/sharp-regular-svg-icons/-/6.4.0/sharp-regular-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@fortawesome/sharp-solid-svg-icons@^6.3.0":
  version "6.4.0"
  resolved "https://npm.fontawesome.com/@fortawesome/sharp-solid-svg-icons/-/6.4.0/sharp-solid-svg-icons-6.4.0.tgz"
  dependencies:
    "@fortawesome/fontawesome-common-types" "6.4.0"

"@humanwhocodes/config-array@^0.11.10":
  version "0.11.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@humanwhocodes/config-array/-/@humanwhocodes/config-array-0.11.10.tgz"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    debug "^4.1.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@humanwhocodes/module-importer/-/@humanwhocodes/module-importer-1.0.1.tgz"

"@humanwhocodes/object-schema@^1.2.1":
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@humanwhocodes/object-schema/-/@humanwhocodes/object-schema-1.2.1.tgz"

"@icons/material@^0.2.4":
  version "0.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@icons/material/-/@icons/material-0.2.4.tgz"

"@jest/schemas@^29.4.3":
  version "29.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jest/schemas/-/@jest/schemas-29.4.3.tgz"
  dependencies:
    "@sinclair/typebox" "^0.25.16"

"@jest/types@^29.5.0":
  version "29.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jest/types/-/@jest/types-29.5.0.tgz"
  dependencies:
    "@jest/schemas" "^29.4.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/gen-mapping/-/@jridgewell/gen-mapping-0.3.3.tgz"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/resolve-uri/-/@jridgewell/resolve-uri-3.1.0.tgz"

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/set-array/-/@jridgewell/set-array-1.1.2.tgz"

"@jridgewell/source-map@^0.3.3":
  version "0.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/source-map/-/@jridgewell/source-map-0.3.3.tgz"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@1.4.14":
  version "1.4.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/sourcemap-codec/-/@jridgewell/sourcemap-codec-1.4.14.tgz"

"@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/sourcemap-codec/-/@jridgewell/sourcemap-codec-1.4.15.tgz"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.18"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@jridgewell/trace-mapping/-/@jridgewell/trace-mapping-0.3.18.tgz"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@leichtgewicht/ip-codec/-/@leichtgewicht/ip-codec-2.0.4.tgz"

"@mui/base@5.0.0-beta.4":
  version "5.0.0-beta.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/base/-/@mui/base-5.0.0-beta.4.tgz"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "@emotion/is-prop-valid" "^1.2.1"
    "@mui/types" "^7.2.4"
    "@mui/utils" "^5.13.1"
    "@popperjs/core" "^2.11.8"
    clsx "^1.2.1"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/base@5.0.0-beta.5":
  version "5.0.0-beta.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/base/-/@mui/base-5.0.0-beta.5.tgz"
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@emotion/is-prop-valid" "^1.2.1"
    "@mui/types" "^7.2.4"
    "@mui/utils" "^5.13.6"
    "@popperjs/core" "^2.11.8"
    clsx "^1.2.1"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/core-downloads-tracker@^5.13.4":
  version "5.13.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/core-downloads-tracker/-/@mui/core-downloads-tracker-5.13.4.tgz"

"@mui/icons-material@^5.11.11":
  version "5.11.16"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/icons-material/-/@mui/icons-material-5.11.16.tgz"
  dependencies:
    "@babel/runtime" "^7.21.0"

"@mui/lab@^5.0.0-alpha.123":
  version "5.0.0-alpha.134"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/lab/-/@mui/lab-5.0.0-alpha.134.tgz"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "@mui/base" "5.0.0-beta.4"
    "@mui/system" "^5.13.5"
    "@mui/types" "^7.2.4"
    "@mui/utils" "^5.13.1"
    clsx "^1.2.1"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/material@^5.11.13":
  version "5.13.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/material/-/@mui/material-5.13.6.tgz"
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@mui/base" "5.0.0-beta.5"
    "@mui/core-downloads-tracker" "^5.13.4"
    "@mui/system" "^5.13.6"
    "@mui/types" "^7.2.4"
    "@mui/utils" "^5.13.6"
    "@types/react-transition-group" "^4.4.6"
    clsx "^1.2.1"
    csstype "^3.1.2"
    prop-types "^15.8.1"
    react-is "^18.2.0"
    react-transition-group "^4.4.5"

"@mui/private-theming@^5.13.1":
  version "5.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/private-theming/-/@mui/private-theming-5.13.1.tgz"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "@mui/utils" "^5.13.1"
    prop-types "^15.8.1"

"@mui/styled-engine@^5.13.2":
  version "5.13.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/styled-engine/-/@mui/styled-engine-5.13.2.tgz"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "@emotion/cache" "^11.11.0"
    csstype "^3.1.2"
    prop-types "^15.8.1"

"@mui/system@^5.13.5", "@mui/system@^5.13.6":
  version "5.13.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/system/-/@mui/system-5.13.6.tgz"
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@mui/private-theming" "^5.13.1"
    "@mui/styled-engine" "^5.13.2"
    "@mui/types" "^7.2.4"
    "@mui/utils" "^5.13.6"
    clsx "^1.2.1"
    csstype "^3.1.2"
    prop-types "^15.8.1"

"@mui/types@^7.2.4":
  version "7.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/types/-/@mui/types-7.2.4.tgz"

"@mui/utils@^5.10.3", "@mui/utils@^5.13.1", "@mui/utils@^5.13.6":
  version "5.13.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/utils/-/@mui/utils-5.13.6.tgz"
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@types/prop-types" "^15.7.5"
    "@types/react-is" "^18.2.0"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/x-date-pickers@^5.0.19":
  version "5.0.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@mui/x-date-pickers/-/@mui/x-date-pickers-5.0.20.tgz"
  dependencies:
    "@babel/runtime" "^7.18.9"
    "@date-io/core" "^2.15.0"
    "@date-io/date-fns" "^2.15.0"
    "@date-io/dayjs" "^2.15.0"
    "@date-io/luxon" "^2.15.0"
    "@date-io/moment" "^2.15.0"
    "@mui/utils" "^5.10.3"
    "@types/react-transition-group" "^4.4.5"
    clsx "^1.2.1"
    prop-types "^15.7.2"
    react-transition-group "^4.4.5"
    rifm "^0.12.1"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nicolo-ribaudo/eslint-scope-5-internals/-/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz"
  dependencies:
    eslint-scope "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nodelib/fs.scandir/-/@nodelib/fs.scandir-2.1.5.tgz"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nodelib/fs.stat/-/@nodelib/fs.stat-2.0.5.tgz"

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@nodelib/fs.walk/-/@nodelib/fs.walk-1.2.8.tgz"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pmmmwh/react-refresh-webpack-plugin@^0.5.3":
  version "0.5.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@pmmmwh/react-refresh-webpack-plugin/-/@pmmmwh/react-refresh-webpack-plugin-0.5.10.tgz"
  dependencies:
    ansi-html-community "^0.0.8"
    common-path-prefix "^3.0.0"
    core-js-pure "^3.23.3"
    error-stack-parser "^2.0.6"
    find-up "^5.0.0"
    html-entities "^2.1.0"
    loader-utils "^2.0.4"
    schema-utils "^3.0.0"
    source-map "^0.7.3"

"@popperjs/core@^2.11.8":
  version "2.11.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@popperjs/core/-/@popperjs/core-2.11.8.tgz"

"@remix-run/router@1.7.0":
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@remix-run/router/-/@remix-run/router-1.7.0.tgz"

"@rollup/plugin-babel@^5.2.0":
  version "5.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@rollup/plugin-babel/-/@rollup/plugin-babel-5.3.1.tgz"
  dependencies:
    "@babel/helper-module-imports" "^7.10.4"
    "@rollup/pluginutils" "^3.1.0"

"@rollup/plugin-node-resolve@^11.2.1":
  version "11.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@rollup/plugin-node-resolve/-/@rollup/plugin-node-resolve-11.2.1.tgz"
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    "@types/resolve" "1.17.1"
    builtin-modules "^3.1.0"
    deepmerge "^4.2.2"
    is-module "^1.0.0"
    resolve "^1.19.0"

"@rollup/plugin-replace@^2.4.1":
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@rollup/plugin-replace/-/@rollup/plugin-replace-2.4.2.tgz"
  dependencies:
    "@rollup/pluginutils" "^3.1.0"
    magic-string "^0.25.7"

"@rollup/pluginutils@^3.1.0":
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@rollup/pluginutils/-/@rollup/pluginutils-3.1.0.tgz"
  dependencies:
    "@types/estree" "0.0.39"
    estree-walker "^1.0.1"
    picomatch "^2.2.2"

"@rushstack/eslint-patch@^1.1.0":
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@rushstack/eslint-patch/-/@rushstack/eslint-patch-1.3.2.tgz"

"@sinclair/typebox@^0.25.16":
  version "0.25.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@sinclair/typebox/-/@sinclair/typebox-0.25.24.tgz"

"@surma/rollup-plugin-off-main-thread@^2.2.3":
  version "2.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@surma/rollup-plugin-off-main-thread/-/@surma/rollup-plugin-off-main-thread-2.2.3.tgz"
  dependencies:
    ejs "^3.1.6"
    json5 "^2.2.0"
    magic-string "^0.25.0"
    string.prototype.matchall "^4.0.6"

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-add-jsx-attribute/-/@svgr/babel-plugin-add-jsx-attribute-5.4.0.tgz"

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-remove-jsx-attribute/-/@svgr/babel-plugin-remove-jsx-attribute-5.4.0.tgz"

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-remove-jsx-empty-expression/-/@svgr/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz"

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-replace-jsx-attribute-value/-/@svgr/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz"

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-svg-dynamic-title/-/@svgr/babel-plugin-svg-dynamic-title-5.4.0.tgz"

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-svg-em-dimensions/-/@svgr/babel-plugin-svg-em-dimensions-5.4.0.tgz"

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  version "5.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-transform-react-native-svg/-/@svgr/babel-plugin-transform-react-native-svg-5.4.0.tgz"

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-plugin-transform-svg-component/-/@svgr/babel-plugin-transform-svg-component-5.5.0.tgz"

"@svgr/babel-preset@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/babel-preset/-/@svgr/babel-preset-5.5.0.tgz"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/core@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/core/-/@svgr/core-5.5.0.tgz"
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    camelcase "^6.2.0"
    cosmiconfig "^7.0.0"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/hast-util-to-babel-ast/-/@svgr/hast-util-to-babel-ast-5.5.0.tgz"
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/plugin-jsx@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/plugin-jsx/-/@svgr/plugin-jsx-5.5.0.tgz"
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    svg-parser "^2.0.2"

"@svgr/plugin-svgo@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/plugin-svgo/-/@svgr/plugin-svgo-5.5.0.tgz"
  dependencies:
    cosmiconfig "^7.0.0"
    deepmerge "^4.2.2"
    svgo "^1.2.2"

"@svgr/webpack@^5.5.0":
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@svgr/webpack/-/@svgr/webpack-5.5.0.tgz"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/plugin-transform-react-constant-elements" "^7.12.1"
    "@babel/preset-env" "^7.12.1"
    "@babel/preset-react" "^7.12.5"
    "@svgr/core" "^5.5.0"
    "@svgr/plugin-jsx" "^5.5.0"
    "@svgr/plugin-svgo" "^5.5.0"
    loader-utils "^2.0.0"

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@trysound/sax/-/@trysound/sax-0.2.0.tgz"

"@types/autosuggest-highlight@^3.2.3":
  version "3.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/autosuggest-highlight/-/@types/autosuggest-highlight-3.2.3.tgz#966b4f6b2b8fc9df2838481600500db6b3795aaa"
  integrity sha1-lmtPayuPyd8oOEgWAFANtrN5Wqo=

"@types/body-parser@*":
  version "1.19.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/body-parser/-/@types/body-parser-1.19.2.tgz"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/bonjour/-/@types/bonjour-3.5.10.tgz"
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/connect-history-api-fallback/-/@types/connect-history-api-fallback-1.5.0.tgz"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.35"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/connect/-/@types/connect-3.4.35.tgz"
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  version "3.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/eslint-scope/-/@types/eslint-scope-3.7.4.tgz"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@^7.29.0 || ^8.4.1":
  version "8.40.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/eslint/-/@types/eslint-8.40.2.tgz"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/estree/-/@types/estree-1.0.1.tgz"

"@types/estree@0.0.39":
  version "0.0.39"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/estree/-/@types/estree-0.0.39.tgz"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.33":
  version "4.17.35"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/express-serve-static-core/-/@types/express-serve-static-core-4.17.35.tgz"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.17"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/express/-/@types/express-4.17.17.tgz"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/html-minifier-terser/-/@types/html-minifier-terser-6.1.0.tgz"

"@types/http-errors@*":
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/http-errors/-/@types/http-errors-2.0.1.tgz"

"@types/http-proxy@^1.17.8":
  version "1.17.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/http-proxy/-/@types/http-proxy-1.17.11.tgz"
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0":
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/istanbul-lib-coverage/-/@types/istanbul-lib-coverage-2.0.4.tgz"

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/istanbul-lib-report/-/@types/istanbul-lib-report-3.0.0.tgz"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/istanbul-reports/-/@types/istanbul-reports-3.0.1.tgz"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.4", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/json-schema/-/@types/json-schema-7.0.12.tgz"

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/json5/-/@types/json5-0.0.29.tgz"

"@types/mime@*":
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/mime/-/@types/mime-3.0.1.tgz"

"@types/mime@^1":
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/mime/-/@types/mime-1.3.2.tgz"

"@types/node@*":
  version "20.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/node/-/@types/node-20.3.2.tgz"

"@types/node@^16.18.12":
  version "16.18.37"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/node/-/@types/node-16.18.37.tgz"

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/parse-json/-/@types/parse-json-4.0.0.tgz"

"@types/prop-types@*", "@types/prop-types@^15.7.5":
  version "15.7.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/prop-types/-/@types/prop-types-15.7.5.tgz"

"@types/q@^1.5.1":
  version "1.5.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/q/-/@types/q-1.5.5.tgz"

"@types/qs@*":
  version "6.9.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/qs/-/@types/qs-6.9.7.tgz"

"@types/range-parser@*":
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/range-parser/-/@types/range-parser-1.2.4.tgz"

"@types/react-color@^3.0.6":
  version "3.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react-color/-/@types/react-color-3.0.6.tgz"
  dependencies:
    "@types/react" "*"
    "@types/reactcss" "*"

"@types/react-dom@^18.0.11":
  version "18.2.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react-dom/-/@types/react-dom-18.2.6.tgz"
  dependencies:
    "@types/react" "*"

"@types/react-is@^18.2.0":
  version "18.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react-is/-/@types/react-is-18.2.1.tgz"
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@^4.4.5", "@types/react-transition-group@^4.4.6":
  version "4.4.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react-transition-group/-/@types/react-transition-group-4.4.6.tgz"
  dependencies:
    "@types/react" "*"

"@types/react@*", "@types/react@^18.0.28":
  version "18.2.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/react/-/@types/react-18.2.14.tgz"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/reactcss@*":
  version "1.2.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/reactcss/-/@types/reactcss-1.2.6.tgz"
  dependencies:
    "@types/react" "*"

"@types/resolve@1.17.1":
  version "1.17.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/resolve/-/@types/resolve-1.17.1.tgz"
  dependencies:
    "@types/node" "*"

"@types/retry@0.12.0":
  version "0.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/retry/-/@types/retry-0.12.0.tgz"

"@types/scheduler@*":
  version "0.16.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/scheduler/-/@types/scheduler-0.16.3.tgz"

"@types/semver@^7.3.12":
  version "7.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/semver/-/@types/semver-7.5.0.tgz"

"@types/send@*":
  version "0.17.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/send/-/@types/send-0.17.1.tgz"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.1":
  version "1.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/serve-index/-/@types/serve-index-1.9.1.tgz"
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  version "1.15.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/serve-static/-/@types/serve-static-1.15.2.tgz"
  dependencies:
    "@types/http-errors" "*"
    "@types/mime" "*"
    "@types/node" "*"

"@types/sockjs@^0.3.33":
  version "0.3.33"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/sockjs/-/@types/sockjs-0.3.33.tgz"
  dependencies:
    "@types/node" "*"

"@types/trusted-types@^2.0.2":
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/trusted-types/-/@types/trusted-types-2.0.3.tgz"

"@types/uuid@^9.0.1":
  version "9.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/uuid/-/@types/uuid-9.0.2.tgz"

"@types/ws@^8.5.5":
  version "8.5.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/ws/-/@types/ws-8.5.5.tgz"
  dependencies:
    "@types/node" "*"

"@types/yargs-parser@*":
  version "21.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/yargs-parser/-/@types/yargs-parser-21.0.0.tgz"

"@types/yargs@^17.0.8":
  version "17.0.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/yargs/-/@types/yargs-17.0.24.tgz"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yup@^0.32.0":
  version "0.32.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@types/yup/-/@types/yup-0.32.0.tgz"
  dependencies:
    yup "*"

"@typescript-eslint/eslint-plugin@^5.5.0":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/eslint-plugin/-/@typescript-eslint/eslint-plugin-5.60.1.tgz"
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.60.1"
    "@typescript-eslint/type-utils" "5.60.1"
    "@typescript-eslint/utils" "5.60.1"
    debug "^4.3.4"
    grapheme-splitter "^1.0.4"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@^5.0.0":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/experimental-utils/-/@typescript-eslint/experimental-utils-5.60.1.tgz"
  dependencies:
    "@typescript-eslint/utils" "5.60.1"

"@typescript-eslint/parser@^5.5.0":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/parser/-/@typescript-eslint/parser-5.60.1.tgz"
  dependencies:
    "@typescript-eslint/scope-manager" "5.60.1"
    "@typescript-eslint/types" "5.60.1"
    "@typescript-eslint/typescript-estree" "5.60.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.60.1":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/scope-manager/-/@typescript-eslint/scope-manager-5.60.1.tgz"
  dependencies:
    "@typescript-eslint/types" "5.60.1"
    "@typescript-eslint/visitor-keys" "5.60.1"

"@typescript-eslint/type-utils@5.60.1":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/type-utils/-/@typescript-eslint/type-utils-5.60.1.tgz"
  dependencies:
    "@typescript-eslint/typescript-estree" "5.60.1"
    "@typescript-eslint/utils" "5.60.1"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.60.1":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/types/-/@typescript-eslint/types-5.60.1.tgz"

"@typescript-eslint/typescript-estree@5.60.1":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/typescript-estree/-/@typescript-eslint/typescript-estree-5.60.1.tgz"
  dependencies:
    "@typescript-eslint/types" "5.60.1"
    "@typescript-eslint/visitor-keys" "5.60.1"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.60.1", "@typescript-eslint/utils@^5.58.0":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/utils/-/@typescript-eslint/utils-5.60.1.tgz"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.60.1"
    "@typescript-eslint/types" "5.60.1"
    "@typescript-eslint/typescript-estree" "5.60.1"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.60.1":
  version "5.60.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@typescript-eslint/visitor-keys/-/@typescript-eslint/visitor-keys-5.60.1.tgz"
  dependencies:
    "@typescript-eslint/types" "5.60.1"
    eslint-visitor-keys "^3.3.0"

"@webassemblyjs/ast@1.11.6", "@webassemblyjs/ast@^1.11.5":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/ast/-/@webassemblyjs/ast-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"

"@webassemblyjs/floating-point-hex-parser@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/floating-point-hex-parser/-/@webassemblyjs/floating-point-hex-parser-1.11.6.tgz"

"@webassemblyjs/helper-api-error@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-api-error/-/@webassemblyjs/helper-api-error-1.11.6.tgz"

"@webassemblyjs/helper-buffer@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-buffer/-/@webassemblyjs/helper-buffer-1.11.6.tgz"

"@webassemblyjs/helper-numbers@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-numbers/-/@webassemblyjs/helper-numbers-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-wasm-bytecode/-/@webassemblyjs/helper-wasm-bytecode-1.11.6.tgz"

"@webassemblyjs/helper-wasm-section@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/helper-wasm-section/-/@webassemblyjs/helper-wasm-section-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-buffer" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.11.6"

"@webassemblyjs/ieee754@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/ieee754/-/@webassemblyjs/ieee754-1.11.6.tgz"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/leb128/-/@webassemblyjs/leb128-1.11.6.tgz"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/utf8/-/@webassemblyjs/utf8-1.11.6.tgz"

"@webassemblyjs/wasm-edit@^1.11.5":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-edit/-/@webassemblyjs/wasm-edit-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-buffer" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/helper-wasm-section" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.11.6"
    "@webassemblyjs/wasm-opt" "1.11.6"
    "@webassemblyjs/wasm-parser" "1.11.6"
    "@webassemblyjs/wast-printer" "1.11.6"

"@webassemblyjs/wasm-gen@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-gen/-/@webassemblyjs/wasm-gen-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wasm-opt@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-opt/-/@webassemblyjs/wasm-opt-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-buffer" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.11.6"
    "@webassemblyjs/wasm-parser" "1.11.6"

"@webassemblyjs/wasm-parser@1.11.6", "@webassemblyjs/wasm-parser@^1.11.5":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wasm-parser/-/@webassemblyjs/wasm-parser-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wast-printer@1.11.6":
  version "1.11.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webassemblyjs/wast-printer/-/@webassemblyjs/wast-printer-1.11.6.tgz"
  dependencies:
    "@webassemblyjs/ast" "1.11.6"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^2.1.1":
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webpack-cli/configtest/-/@webpack-cli/configtest-2.1.1.tgz"

"@webpack-cli/info@^2.0.2":
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webpack-cli/info/-/@webpack-cli/info-2.0.2.tgz"

"@webpack-cli/serve@^2.0.5":
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@webpack-cli/serve/-/@webpack-cli/serve-2.0.5.tgz"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@xtuc/ieee754/-/@xtuc/ieee754-1.2.0.tgz"

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/@xtuc/long/-/@xtuc/long-4.2.2.tgz"

abab@^2.0.5:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/abab/-/abab-2.0.6.tgz"

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/accepts/-/accepts-1.3.8.tgz"
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.9.0:
  version "1.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/acorn-import-assertions/-/acorn-import-assertions-1.9.0.tgz"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz"

acorn@^8.7.1, acorn@^8.8.0, acorn@^8.8.2:
  version "8.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/acorn/-/acorn-8.9.0.tgz"

address@^1.0.1, address@^1.1.2:
  version "1.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/address/-/address-1.2.2.tgz"

adjust-sourcemap-loader@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz"
  dependencies:
    loader-utils "^2.0.0"
    regex-parser "^2.2.11"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv-formats/-/ajv-formats-2.1.1.tgz"
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.4.1, ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv-keywords/-/ajv-keywords-3.5.2.tgz"

ajv-keywords@^5.0.0, ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.10.0, ajv@^6.12.2, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv/-/ajv-6.12.6.tgz"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.12.0, ajv@^8.6.0, ajv@^8.9.0:
  version "8.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ajv/-/ajv-8.12.0.tgz"
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-html-community/-/ansi-html-community-0.0.8.tgz"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-regex/-/ansi-regex-5.0.1.tgz"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-styles/-/ansi-styles-3.2.1.tgz"
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ansi-styles/-/ansi-styles-4.3.0.tgz"
  dependencies:
    color-convert "^2.0.1"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/anymatch/-/anymatch-3.1.3.tgz"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/argparse/-/argparse-1.0.10.tgz"
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/argparse/-/argparse-2.0.1.tgz"

aria-query@^5.1.3:
  version "5.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/aria-query/-/aria-query-5.3.0.tgz"
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz"
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-flatten/-/array-flatten-1.1.1.tgz"

array-flatten@^2.1.2:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-flatten/-/array-flatten-2.1.2.tgz"

array-includes@^3.1.5, array-includes@^3.1.6:
  version "3.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-includes/-/array-includes-3.1.6.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array-union/-/array-union-2.1.0.tgz"

array.prototype.flat@^1.3.1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array.prototype.flat/-/array.prototype.flat-1.3.1.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array.prototype.flatmap/-/array.prototype.flatmap-1.3.1.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"

array.prototype.reduce@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array.prototype.reduce/-/array.prototype.reduce-1.0.5.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-array-method-boxes-properly "^1.0.0"
    is-string "^1.0.7"

array.prototype.tosorted@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/array.prototype.tosorted/-/array.prototype.tosorted-1.1.1.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.1.3"

asap@~2.0.6:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/asap/-/asap-2.0.6.tgz"

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ast-types-flow/-/ast-types-flow-0.0.7.tgz"

async@^3.2.3:
  version "3.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/async/-/async-3.2.4.tgz"

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/at-least-node/-/at-least-node-1.0.0.tgz"

attr-accept@^2.2.2:
  version "2.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/attr-accept/-/attr-accept-2.2.2.tgz"

autoprefixer@^10.4.13:
  version "10.4.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/autoprefixer/-/autoprefixer-10.4.14.tgz"
  dependencies:
    browserslist "^4.21.5"
    caniuse-lite "^1.0.30001464"
    fraction.js "^4.2.0"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

autosuggest-highlight@^3.3.4:
  version "3.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/autosuggest-highlight/-/autosuggest-highlight-3.3.4.tgz#d71b575ba8eab40b5adba73df9244e9ba88cc387"
  integrity sha1-1xtXW6jqtAta26c9+SROm6iMw4c=
  dependencies:
    remove-accents "^0.4.2"

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"

axe-core@^4.6.2:
  version "4.7.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/axe-core/-/axe-core-4.7.2.tgz"

axobject-query@^3.1.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/axobject-query/-/axobject-query-3.2.1.tgz"
  dependencies:
    dequal "^2.0.3"

babel-loader@^8.2.3:
  version "8.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-loader/-/babel-loader-8.3.0.tgz"
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^2.0.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

babel-plugin-named-asset-import@^0.3.8:
  version "0.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-named-asset-import/-/babel-plugin-named-asset-import-0.3.8.tgz"

babel-plugin-polyfill-corejs2@^0.4.3:
  version "0.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.3.tgz"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.4.0"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.8.1:
  version "0.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.8.1.tgz"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.4.0"
    core-js-compat "^3.30.1"

babel-plugin-polyfill-regenerator@^0.5.0:
  version "0.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.5.0.tgz"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.4.0"

babel-plugin-transform-react-remove-prop-types@^0.4.24:
  version "0.4.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz"

babel-preset-react-app@^10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/babel-preset-react-app/-/babel-preset-react-app-10.0.1.tgz"
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-decorators" "^7.16.4"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-transform-flow-strip-types" "^7.16.0"
    "@babel/plugin-transform-react-display-name" "^7.16.0"
    "@babel/plugin-transform-runtime" "^7.16.4"
    "@babel/preset-env" "^7.16.4"
    "@babel/preset-react" "^7.16.0"
    "@babel/preset-typescript" "^7.16.0"
    "@babel/runtime" "^7.16.3"
    babel-plugin-macros "^3.1.0"
    babel-plugin-transform-react-remove-prop-types "^0.4.24"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/balanced-match/-/balanced-match-1.0.2.tgz"

batch@0.6.1:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/batch/-/batch-0.6.1.tgz"

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/big.js/-/big.js-5.2.2.tgz"

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/binary-extensions/-/binary-extensions-2.2.0.tgz"

body-parser@1.20.1:
  version "1.20.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/body-parser/-/body-parser-1.20.1.tgz"
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.0.11:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/bonjour-service/-/bonjour-service-1.1.1.tgz"
  dependencies:
    array-flatten "^2.1.2"
    dns-equal "^1.0.0"
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/boolbase/-/boolbase-1.0.0.tgz"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/brace-expansion/-/brace-expansion-1.1.11.tgz"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/brace-expansion/-/brace-expansion-2.0.1.tgz"
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/braces/-/braces-3.0.2.tgz"
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.0.0, browserslist@^4.14.5, browserslist@^4.18.1, browserslist@^4.21.3, browserslist@^4.21.4, browserslist@^4.21.5:
  version "4.21.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/browserslist/-/browserslist-4.21.9.tgz"
  dependencies:
    caniuse-lite "^1.0.30001503"
    electron-to-chromium "^1.4.431"
    node-releases "^2.0.12"
    update-browserslist-db "^1.0.11"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/buffer-from/-/buffer-from-1.1.2.tgz"

builtin-modules@^3.1.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/builtin-modules/-/builtin-modules-3.3.0.tgz"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/bytes/-/bytes-3.0.0.tgz"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/bytes/-/bytes-3.1.2.tgz"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/call-bind/-/call-bind-1.0.2.tgz"
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/callsites/-/callsites-3.1.0.tgz"

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/camel-case/-/camel-case-4.1.2.tgz"
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/camelcase/-/camelcase-6.3.0.tgz"

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/caniuse-api/-/caniuse-api-3.0.0.tgz"
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001464, caniuse-lite@^1.0.30001503:
  version "1.0.30001508"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/caniuse-lite/-/caniuse-lite-1.0.30001508.tgz"

case-sensitive-paths-webpack-plugin@^2.4.0:
  version "2.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"

chalk@^2.0.0, chalk@^2.4.1:
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chalk/-/chalk-2.4.2.tgz"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chalk/-/chalk-4.1.2.tgz"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.4.2, chokidar@^3.5.3:
  version "3.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chokidar/-/chokidar-3.5.3.tgz"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"

ci-info@^3.2.0:
  version "3.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ci-info/-/ci-info-3.8.0.tgz"

classnames@^2.2.5:
  version "2.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/classnames/-/classnames-2.3.2.tgz"

clean-css@^5.2.2:
  version "5.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/clean-css/-/clean-css-5.3.2.tgz"
  dependencies:
    source-map "~0.6.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/clone-deep/-/clone-deep-4.0.1.tgz"
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clsx@^1.1.1, clsx@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/clsx/-/clsx-1.2.1.tgz"

coa@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/coa/-/coa-2.0.2.tgz"
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-convert/-/color-convert-1.9.3.tgz"
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-convert/-/color-convert-2.0.1.tgz"
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-name/-/color-name-1.1.3.tgz"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/color-name/-/color-name-1.1.4.tgz"

colord@^2.9.1:
  version "2.9.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/colord/-/colord-2.9.3.tgz"

colorette@^2.0.10, colorette@^2.0.14:
  version "2.0.20"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/colorette/-/colorette-2.0.20.tgz"

commander@^10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-10.0.1.tgz"

commander@^2.20.0:
  version "2.20.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-2.20.3.tgz"

commander@^7.2.0:
  version "7.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-7.2.0.tgz"

commander@^8.3.0:
  version "8.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commander/-/commander-8.3.0.tgz"

common-path-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/common-path-prefix/-/common-path-prefix-3.0.0.tgz"

common-tags@^1.8.0:
  version "1.8.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/common-tags/-/common-tags-1.8.2.tgz"

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/commondir/-/commondir-1.0.1.tgz"

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/compressible/-/compressible-2.0.18.tgz"
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/compression/-/compression-1.7.4.tgz"
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/concat-map/-/concat-map-0.0.1.tgz"

confusing-browser-globals@^1.0.11:
  version "1.0.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz"

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz"

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/content-disposition/-/content-disposition-0.5.4.tgz"
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/content-type/-/content-type-1.0.5.tgz"

convert-source-map@^1.5.0, convert-source-map@^1.7.0:
  version "1.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/convert-source-map/-/convert-source-map-1.9.0.tgz"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cookie-signature/-/cookie-signature-1.0.6.tgz"

cookie@0.5.0:
  version "0.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cookie/-/cookie-0.5.0.tgz"

core-js-compat@^3.30.1, core-js-compat@^3.30.2:
  version "3.31.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-js-compat/-/core-js-compat-3.31.0.tgz"
  dependencies:
    browserslist "^4.21.5"

core-js-pure@^3.23.3:
  version "3.31.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-js-pure/-/core-js-pure-3.31.0.tgz"

core-js@^3.19.2:
  version "3.31.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-js/-/core-js-3.31.0.tgz"

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/core-util-is/-/core-util-is-1.0.3.tgz"

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cosmiconfig/-/cosmiconfig-6.0.0.tgz"
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cross-spawn/-/cross-spawn-7.0.3.tgz"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/crypto-random-string/-/crypto-random-string-2.0.0.tgz"

css-blank-pseudo@^3.0.3:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-blank-pseudo/-/css-blank-pseudo-3.0.3.tgz"
  dependencies:
    postcss-selector-parser "^6.0.9"

css-declaration-sorter@^6.3.1:
  version "6.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-declaration-sorter/-/css-declaration-sorter-6.4.0.tgz"

css-has-pseudo@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-has-pseudo/-/css-has-pseudo-3.0.4.tgz"
  dependencies:
    postcss-selector-parser "^6.0.9"

css-loader@^6.7.3:
  version "6.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-loader/-/css-loader-6.8.1.tgz"
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.21"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.3"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.3.8"

css-minimizer-webpack-plugin@^4.2.2:
  version "4.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-4.2.2.tgz"
  dependencies:
    cssnano "^5.1.8"
    jest-worker "^29.1.2"
    postcss "^8.4.17"
    schema-utils "^4.0.0"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"

css-prefers-color-scheme@^6.0.3:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-prefers-color-scheme/-/css-prefers-color-scheme-6.0.3.tgz"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz"

css-select@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-select/-/css-select-2.1.0.tgz"
  dependencies:
    boolbase "^1.0.0"
    css-what "^3.2.1"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-select@^4.1.3:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-select/-/css-select-4.3.0.tgz"
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@1.0.0-alpha.37:
  version "1.0.0-alpha.37"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-tree/-/css-tree-1.0.0-alpha.37.tgz"
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.6.1"

css-tree@^1.1.2, css-tree@^1.1.3:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-tree/-/css-tree-1.1.3.tgz"
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^3.2.1:
  version "3.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-what/-/css-what-3.4.2.tgz"

css-what@^6.0.1:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/css-what/-/css-what-6.1.0.tgz"

cssdb@^7.1.0:
  version "7.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssdb/-/cssdb-7.6.0.tgz"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssesc/-/cssesc-3.0.0.tgz"

cssnano-preset-default@^5.2.14:
  version "5.2.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz"
  dependencies:
    css-declaration-sorter "^6.3.1"
    cssnano-utils "^3.1.0"
    postcss-calc "^8.2.3"
    postcss-colormin "^5.3.1"
    postcss-convert-values "^5.1.3"
    postcss-discard-comments "^5.1.2"
    postcss-discard-duplicates "^5.1.0"
    postcss-discard-empty "^5.1.1"
    postcss-discard-overridden "^5.1.0"
    postcss-merge-longhand "^5.1.7"
    postcss-merge-rules "^5.1.4"
    postcss-minify-font-values "^5.1.0"
    postcss-minify-gradients "^5.1.1"
    postcss-minify-params "^5.1.4"
    postcss-minify-selectors "^5.2.1"
    postcss-normalize-charset "^5.1.0"
    postcss-normalize-display-values "^5.1.0"
    postcss-normalize-positions "^5.1.1"
    postcss-normalize-repeat-style "^5.1.1"
    postcss-normalize-string "^5.1.0"
    postcss-normalize-timing-functions "^5.1.0"
    postcss-normalize-unicode "^5.1.1"
    postcss-normalize-url "^5.1.0"
    postcss-normalize-whitespace "^5.1.1"
    postcss-ordered-values "^5.1.3"
    postcss-reduce-initial "^5.1.2"
    postcss-reduce-transforms "^5.1.0"
    postcss-svgo "^5.1.0"
    postcss-unique-selectors "^5.1.1"

cssnano-utils@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssnano-utils/-/cssnano-utils-3.1.0.tgz"

cssnano@^5.1.8:
  version "5.1.15"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/cssnano/-/cssnano-5.1.15.tgz"
  dependencies:
    cssnano-preset-default "^5.2.14"
    lilconfig "^2.0.3"
    yaml "^1.10.2"

csso@^4.0.2, csso@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/csso/-/csso-4.2.0.tgz"
  dependencies:
    css-tree "^1.1.2"

csstype@^3.0.2, csstype@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/csstype/-/csstype-3.1.2.tgz"

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"

date-fns@^2.8.0, date-fns@^2.8.1:
  version "2.30.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/date-fns/-/date-fns-2.30.0.tgz"
  dependencies:
    "@babel/runtime" "^7.21.0"

dayjs@^1.11.7:
  version "1.11.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dayjs/-/dayjs-1.11.8.tgz"

debug@2.6.9, debug@^2.6.0:
  version "2.6.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/debug/-/debug-2.6.9.tgz"
  dependencies:
    ms "2.0.0"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/debug/-/debug-3.2.7.tgz"
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/debug/-/debug-4.3.4.tgz"
  dependencies:
    ms "2.1.2"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/deep-is/-/deep-is-0.1.4.tgz"

deepmerge@^2.1.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/deepmerge/-/deepmerge-2.2.1.tgz"

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/deepmerge/-/deepmerge-4.3.1.tgz"

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/default-gateway/-/default-gateway-6.0.3.tgz"
  dependencies:
    execa "^5.0.0"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"

define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/define-properties/-/define-properties-1.2.0.tgz"
  dependencies:
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

depd@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/depd/-/depd-2.0.0.tgz"

depd@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/depd/-/depd-1.1.2.tgz"

dequal@^2.0.3:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dequal/-/dequal-2.0.3.tgz"

destroy@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/destroy/-/destroy-1.2.0.tgz"

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/detect-node/-/detect-node-2.1.0.tgz"

detect-port-alt@^1.1.6:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/detect-port-alt/-/detect-port-alt-1.1.6.tgz"
  dependencies:
    address "^1.0.1"
    debug "^2.6.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dir-glob/-/dir-glob-3.0.1.tgz"
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dns-equal/-/dns-equal-1.0.0.tgz"

dns-packet@^5.2.2:
  version "5.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dns-packet/-/dns-packet-5.6.0.tgz"
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/doctrine/-/doctrine-2.1.0.tgz"
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/doctrine/-/doctrine-3.0.0.tgz"
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-converter/-/dom-converter-0.2.0.tgz"
  dependencies:
    utila "~0.4"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-helpers/-/dom-helpers-5.2.1.tgz"
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dom-serializer@0:
  version "0.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-serializer/-/dom-serializer-0.2.2.tgz"
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dom-serializer/-/dom-serializer-1.4.1.tgz"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domelementtype/-/domelementtype-1.3.1.tgz"

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domelementtype/-/domelementtype-2.3.0.tgz"

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domhandler/-/domhandler-4.3.1.tgz"
  dependencies:
    domelementtype "^2.2.0"

domutils@^1.7.0:
  version "1.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domutils/-/domutils-1.7.0.tgz"
  dependencies:
    dom-serializer "0"
    domelementtype "1"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/domutils/-/domutils-2.8.0.tgz"
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dot-case/-/dot-case-3.0.4.tgz"
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dotenv-expand/-/dotenv-expand-5.1.0.tgz"

dotenv@^10.0.0:
  version "10.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/dotenv/-/dotenv-10.0.0.tgz"

duplexer@^0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/duplexer/-/duplexer-0.1.2.tgz"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ee-first/-/ee-first-1.1.1.tgz"

ejs@^3.1.6:
  version "3.1.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ejs/-/ejs-3.1.9.tgz"
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.4.431:
  version "1.4.441"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/electron-to-chromium/-/electron-to-chromium-1.4.441.tgz"

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/emoji-regex/-/emoji-regex-9.2.2.tgz"

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/emojis-list/-/emojis-list-3.0.0.tgz"

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/encodeurl/-/encodeurl-1.0.2.tgz"

enhanced-resolve@^5.15.0:
  version "5.15.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/enhanced-resolve/-/enhanced-resolve-5.15.0.tgz"
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/entities/-/entities-2.2.0.tgz"

envinfo@^7.7.3:
  version "7.10.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/envinfo/-/envinfo-7.10.0.tgz"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/error-ex/-/error-ex-1.3.2.tgz"
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.17.2, es-abstract@^1.19.0, es-abstract@^1.20.4, es-abstract@^1.21.2:
  version "1.21.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-abstract/-/es-abstract-1.21.2.tgz"
  dependencies:
    array-buffer-byte-length "^1.0.0"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.5"
    get-intrinsic "^1.2.0"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.10"
    is-weakref "^1.0.2"
    object-inspect "^1.12.3"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.4.3"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.7"
    string.prototype.trimend "^1.0.6"
    string.prototype.trimstart "^1.0.6"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.9"

es-array-method-boxes-properly@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz"

es-module-lexer@^1.2.1:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-module-lexer/-/es-module-lexer-1.3.0.tgz"

es-set-tostringtag@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz"
  dependencies:
    get-intrinsic "^1.1.3"
    has "^1.0.3"
    has-tostringtag "^1.0.0"

es-shim-unscopables@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-shim-unscopables/-/es-shim-unscopables-1.0.0.tgz"
  dependencies:
    has "^1.0.3"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escalade/-/escalade-3.1.1.tgz"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escape-html/-/escape-html-1.0.3.tgz"

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"

eslint-config-react-app@^7.0.1:
  version "7.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-config-react-app/-/eslint-config-react-app-7.0.1.tgz"
  dependencies:
    "@babel/core" "^7.16.0"
    "@babel/eslint-parser" "^7.16.3"
    "@rushstack/eslint-patch" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^5.5.0"
    "@typescript-eslint/parser" "^5.5.0"
    babel-preset-react-app "^10.0.1"
    confusing-browser-globals "^1.0.11"
    eslint-plugin-flowtype "^8.0.3"
    eslint-plugin-import "^2.25.3"
    eslint-plugin-jest "^25.3.0"
    eslint-plugin-jsx-a11y "^6.5.1"
    eslint-plugin-react "^7.27.1"
    eslint-plugin-react-hooks "^4.3.0"
    eslint-plugin-testing-library "^5.0.1"

eslint-import-resolver-node@^0.3.7:
  version "0.3.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.7.tgz"
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.11.0"
    resolve "^1.22.1"

eslint-module-utils@^2.7.4:
  version "2.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-module-utils/-/eslint-module-utils-2.8.0.tgz"
  dependencies:
    debug "^3.2.7"

eslint-plugin-flowtype@^8.0.3:
  version "8.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-flowtype/-/eslint-plugin-flowtype-8.0.3.tgz"
  dependencies:
    lodash "^4.17.21"
    string-natural-compare "^3.0.1"

eslint-plugin-import@^2.25.3:
  version "2.27.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-import/-/eslint-plugin-import-2.27.5.tgz"
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    array.prototype.flatmap "^1.3.1"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.7"
    eslint-module-utils "^2.7.4"
    has "^1.0.3"
    is-core-module "^2.11.0"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.values "^1.1.6"
    resolve "^1.22.1"
    semver "^6.3.0"
    tsconfig-paths "^3.14.1"

eslint-plugin-jest@^25.3.0:
  version "25.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-jest/-/eslint-plugin-jest-25.7.0.tgz"
  dependencies:
    "@typescript-eslint/experimental-utils" "^5.0.0"

eslint-plugin-jsx-a11y@^6.5.1:
  version "6.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.7.1.tgz"
  dependencies:
    "@babel/runtime" "^7.20.7"
    aria-query "^5.1.3"
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    ast-types-flow "^0.0.7"
    axe-core "^4.6.2"
    axobject-query "^3.1.1"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    has "^1.0.3"
    jsx-ast-utils "^3.3.3"
    language-tags "=1.0.5"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    semver "^6.3.0"

eslint-plugin-react-hooks@^4.3.0:
  version "4.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.0.tgz"

eslint-plugin-react@^7.27.1:
  version "7.32.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-react/-/eslint-plugin-react-7.32.2.tgz"
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    array.prototype.tosorted "^1.1.1"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    object.hasown "^1.1.2"
    object.values "^1.1.6"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.4"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.8"

eslint-plugin-testing-library@^5.0.1:
  version "5.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-plugin-testing-library/-/eslint-plugin-testing-library-5.11.0.tgz"
  dependencies:
    "@typescript-eslint/utils" "^5.58.0"

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-scope/-/eslint-scope-5.1.1.tgz"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.0:
  version "7.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-scope/-/eslint-scope-7.2.0.tgz"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1:
  version "3.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-visitor-keys/-/eslint-visitor-keys-3.4.1.tgz"

eslint-webpack-plugin@^3.1.1:
  version "3.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint-webpack-plugin/-/eslint-webpack-plugin-3.2.0.tgz"
  dependencies:
    "@types/eslint" "^7.29.0 || ^8.4.1"
    jest-worker "^28.0.2"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    schema-utils "^4.0.0"

eslint@^8.3.0:
  version "8.43.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eslint/-/eslint-8.43.0.tgz"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.4.0"
    "@eslint/eslintrc" "^2.0.3"
    "@eslint/js" "8.43.0"
    "@humanwhocodes/config-array" "^0.11.10"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.0"
    eslint-visitor-keys "^3.4.1"
    espree "^9.5.2"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"

espree@^9.5.2:
  version "9.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/espree/-/espree-9.5.2.tgz"
  dependencies:
    acorn "^8.8.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esprima/-/esprima-4.0.1.tgz"

esquery@^1.4.2:
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esquery/-/esquery-1.5.0.tgz"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esrecurse/-/esrecurse-4.3.0.tgz"
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/estraverse/-/estraverse-4.3.0.tgz"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/estraverse/-/estraverse-5.3.0.tgz"

estree-walker@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/estree-walker/-/estree-walker-1.0.1.tgz"

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/esutils/-/esutils-2.0.3.tgz"

etag@~1.8.1:
  version "1.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/etag/-/etag-1.8.1.tgz"

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/eventemitter3/-/eventemitter3-4.0.7.tgz"

events@^3.2.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/events/-/events-3.3.0.tgz"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/execa/-/execa-5.1.1.tgz"
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

express@^4.17.3:
  version "4.18.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/express/-/express-4.18.2.tgz"
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"

fast-glob@^3.2.9:
  version "3.2.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-glob/-/fast-glob-3.2.12.tgz"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"

fastest-levenshtein@^1.0.12:
  version "1.0.16"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz"

fastq@^1.6.0:
  version "1.15.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fastq/-/fastq-1.15.0.tgz"
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/faye-websocket/-/faye-websocket-0.11.4.tgz"
  dependencies:
    websocket-driver ">=0.5.1"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  dependencies:
    flat-cache "^3.0.4"

file-loader@^6.2.0:
  version "6.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/file-loader/-/file-loader-6.2.0.tgz"
  dependencies:
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

file-selector@^0.6.0:
  version "0.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/file-selector/-/file-selector-0.6.0.tgz"
  dependencies:
    tslib "^2.4.0"

filelist@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/filelist/-/filelist-1.0.4.tgz"
  dependencies:
    minimatch "^5.0.1"

filesize@^8.0.6:
  version "8.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/filesize/-/filesize-8.0.7.tgz"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fill-range/-/fill-range-7.0.1.tgz"
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/finalhandler/-/finalhandler-1.2.0.tgz"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-root/-/find-root-1.1.0.tgz"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-up/-/find-up-3.0.0.tgz"
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-up/-/find-up-4.1.0.tgz"
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/find-up/-/find-up-5.0.0.tgz"
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/flat-cache/-/flat-cache-3.0.4.tgz"
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/flatted/-/flatted-3.2.7.tgz"

follow-redirects@^1.0.0:
  version "1.15.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/follow-redirects/-/follow-redirects-1.15.2.tgz"

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/for-each/-/for-each-0.3.3.tgz"
  dependencies:
    is-callable "^1.1.3"

fork-ts-checker-webpack-plugin@^6.5.0:
  version "6.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-6.5.3.tgz"
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@types/json-schema" "^7.0.5"
    chalk "^4.1.0"
    chokidar "^3.4.2"
    cosmiconfig "^6.0.0"
    deepmerge "^4.2.2"
    fs-extra "^9.0.0"
    glob "^7.1.6"
    memfs "^3.1.2"
    minimatch "^3.0.4"
    schema-utils "2.7.0"
    semver "^7.3.2"
    tapable "^1.0.0"

formik@^2.2.9:
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/formik/-/formik-2.4.2.tgz"
  dependencies:
    deepmerge "^2.1.1"
    hoist-non-react-statics "^3.3.0"
    lodash "^4.17.21"
    lodash-es "^4.17.21"
    react-fast-compare "^2.0.1"
    tiny-warning "^1.0.2"
    tslib "^2.0.0"

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/forwarded/-/forwarded-0.2.0.tgz"

fraction.js@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fraction.js/-/fraction.js-4.2.0.tgz"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fresh/-/fresh-0.5.2.tgz"

fs-extra@^9.0.0, fs-extra@^9.0.1:
  version "9.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fs-extra/-/fs-extra-9.1.0.tgz"
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fs-monkey/-/fs-monkey-1.0.4.tgz"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fs.realpath/-/fs.realpath-1.0.0.tgz"

fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/function-bind/-/function-bind-1.1.1.tgz"

function.prototype.name@^1.1.5:
  version "1.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/function.prototype.name/-/function.prototype.name-1.1.5.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"
    functions-have-names "^1.2.2"

functions-have-names@^1.2.2, functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/functions-have-names/-/functions-have-names-1.2.3.tgz"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/gensync/-/gensync-1.0.0-beta.2.tgz"

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-intrinsic/-/get-intrinsic-1.2.1.tgz"
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-stream/-/get-stream-6.0.1.tgz"

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob-parent/-/glob-parent-5.1.2.tgz"
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob-parent/-/glob-parent-6.0.2.tgz"
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"

glob@^7.1.3, glob@^7.1.6:
  version "7.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/glob/-/glob-7.2.3.tgz"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/global-modules/-/global-modules-2.0.0.tgz"
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/global-prefix/-/global-prefix-3.0.0.tgz"
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globals/-/globals-11.12.0.tgz"

globals@^13.19.0:
  version "13.20.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globals/-/globals-13.20.0.tgz"
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globalthis/-/globalthis-1.0.3.tgz"
  dependencies:
    define-properties "^1.1.3"

globby@^11.0.4, globby@^11.1.0:
  version "11.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/globby/-/globby-11.1.0.tgz"
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/gopd/-/gopd-1.0.1.tgz"
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.6, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/graceful-fs/-/graceful-fs-4.2.11.tgz"

grapheme-splitter@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/grapheme-splitter/-/grapheme-splitter-1.0.4.tgz"

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/graphemer/-/graphemer-1.4.0.tgz"

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/gzip-size/-/gzip-size-6.0.0.tgz"
  dependencies:
    duplexer "^0.1.2"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/handle-thing/-/handle-thing-2.0.1.tgz"

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-bigints/-/has-bigints-1.0.2.tgz"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-flag/-/has-flag-3.0.0.tgz"

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-flag/-/has-flag-4.0.0.tgz"

has-property-descriptors@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz"
  dependencies:
    get-intrinsic "^1.1.1"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-proto/-/has-proto-1.0.1.tgz"

has-symbols@^1.0.1, has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-symbols/-/has-symbols-1.0.3.tgz"

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  dependencies:
    has-symbols "^1.0.2"

has@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/has/-/has-1.0.3.tgz"
  dependencies:
    function-bind "^1.1.1"

he@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/he/-/he-1.2.0.tgz"

hoist-non-react-statics@^2.5.5:
  version "2.5.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/hoist-non-react-statics/-/hoist-non-react-statics-2.5.5.tgz"

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  dependencies:
    react-is "^16.7.0"

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/hpack.js/-/hpack.js-2.1.6.tgz"
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.1.0, html-entities@^2.3.2:
  version "2.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/html-entities/-/html-entities-2.4.0.tgz"

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-webpack-plugin@^5.5.0:
  version "5.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/html-webpack-plugin/-/html-webpack-plugin-5.5.3.tgz"
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/htmlparser2/-/htmlparser2-6.1.0.tgz"
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-deceiver/-/http-deceiver-1.2.7.tgz"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-errors/-/http-errors-2.0.0.tgz"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-errors/-/http-errors-1.6.3.tgz"
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-parser-js/-/http-parser-js-0.5.8.tgz"

http-proxy-middleware@^2.0.3:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz"
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/http-proxy/-/http-proxy-1.18.1.tgz"
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/human-signals/-/human-signals-2.1.0.tgz"

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/iconv-lite/-/iconv-lite-0.4.24.tgz"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/iconv-lite/-/iconv-lite-0.6.3.tgz"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/icss-utils/-/icss-utils-5.1.0.tgz"

idb@^6.1.4:
  version "6.1.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/idb/-/idb-6.1.5.tgz"

ignore@^5.2.0:
  version "5.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ignore/-/ignore-5.2.4.tgz"

immer@^9.0.7:
  version "9.0.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/immer/-/immer-9.0.21.tgz"

immutable@^4.0.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/immutable/-/immutable-4.3.0.tgz"

import-fresh@^3.0.0, import-fresh@^3.1.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/import-fresh/-/import-fresh-3.3.0.tgz"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/import-local/-/import-local-3.1.0.tgz"
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/imurmurhash/-/imurmurhash-0.1.4.tgz"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/inflight/-/inflight-1.0.6.tgz"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/inherits/-/inherits-2.0.4.tgz"

inherits@2.0.3:
  version "2.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/inherits/-/inherits-2.0.3.tgz"

ini@^1.3.5:
  version "1.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ini/-/ini-1.3.8.tgz"

internal-slot@^1.0.3, internal-slot@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/internal-slot/-/internal-slot-1.0.5.tgz"
  dependencies:
    get-intrinsic "^1.2.0"
    has "^1.0.3"
    side-channel "^1.0.4"

interpret@^3.1.1:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/interpret/-/interpret-3.1.1.tgz"

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ipaddr.js/-/ipaddr.js-1.9.1.tgz"

ipaddr.js@^2.0.1:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ipaddr.js/-/ipaddr.js-2.1.0.tgz"

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-array-buffer/-/is-array-buffer-3.0.2.tgz"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-arrayish/-/is-arrayish-0.2.1.tgz"

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-bigint/-/is-bigint-1.0.4.tgz"
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-binary-path/-/is-binary-path-2.1.0.tgz"
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-callable/-/is-callable-1.2.7.tgz"

is-core-module@^2.11.0, is-core-module@^2.9.0:
  version "2.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-core-module/-/is-core-module-2.12.1.tgz"
  dependencies:
    has "^1.0.3"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-date-object/-/is-date-object-1.0.5.tgz"
  dependencies:
    has-tostringtag "^1.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-docker/-/is-docker-2.2.1.tgz"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-extglob/-/is-extglob-2.1.1.tgz"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-glob/-/is-glob-4.0.3.tgz"
  dependencies:
    is-extglob "^2.1.1"

is-module@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-module/-/is-module-1.0.0.tgz"

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-negative-zero/-/is-negative-zero-2.0.2.tgz"

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-number-object/-/is-number-object-1.0.7.tgz"
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-number/-/is-number-7.0.0.tgz"

is-obj@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-obj/-/is-obj-1.0.1.tgz"

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-path-inside/-/is-path-inside-3.0.3.tgz"

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-plain-obj/-/is-plain-obj-3.0.0.tgz"

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-plain-object/-/is-plain-object-2.0.4.tgz"
  dependencies:
    isobject "^3.0.1"

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-regex/-/is-regex-1.1.4.tgz"
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-regexp/-/is-regexp-1.0.0.tgz"

is-root@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-root/-/is-root-2.1.0.tgz"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  dependencies:
    call-bind "^1.0.2"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-stream/-/is-stream-2.0.1.tgz"

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-string/-/is-string-1.0.7.tgz"
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-symbol/-/is-symbol-1.0.4.tgz"
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.9:
  version "1.1.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-typed-array/-/is-typed-array-1.1.10.tgz"
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-weakref/-/is-weakref-1.0.2.tgz"
  dependencies:
    call-bind "^1.0.2"

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/is-wsl/-/is-wsl-2.2.0.tgz"
  dependencies:
    is-docker "^2.0.0"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isarray/-/isarray-2.0.5.tgz"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isarray/-/isarray-1.0.0.tgz"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isexe/-/isexe-2.0.0.tgz"

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/isobject/-/isobject-3.0.1.tgz"

jake@^10.8.5:
  version "10.8.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jake/-/jake-10.8.7.tgz"
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-util@^29.5.0:
  version "29.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-util/-/jest-util-29.5.0.tgz"
  dependencies:
    "@jest/types" "^29.5.0"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-worker@^26.2.1:
  version "26.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-worker/-/jest-worker-26.6.2.tgz"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-worker/-/jest-worker-27.5.1.tgz"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^28.0.2:
  version "28.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-worker/-/jest-worker-28.1.3.tgz"
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^29.1.2:
  version "29.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jest-worker/-/jest-worker-29.5.0.tgz"
  dependencies:
    "@types/node" "*"
    jest-util "^29.5.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/js-tokens/-/js-tokens-4.0.0.tgz"

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/js-yaml/-/js-yaml-3.14.1.tgz"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/js-yaml/-/js-yaml-4.1.0.tgz"
  dependencies:
    argparse "^2.0.1"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsesc/-/jsesc-2.5.2.tgz"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsesc/-/jsesc-0.5.0.tgz"

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"

json-schema@^0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-schema/-/json-schema-0.3.0.tgz"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"

json5@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json5/-/json5-1.0.2.tgz"
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2, json5@^2.2.0, json5@^2.2.2:
  version "2.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/json5/-/json5-2.2.3.tgz"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsonfile/-/jsonfile-6.1.0.tgz"
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonpointer@^5.0.0:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsonpointer/-/jsonpointer-5.0.1.tgz"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.3:
  version "3.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/jsx-ast-utils/-/jsx-ast-utils-3.3.3.tgz"
  dependencies:
    array-includes "^3.1.5"
    object.assign "^4.1.3"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/kind-of/-/kind-of-6.0.3.tgz"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/kleur/-/kleur-3.0.3.tgz"

klona@^2.0.4, klona@^2.0.5:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/klona/-/klona-2.0.6.tgz"

language-subtag-registry@~0.3.2:
  version "0.3.22"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/language-subtag-registry/-/language-subtag-registry-0.3.22.tgz"

language-tags@=1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/language-tags/-/language-tags-1.0.5.tgz"
  dependencies:
    language-subtag-registry "~0.3.2"

launch-editor@^2.6.0:
  version "2.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/launch-editor/-/launch-editor-2.6.0.tgz"
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.7.3"

leven@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/leven/-/leven-3.1.0.tgz"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/levn/-/levn-0.4.1.tgz"
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

libphonenumber-js@^1.10.26:
  version "1.10.36"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/libphonenumber-js/-/libphonenumber-js-1.10.36.tgz"

lilconfig@^2.0.3:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lilconfig/-/lilconfig-2.1.0.tgz"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lines-and-columns/-/lines-and-columns-1.2.4.tgz"

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loader-runner/-/loader-runner-4.3.0.tgz"

loader-utils@^2.0.0, loader-utils@^2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loader-utils/-/loader-utils-2.0.4.tgz"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

loader-utils@^3.2.0, loader-utils@^3.2.1:
  version "3.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loader-utils/-/loader-utils-3.2.1.tgz"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/locate-path/-/locate-path-3.0.0.tgz"
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/locate-path/-/locate-path-5.0.0.tgz"
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/locate-path/-/locate-path-6.0.0.tgz"
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.15, lodash-es@^4.17.21:
  version "4.17.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash-es/-/lodash-es-4.17.21.tgz"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.debounce/-/lodash.debounce-4.0.8.tgz"

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.memoize/-/lodash.memoize-4.1.2.tgz"

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.merge/-/lodash.merge-4.6.2.tgz"

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.sortby/-/lodash.sortby-4.7.0.tgz"

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash.uniq/-/lodash.uniq-4.5.0.tgz"

lodash@^4.0.1, lodash@^4.16.6, lodash@^4.17.10, lodash@^4.17.15, lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lodash/-/lodash-4.17.21.tgz"

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/loose-envify/-/loose-envify-1.4.0.tgz"
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lower-case/-/lower-case-2.0.2.tgz"
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lru-cache/-/lru-cache-5.1.1.tgz"
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/lru-cache/-/lru-cache-6.0.0.tgz"
  dependencies:
    yallist "^4.0.0"

magic-string@^0.25.0, magic-string@^0.25.7:
  version "0.25.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/magic-string/-/magic-string-0.25.9.tgz"
  dependencies:
    sourcemap-codec "^1.4.8"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/make-dir/-/make-dir-3.1.0.tgz"
  dependencies:
    semver "^6.0.0"

material-colors@^1.2.1:
  version "1.2.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/material-colors/-/material-colors-1.2.6.tgz"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mdn-data/-/mdn-data-2.0.14.tgz"

mdn-data@2.0.4:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mdn-data/-/mdn-data-2.0.4.tgz"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/media-typer/-/media-typer-0.3.0.tgz"

memfs@^3.1.2, memfs@^3.4.3:
  version "3.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/memfs/-/memfs-3.6.0.tgz"
  dependencies:
    fs-monkey "^1.0.4"

memoize-one@^6.0.0:
  version "6.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/memoize-one/-/memoize-one-6.0.0.tgz"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/merge-descriptors/-/merge-descriptors-1.0.1.tgz"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/merge-stream/-/merge-stream-2.0.0.tgz"

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/merge2/-/merge2-1.4.1.tgz"

methods@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/methods/-/methods-1.1.2.tgz"

micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/micromatch/-/micromatch-4.0.5.tgz"
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0, "mime-db@>= 1.43.0 < 2":
  version "1.52.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mime-db/-/mime-db-1.52.0.tgz"

mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mime-types/-/mime-types-2.1.35.tgz"
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mime/-/mime-1.6.0.tgz"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mimic-fn/-/mimic-fn-2.1.0.tgz"

mini-css-extract-plugin@^2.4.5:
  version "2.7.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mini-css-extract-plugin/-/mini-css-extract-plugin-2.7.6.tgz"
  dependencies:
    schema-utils "^4.0.0"

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimatch/-/minimatch-3.1.2.tgz"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimatch/-/minimatch-5.1.6.tgz"
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/minimist/-/minimist-1.2.8.tgz"

mkdirp@~0.5.1:
  version "0.5.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/mkdirp/-/mkdirp-0.5.6.tgz"
  dependencies:
    minimist "^1.2.6"

ms@2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ms/-/ms-2.0.0.tgz"

ms@2.1.2:
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ms/-/ms-2.1.2.tgz"

ms@2.1.3, ms@^2.1.1:
  version "2.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ms/-/ms-2.1.3.tgz"

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/multicast-dns/-/multicast-dns-7.2.5.tgz"
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

nanoid@^3.0.0, nanoid@^3.3.6:
  version "3.3.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/nanoid/-/nanoid-3.3.6.tgz"

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/natural-compare/-/natural-compare-1.4.0.tgz"

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/negotiator/-/negotiator-0.6.3.tgz"

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/neo-async/-/neo-async-2.6.2.tgz"

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/no-case/-/no-case-3.0.4.tgz"
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-forge@^1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/node-forge/-/node-forge-1.3.1.tgz"

node-releases@^2.0.12:
  version "2.0.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/node-releases/-/node-releases-2.0.12.tgz"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/normalize-path/-/normalize-path-3.0.0.tgz"

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/normalize-range/-/normalize-range-0.1.2.tgz"

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/normalize-url/-/normalize-url-6.1.0.tgz"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/npm-run-path/-/npm-run-path-4.0.1.tgz"
  dependencies:
    path-key "^3.0.0"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/nth-check/-/nth-check-1.0.2.tgz"
  dependencies:
    boolbase "~1.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/nth-check/-/nth-check-2.1.1.tgz"
  dependencies:
    boolbase "^1.0.0"

object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-assign/-/object-assign-4.1.1.tgz"

object-inspect@^1.12.3, object-inspect@^1.9.0:
  version "1.12.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-inspect/-/object-inspect-1.12.3.tgz"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-keys/-/object-keys-1.1.1.tgz"

object-unfreeze@^1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object-unfreeze/-/object-unfreeze-1.1.0.tgz"

object.assign@^4.1.3, object.assign@^4.1.4:
  version "4.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.assign/-/object.assign-4.1.4.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.6:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.entries/-/object.entries-1.1.6.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.fromentries@^2.0.6:
  version "2.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.fromentries/-/object.fromentries-2.0.6.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.getownpropertydescriptors@^2.1.0:
  version "2.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.6.tgz"
  dependencies:
    array.prototype.reduce "^1.0.5"
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.21.2"
    safe-array-concat "^1.0.0"

object.hasown@^1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.hasown/-/object.hasown-1.1.2.tgz"
  dependencies:
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

object.values@^1.1.0, object.values@^1.1.6:
  version "1.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/object.values/-/object.values-1.1.6.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/obuf/-/obuf-1.1.2.tgz"

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/on-finished/-/on-finished-2.4.1.tgz"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/on-headers/-/on-headers-1.0.2.tgz"

once@^1.3.0:
  version "1.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/once/-/once-1.4.0.tgz"
  dependencies:
    wrappy "1"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/onetime/-/onetime-5.1.2.tgz"
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.9, open@^8.4.0:
  version "8.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/open/-/open-8.4.2.tgz"
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/optionator/-/optionator-0.9.1.tgz"
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-limit/-/p-limit-2.3.0.tgz"
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-limit/-/p-limit-3.1.0.tgz"
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-locate/-/p-locate-3.0.0.tgz"
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-locate/-/p-locate-4.1.0.tgz"
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-locate/-/p-locate-5.0.0.tgz"
  dependencies:
    p-limit "^3.0.2"

p-retry@^4.5.0:
  version "4.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-retry/-/p-retry-4.6.2.tgz"
  dependencies:
    "@types/retry" "0.12.0"
    retry "^0.13.1"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/p-try/-/p-try-2.2.0.tgz"

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/param-case/-/param-case-3.0.4.tgz"
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/parent-module/-/parent-module-1.0.1.tgz"
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/parse-json/-/parse-json-5.2.0.tgz"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/parseurl/-/parseurl-1.3.3.tgz"

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pascal-case/-/pascal-case-3.1.2.tgz"
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-exists/-/path-exists-3.0.0.tgz"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-exists/-/path-exists-4.0.0.tgz"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-key/-/path-key-3.1.1.tgz"

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-parse/-/path-parse-1.0.7.tgz"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-to-regexp/-/path-to-regexp-0.1.7.tgz"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/path-type/-/path-type-4.0.0.tgz"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/performance-now/-/performance-now-2.1.0.tgz"

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/picocolors/-/picocolors-1.0.0.tgz"

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.2, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/picomatch/-/picomatch-2.3.1.tgz"

pkg-dir@^4.1.0, pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pkg-dir/-/pkg-dir-4.2.0.tgz"
  dependencies:
    find-up "^4.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pkg-up/-/pkg-up-3.1.0.tgz"
  dependencies:
    find-up "^3.0.0"

postcss-attribute-case-insensitive@^5.0.2:
  version "5.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-5.0.2.tgz"
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-browser-comments@^4:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-browser-comments/-/postcss-browser-comments-4.0.0.tgz"

postcss-calc@^8.2.3:
  version "8.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-calc/-/postcss-calc-8.2.4.tgz"
  dependencies:
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"

postcss-clamp@^4.1.0:
  version "4.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-clamp/-/postcss-clamp-4.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-functional-notation@^4.2.4:
  version "4.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-color-functional-notation/-/postcss-color-functional-notation-4.2.4.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-hex-alpha@^8.0.4:
  version "8.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-color-hex-alpha/-/postcss-color-hex-alpha-8.0.4.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-rebeccapurple@^7.1.1:
  version "7.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-7.1.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-colormin@^5.3.1:
  version "5.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-colormin/-/postcss-colormin-5.3.1.tgz"
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    colord "^2.9.1"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^5.1.3:
  version "5.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz"
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-custom-media@^8.0.2:
  version "8.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-custom-media/-/postcss-custom-media-8.0.2.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-custom-properties@^12.1.10:
  version "12.1.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-custom-properties/-/postcss-custom-properties-12.1.11.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-custom-selectors@^6.0.3:
  version "6.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-custom-selectors/-/postcss-custom-selectors-6.0.3.tgz"
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-dir-pseudo-class@^6.0.5:
  version "6.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-6.0.5.tgz"
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-discard-comments@^5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz"

postcss-discard-duplicates@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz"

postcss-discard-empty@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz"

postcss-discard-overridden@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz"

postcss-double-position-gradients@^3.1.2:
  version "3.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-double-position-gradients/-/postcss-double-position-gradients-3.1.2.tgz"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

postcss-env-function@^4.0.6:
  version "4.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-env-function/-/postcss-env-function-4.0.6.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-flexbugs-fixes@^5.0.2:
  version "5.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-5.0.2.tgz"

postcss-focus-visible@^6.0.4:
  version "6.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-focus-visible/-/postcss-focus-visible-6.0.4.tgz"
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-focus-within@^5.0.4:
  version "5.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-focus-within/-/postcss-focus-within-5.0.4.tgz"
  dependencies:
    postcss-selector-parser "^6.0.9"

postcss-font-variant@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz"

postcss-gap-properties@^3.0.5:
  version "3.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-gap-properties/-/postcss-gap-properties-3.0.5.tgz"

postcss-image-set-function@^4.0.7:
  version "4.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-image-set-function/-/postcss-image-set-function-4.0.7.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-initial@^4.0.1:
  version "4.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-initial/-/postcss-initial-4.0.1.tgz"

postcss-lab-function@^4.2.1:
  version "4.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-lab-function/-/postcss-lab-function-4.2.1.tgz"
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^1.1.0"
    postcss-value-parser "^4.2.0"

postcss-loader@^6.2.1:
  version "6.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-loader/-/postcss-loader-6.2.1.tgz"
  dependencies:
    cosmiconfig "^7.0.0"
    klona "^2.0.5"
    semver "^7.3.5"

postcss-logical@^5.0.4:
  version "5.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-logical/-/postcss-logical-5.0.4.tgz"

postcss-media-minmax@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-media-minmax/-/postcss-media-minmax-5.0.0.tgz"

postcss-merge-longhand@^5.1.7:
  version "5.1.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^5.1.1"

postcss-merge-rules@^5.1.4:
  version "5.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz"
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    cssnano-utils "^3.1.0"
    postcss-selector-parser "^6.0.5"

postcss-minify-font-values@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz"
  dependencies:
    colord "^2.9.1"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^5.1.4:
  version "5.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz"
  dependencies:
    browserslist "^4.21.4"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^5.2.1:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz"
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.0.0.tgz"

postcss-modules-local-by-default@^4.0.0, postcss-modules-local-by-default@^4.0.3:
  version "4.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.3.tgz"
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-scope/-/postcss-modules-scope-3.0.0.tgz"
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  dependencies:
    icss-utils "^5.0.0"

postcss-nesting@^10.2.0:
  version "10.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-nesting/-/postcss-nesting-10.2.0.tgz"
  dependencies:
    "@csstools/selector-specificity" "^2.0.0"
    postcss-selector-parser "^6.0.10"

postcss-normalize-charset@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz"

postcss-normalize-display-values@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz"
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz"
  dependencies:
    normalize-url "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize@^10.0.1:
  version "10.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-normalize/-/postcss-normalize-10.0.1.tgz"
  dependencies:
    "@csstools/normalize.css" "*"
    postcss-browser-comments "^4"
    sanitize.css "*"

postcss-opacity-percentage@^1.1.2:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-opacity-percentage/-/postcss-opacity-percentage-1.1.3.tgz"

postcss-ordered-values@^5.1.3:
  version "5.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz"
  dependencies:
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-overflow-shorthand@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-overflow-shorthand/-/postcss-overflow-shorthand-3.0.4.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-page-break@^3.0.4:
  version "3.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-page-break/-/postcss-page-break-3.0.4.tgz"

postcss-place@^7.0.5:
  version "7.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-place/-/postcss-place-7.0.5.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-preset-env@^7.0.1:
  version "7.8.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-preset-env/-/postcss-preset-env-7.8.3.tgz"
  dependencies:
    "@csstools/postcss-cascade-layers" "^1.1.1"
    "@csstools/postcss-color-function" "^1.1.1"
    "@csstools/postcss-font-format-keywords" "^1.0.1"
    "@csstools/postcss-hwb-function" "^1.0.2"
    "@csstools/postcss-ic-unit" "^1.0.1"
    "@csstools/postcss-is-pseudo-class" "^2.0.7"
    "@csstools/postcss-nested-calc" "^1.0.0"
    "@csstools/postcss-normalize-display-values" "^1.0.1"
    "@csstools/postcss-oklab-function" "^1.1.1"
    "@csstools/postcss-progressive-custom-properties" "^1.3.0"
    "@csstools/postcss-stepped-value-functions" "^1.0.1"
    "@csstools/postcss-text-decoration-shorthand" "^1.0.0"
    "@csstools/postcss-trigonometric-functions" "^1.0.2"
    "@csstools/postcss-unset-value" "^1.0.2"
    autoprefixer "^10.4.13"
    browserslist "^4.21.4"
    css-blank-pseudo "^3.0.3"
    css-has-pseudo "^3.0.4"
    css-prefers-color-scheme "^6.0.3"
    cssdb "^7.1.0"
    postcss-attribute-case-insensitive "^5.0.2"
    postcss-clamp "^4.1.0"
    postcss-color-functional-notation "^4.2.4"
    postcss-color-hex-alpha "^8.0.4"
    postcss-color-rebeccapurple "^7.1.1"
    postcss-custom-media "^8.0.2"
    postcss-custom-properties "^12.1.10"
    postcss-custom-selectors "^6.0.3"
    postcss-dir-pseudo-class "^6.0.5"
    postcss-double-position-gradients "^3.1.2"
    postcss-env-function "^4.0.6"
    postcss-focus-visible "^6.0.4"
    postcss-focus-within "^5.0.4"
    postcss-font-variant "^5.0.0"
    postcss-gap-properties "^3.0.5"
    postcss-image-set-function "^4.0.7"
    postcss-initial "^4.0.1"
    postcss-lab-function "^4.2.1"
    postcss-logical "^5.0.4"
    postcss-media-minmax "^5.0.0"
    postcss-nesting "^10.2.0"
    postcss-opacity-percentage "^1.1.2"
    postcss-overflow-shorthand "^3.0.4"
    postcss-page-break "^3.0.4"
    postcss-place "^7.0.5"
    postcss-pseudo-class-any-link "^7.1.6"
    postcss-replace-overflow-wrap "^4.0.0"
    postcss-selector-not "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-pseudo-class-any-link@^7.1.6:
  version "7.1.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-7.1.6.tgz"
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-reduce-initial@^5.1.2:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz"
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-replace-overflow-wrap@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz"

postcss-scss@^4.0.6:
  version "4.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-scss/-/postcss-scss-4.0.6.tgz"

postcss-selector-not@^6.0.1:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-selector-not/-/postcss-selector-not-6.0.1.tgz"
  dependencies:
    postcss-selector-parser "^6.0.10"

postcss-selector-parser@^6.0.10, postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.9:
  version "6.0.13"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz"
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^5.1.0:
  version "5.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-svgo/-/postcss-svgo-5.1.0.tgz"
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^2.7.0"

postcss-unique-selectors@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz"
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"

postcss@^8.2.14, postcss@^8.4.17, postcss@^8.4.21, postcss@^8.4.4:
  version "8.4.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/postcss/-/postcss-8.4.24.tgz"
  dependencies:
    nanoid "^3.3.6"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/prelude-ls/-/prelude-ls-1.2.1.tgz"

pretty-bytes@^5.3.0, pretty-bytes@^5.4.1:
  version "5.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pretty-bytes/-/pretty-bytes-5.6.0.tgz"

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/pretty-error/-/pretty-error-4.0.0.tgz"
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/process-nextick-args/-/process-nextick-args-2.0.1.tgz"

promise@^8.1.0:
  version "8.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/promise/-/promise-8.3.0.tgz"
  dependencies:
    asap "~2.0.6"

prompts@^2.4.2:
  version "2.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/prompts/-/prompts-2.4.2.tgz"
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.5.10, prop-types@^15.5.8, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/prop-types/-/prop-types-15.8.1.tgz"
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.5:
  version "2.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/property-expr/-/property-expr-2.0.5.tgz"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/proxy-addr/-/proxy-addr-2.0.7.tgz"
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

punycode@^2.1.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/punycode/-/punycode-2.3.0.tgz"

q@^1.1.2:
  version "1.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/q/-/q-1.5.1.tgz"

qs@6.11.0:
  version "6.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/qs/-/qs-6.11.0.tgz"
  dependencies:
    side-channel "^1.0.4"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/queue-microtask/-/queue-microtask-1.2.3.tgz"

raf@^3.4.1:
  version "3.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/raf/-/raf-3.4.1.tgz"
  dependencies:
    performance-now "^2.1.0"

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/randombytes/-/randombytes-2.1.0.tgz"
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/range-parser/-/range-parser-1.2.1.tgz"

raw-body@2.5.1:
  version "2.5.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/raw-body/-/raw-body-2.5.1.tgz"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-app-polyfill@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-app-polyfill/-/react-app-polyfill-3.0.0.tgz"
  dependencies:
    core-js "^3.19.2"
    object-assign "^4.1.1"
    promise "^8.1.0"
    raf "^3.4.1"
    regenerator-runtime "^0.13.9"
    whatwg-fetch "^3.6.2"

react-checkbox-tree@^1.8.0:
  version "1.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-checkbox-tree/-/react-checkbox-tree-1.8.0.tgz"
  dependencies:
    classnames "^2.2.5"
    lodash "^4.17.10"
    nanoid "^3.0.0"
    prop-types "^15.5.8"

react-color@^2.19.3:
  version "2.19.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-color/-/react-color-2.19.3.tgz"
  dependencies:
    "@icons/material" "^0.2.4"
    lodash "^4.17.15"
    lodash-es "^4.17.15"
    material-colors "^1.2.1"
    prop-types "^15.5.10"
    reactcss "^1.2.0"
    tinycolor2 "^1.4.1"

react-css-modules@^4.7.11:
  version "4.7.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-css-modules/-/react-css-modules-4.7.11.tgz"
  dependencies:
    hoist-non-react-statics "^2.5.5"
    lodash "^4.16.6"
    object-unfreeze "^1.1.0"

react-dev-utils@^12.0.1:
  version "12.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-dev-utils/-/react-dev-utils-12.0.1.tgz"
  dependencies:
    "@babel/code-frame" "^7.16.0"
    address "^1.1.2"
    browserslist "^4.18.1"
    chalk "^4.1.2"
    cross-spawn "^7.0.3"
    detect-port-alt "^1.1.6"
    escape-string-regexp "^4.0.0"
    filesize "^8.0.6"
    find-up "^5.0.0"
    fork-ts-checker-webpack-plugin "^6.5.0"
    global-modules "^2.0.0"
    globby "^11.0.4"
    gzip-size "^6.0.0"
    immer "^9.0.7"
    is-root "^2.1.0"
    loader-utils "^3.2.0"
    open "^8.4.0"
    pkg-up "^3.1.0"
    prompts "^2.4.2"
    react-error-overlay "^6.0.11"
    recursive-readdir "^2.2.2"
    shell-quote "^1.7.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

react-dom@^18.2.0:
  version "18.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-dom/-/react-dom-18.2.0.tgz"
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.0"

react-draggable@^4.4.5:
  version "4.4.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-draggable/-/react-draggable-4.4.5.tgz"
  dependencies:
    clsx "^1.1.1"
    prop-types "^15.8.1"

react-dropzone@^14.2.3:
  version "14.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-dropzone/-/react-dropzone-14.2.3.tgz"
  dependencies:
    attr-accept "^2.2.2"
    file-selector "^0.6.0"
    prop-types "^15.8.1"

react-error-overlay@^6.0.11:
  version "6.0.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-error-overlay/-/react-error-overlay-6.0.11.tgz"

react-fast-compare@^2.0.1:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-fast-compare/-/react-fast-compare-2.0.4.tgz"

react-infinite-scroll-component@^6.1.0:
  version "6.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-infinite-scroll-component/-/react-infinite-scroll-component-6.1.0.tgz"
  dependencies:
    throttle-debounce "^2.1.0"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-is/-/react-is-16.13.1.tgz"

react-is@^18.2.0:
  version "18.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-is/-/react-is-18.2.0.tgz"

react-refresh@^0.11.0:
  version "0.11.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-refresh/-/react-refresh-0.11.0.tgz"

react-router-dom@^6.8.2:
  version "6.14.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-router-dom/-/react-router-dom-6.14.0.tgz"
  dependencies:
    "@remix-run/router" "1.7.0"
    react-router "6.14.0"

react-router@6.14.0:
  version "6.14.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-router/-/react-router-6.14.0.tgz"
  dependencies:
    "@remix-run/router" "1.7.0"

react-transition-group@^4.4.5:
  version "4.4.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react-transition-group/-/react-transition-group-4.4.5.tgz"
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@^18.2.0:
  version "18.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/react/-/react-18.2.0.tgz"
  dependencies:
    loose-envify "^1.1.0"

reactcss@^1.2.0:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/reactcss/-/reactcss-1.2.3.tgz"
  dependencies:
    lodash "^4.0.1"

readable-stream@^2.0.1:
  version "2.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/readable-stream/-/readable-stream-2.3.8.tgz"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6:
  version "3.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/readable-stream/-/readable-stream-3.6.2.tgz"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/readdirp/-/readdirp-3.6.0.tgz"
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.8.0:
  version "0.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rechoir/-/rechoir-0.8.0.tgz"
  dependencies:
    resolve "^1.20.0"

recursive-readdir@^2.2.2:
  version "2.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/recursive-readdir/-/recursive-readdir-2.2.3.tgz"
  dependencies:
    minimatch "^3.0.5"

redi-component-utils@^1.0.21:
  version "1.0.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-component-utils/-/redi-component-utils-1.0.21.tgz"

redi-formik-material@^2.0.10:
  version "2.0.12"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-formik-material/-/redi-formik-material-2.0.12.tgz#9cc03523942a99f048e2066d7d9cd4e578c8f4fd"
  integrity sha1-nMA1I5QqmfBI4gZtfZzU5XjI9P0=

redi-http@^2.2.10:
  version "2.2.10"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-http/-/redi-http-2.2.10.tgz#6fe041e2a57d5443d4fc047bc5b435c49ad321ac"
  integrity sha1-b+BB4qV9VEPU/AR7xbQ1xJrTIaw=

redi-query-builder@^1.1.17:
  version "1.1.17"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-query-builder/-/redi-query-builder-1.1.17.tgz"
  optionalDependencies:
    redi-redux-persist "^1.0.*"

redi-redux-persist@^1.0.*:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-redux-persist/-/redi-redux-persist-1.0.2.tgz"
  dependencies:
    date-fns "^2.8.1"
    redux "^4.0.1"
    redux-persist "^5.10.0"

redi-security-components@1.0.7:
  version "1.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-security-components/-/redi-security-components-1.0.7.tgz#c5bc5d830f109de17d1f0e774357bf734b9d364d"
  integrity sha1-xbxdgw8QneF9Hw53Q1e/c0udNk0=

redi-ui-utils@^2.0.0:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redi-ui-utils/-/redi-ui-utils-2.0.2.tgz"

redux-persist@^5.10.0:
  version "5.10.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redux-persist/-/redux-persist-5.10.0.tgz"

redux@^4.0.1:
  version "4.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/redux/-/redux-4.2.1.tgz"
  dependencies:
    "@babel/runtime" "^7.9.2"

regenerate-unicode-properties@^10.1.0:
  version "10.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz"
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerate/-/regenerate-1.4.2.tgz"

regenerator-runtime@^0.13.11, regenerator-runtime@^0.13.9:
  version "0.13.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"

regenerator-transform@^0.15.1:
  version "0.15.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regenerator-transform/-/regenerator-transform-0.15.1.tgz"
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-parser@^2.2.11:
  version "2.2.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regex-parser/-/regex-parser-2.2.11.tgz"

regexp.prototype.flags@^1.4.3:
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    functions-have-names "^1.2.3"

regexpu-core@^5.3.1:
  version "5.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regexpu-core/-/regexpu-core-5.3.2.tgz"
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/regjsparser/-/regjsparser-0.9.1.tgz"
  dependencies:
    jsesc "~0.5.0"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/relateurl/-/relateurl-0.2.7.tgz"

remove-accents@^0.4.2:
  version "0.4.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/remove-accents/-/remove-accents-0.4.4.tgz#73704abf7dae3764295d475d2b6afac4ea23e4d9"
  integrity sha1-c3BKv32uN2QpXUddK2r6xOoj5Nk=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/renderkid/-/renderkid-3.0.0.tgz"
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/require-from-string/-/require-from-string-2.0.2.tgz"

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/requires-port/-/requires-port-1.0.0.tgz"

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-from/-/resolve-from-4.0.0.tgz"

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-from/-/resolve-from-5.0.0.tgz"

resolve-url-loader@^5.0.0:
  version "5.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve-url-loader/-/resolve-url-loader-5.0.0.tgz"
  dependencies:
    adjust-sourcemap-loader "^4.0.0"
    convert-source-map "^1.7.0"
    loader-utils "^2.0.0"
    postcss "^8.2.14"
    source-map "0.6.1"

resolve@^1.14.2, resolve@^1.19.0, resolve@^1.20.0, resolve@^1.22.1:
  version "1.22.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve/-/resolve-1.22.2.tgz"
  dependencies:
    is-core-module "^2.11.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.4:
  version "2.0.0-next.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/resolve/-/resolve-2.0.0-next.4.tgz"
  dependencies:
    is-core-module "^2.9.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/retry/-/retry-0.13.1.tgz"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/reusify/-/reusify-1.0.4.tgz"

rifm@^0.12.1:
  version "0.12.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rifm/-/rifm-0.12.1.tgz"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rimraf/-/rimraf-3.0.2.tgz"
  dependencies:
    glob "^7.1.3"

rollup-plugin-terser@^7.0.0:
  version "7.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rollup-plugin-terser/-/rollup-plugin-terser-7.0.2.tgz"
  dependencies:
    "@babel/code-frame" "^7.10.4"
    jest-worker "^26.2.1"
    serialize-javascript "^4.0.0"
    terser "^5.0.0"

rollup@^2.43.1:
  version "2.79.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/rollup/-/rollup-2.79.1.tgz"
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/run-parallel/-/run-parallel-1.2.0.tgz"
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-array-concat/-/safe-array-concat-1.0.0.tgz"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-buffer/-/safe-buffer-5.1.2.tgz"

safe-buffer@5.2.1, safe-buffer@>=5.1.0, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-buffer/-/safe-buffer-5.2.1.tgz"

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safe-regex-test/-/safe-regex-test-1.0.0.tgz"
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/safer-buffer/-/safer-buffer-2.1.2.tgz"

sanitize.css@*:
  version "13.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sanitize.css/-/sanitize.css-13.0.0.tgz"

sass-loader@^12.3.0:
  version "12.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sass-loader/-/sass-loader-12.6.0.tgz"
  dependencies:
    klona "^2.0.4"
    neo-async "^2.6.2"

sass@^1.58.3:
  version "1.63.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sass/-/sass-1.63.6.tgz"
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

sax@~1.2.4:
  version "1.2.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sax/-/sax-1.2.4.tgz"

scheduler@^0.23.0:
  version "0.23.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/scheduler/-/scheduler-0.23.0.tgz"
  dependencies:
    loose-envify "^1.1.0"

schema-utils@2.7.0:
  version "2.7.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-2.7.0.tgz"
  dependencies:
    "@types/json-schema" "^7.0.4"
    ajv "^6.12.2"
    ajv-keywords "^3.4.1"

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-2.7.1.tgz"
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0, schema-utils@^3.1.1, schema-utils@^3.2.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-3.3.0.tgz"
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/schema-utils/-/schema-utils-4.2.0.tgz"
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/select-hose/-/select-hose-2.0.0.tgz"

selfsigned@^2.1.1:
  version "2.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/selfsigned/-/selfsigned-2.1.1.tgz"
  dependencies:
    node-forge "^1"

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/semver/-/semver-6.3.0.tgz"

semver@^7.3.2, semver@^7.3.5, semver@^7.3.7, semver@^7.3.8:
  version "7.5.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/semver/-/semver-7.5.3.tgz"
  dependencies:
    lru-cache "^6.0.0"

send@0.18.0:
  version "0.18.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/send/-/send-0.18.0.tgz"
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serialize-javascript/-/serialize-javascript-4.0.0.tgz"
  dependencies:
    randombytes "^2.1.0"

serialize-javascript@^6.0.0, serialize-javascript@^6.0.1:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serialize-javascript/-/serialize-javascript-6.0.1.tgz"
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serve-index/-/serve-index-1.9.1.tgz"
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/serve-static/-/serve-static-1.15.0.tgz"
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/setprototypeof/-/setprototypeof-1.1.0.tgz"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/setprototypeof/-/setprototypeof-1.2.0.tgz"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shallow-clone/-/shallow-clone-3.0.1.tgz"
  dependencies:
    kind-of "^6.0.2"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shebang-command/-/shebang-command-2.0.0.tgz"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shebang-regex/-/shebang-regex-3.0.0.tgz"

shell-quote@^1.7.3:
  version "1.8.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/shell-quote/-/shell-quote-1.8.1.tgz"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/side-channel/-/side-channel-1.0.4.tgz"
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/signal-exit/-/signal-exit-3.0.7.tgz"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sisteransi/-/sisteransi-1.0.5.tgz"

slash@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/slash/-/slash-3.0.0.tgz"

sockjs@^0.3.24:
  version "0.3.24"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sockjs/-/sockjs-0.3.24.tgz"
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

source-list-map@^2.0.0, source-list-map@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-list-map/-/source-list-map-2.0.1.tgz"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.0.1, source-map-js@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map-js/-/source-map-js-1.0.2.tgz"

source-map-loader@^3.0.0:
  version "3.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map-loader/-/source-map-loader-3.0.2.tgz"
  dependencies:
    abab "^2.0.5"
    iconv-lite "^0.6.3"
    source-map-js "^1.0.1"

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map-support/-/source-map-support-0.5.21.tgz"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map-url/-/source-map-url-0.4.1.tgz"

source-map@0.6.1, source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.6.1.tgz"

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.5.7.tgz"

source-map@^0.7.3:
  version "0.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.7.4.tgz"

source-map@^0.8.0-beta.0:
  version "0.8.0-beta.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/source-map/-/source-map-0.8.0-beta.0.tgz"
  dependencies:
    whatwg-url "^7.0.0"

sourcemap-codec@^1.4.8:
  version "1.4.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz"

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/spdy-transport/-/spdy-transport-3.0.0.tgz"
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/spdy/-/spdy-4.0.2.tgz"
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/sprintf-js/-/sprintf-js-1.0.3.tgz"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stable/-/stable-0.1.8.tgz"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stackframe/-/stackframe-1.3.4.tgz"

statuses@2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/statuses/-/statuses-2.0.1.tgz"

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/statuses/-/statuses-1.5.0.tgz"

string-natural-compare@^3.0.1:
  version "3.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string-natural-compare/-/string-natural-compare-3.0.1.tgz"

string.prototype.matchall@^4.0.6, string.prototype.matchall@^4.0.8:
  version "4.0.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.matchall/-/string.prototype.matchall-4.0.8.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"
    get-intrinsic "^1.1.3"
    has-symbols "^1.0.3"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.4.3"
    side-channel "^1.0.4"

string.prototype.trim@^1.2.7:
  version "1.2.7"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.trim/-/string.prototype.trim-1.2.7.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimend@^1.0.6:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string.prototype.trimstart@^1.0.6:
  version "1.0.6"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string.prototype.trimstart/-/string.prototype.trimstart-1.0.6.tgz"
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    es-abstract "^1.20.4"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string_decoder/-/string_decoder-1.3.0.tgz"
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/string_decoder/-/string_decoder-1.1.1.tgz"
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stringify-object/-/stringify-object-3.3.0.tgz"
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-ansi/-/strip-ansi-6.0.1.tgz"
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-bom/-/strip-bom-3.0.0.tgz"

strip-comments@^2.0.1:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-comments/-/strip-comments-2.0.1.tgz"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-final-newline/-/strip-final-newline-2.0.0.tgz"

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/strip-json-comments/-/strip-json-comments-3.1.1.tgz"

style-loader@^3.3.1:
  version "3.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/style-loader/-/style-loader-3.3.3.tgz"

stylehacks@^5.1.1:
  version "5.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stylehacks/-/stylehacks-5.1.1.tgz"
  dependencies:
    browserslist "^4.21.4"
    postcss-selector-parser "^6.0.4"

stylis@4.2.0:
  version "4.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/stylis/-/stylis-4.2.0.tgz"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-color/-/supports-color-5.5.0.tgz"
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-color/-/supports-color-7.2.0.tgz"
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-color/-/supports-color-8.1.1.tgz"
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"

svg-parser@^2.0.2:
  version "2.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/svg-parser/-/svg-parser-2.0.4.tgz"

svgo@^1.2.2:
  version "1.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/svgo/-/svgo-1.3.2.tgz"
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.37"
    csso "^4.0.2"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

svgo@^2.7.0:
  version "2.8.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/svgo/-/svgo-2.8.0.tgz"
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^4.1.3"
    css-tree "^1.1.3"
    csso "^4.2.0"
    picocolors "^1.0.0"
    stable "^0.1.8"

tapable@^1.0.0:
  version "1.1.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tapable/-/tapable-1.1.3.tgz"

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tapable/-/tapable-2.2.1.tgz"

temp-dir@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/temp-dir/-/temp-dir-2.0.0.tgz"

tempy@^0.6.0:
  version "0.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tempy/-/tempy-0.6.0.tgz"
  dependencies:
    is-stream "^2.0.0"
    temp-dir "^2.0.0"
    type-fest "^0.16.0"
    unique-string "^2.0.0"

terser-webpack-plugin@^5.2.5, terser-webpack-plugin@^5.3.7:
  version "5.3.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/terser-webpack-plugin/-/terser-webpack-plugin-5.3.9.tgz"
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.17"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.16.8"

terser@^5.0.0, terser@^5.10.0, terser@^5.16.8:
  version "5.18.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/terser/-/terser-5.18.1.tgz"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/text-table/-/text-table-0.2.0.tgz"

throttle-debounce@^2.1.0:
  version "2.3.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/throttle-debounce/-/throttle-debounce-2.3.0.tgz"

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/thunky/-/thunky-1.1.0.tgz"

tiny-case@^1.0.3:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tiny-case/-/tiny-case-1.0.3.tgz"

tiny-warning@^1.0.2:
  version "1.0.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tiny-warning/-/tiny-warning-1.0.3.tgz"

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tinycolor2/-/tinycolor2-1.6.0.tgz"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/to-fast-properties/-/to-fast-properties-2.0.0.tgz"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/to-regex-range/-/to-regex-range-5.0.1.tgz"
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/toidentifier/-/toidentifier-1.0.1.tgz"

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/toposort/-/toposort-2.0.2.tgz"

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tr46/-/tr46-1.0.1.tgz"
  dependencies:
    punycode "^2.1.0"

tsconfig-paths@^3.14.1:
  version "3.14.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tsconfig-paths/-/tsconfig-paths-3.14.2.tgz"
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tslib/-/tslib-1.14.1.tgz"

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.4.0:
  version "2.6.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tslib/-/tslib-2.6.0.tgz"

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/tsutils/-/tsutils-3.21.0.tgz"
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-check/-/type-check-0.4.0.tgz"
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.16.0:
  version "0.16.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-fest/-/type-fest-0.16.0.tgz"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-fest/-/type-fest-0.20.2.tgz"

type-fest@^2.19.0:
  version "2.19.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-fest/-/type-fest-2.19.0.tgz"

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/type-is/-/type-is-1.6.18.tgz"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-length@^1.0.4:
  version "1.0.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typed-array-length/-/typed-array-length-1.0.4.tgz"
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typescript@^4.9.5:
  version "4.9.5"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/typescript/-/typescript-4.9.5.tgz"

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz"

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unique-string/-/unique-string-2.0.0.tgz"
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/universalify/-/universalify-2.0.0.tgz"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unpipe/-/unpipe-1.0.0.tgz"

unquote@~1.1.1:
  version "1.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/unquote/-/unquote-1.1.1.tgz"

upath@^1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/upath/-/upath-1.2.0.tgz"

update-browserslist-db@^1.0.11:
  version "1.0.11"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz"
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/uri-js/-/uri-js-4.4.1.tgz"
  dependencies:
    punycode "^2.1.0"

use-sync-external-store@1.2.0:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/use-sync-external-store/-/use-sync-external-store-1.2.0.tgz"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/util-deprecate/-/util-deprecate-1.0.2.tgz"

util.promisify@~1.0.0:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/util.promisify/-/util.promisify-1.0.1.tgz"
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.2"
    has-symbols "^1.0.1"
    object.getownpropertydescriptors "^2.1.0"

utila@~0.4:
  version "0.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/utila/-/utila-0.4.0.tgz"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/utils-merge/-/utils-merge-1.0.1.tgz"

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/uuid/-/uuid-8.3.2.tgz"

uuid@^9.0.0:
  version "9.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/uuid/-/uuid-9.0.0.tgz"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/vary/-/vary-1.1.2.tgz"

watchpack@^2.4.0:
  version "2.4.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/watchpack/-/watchpack-2.4.0.tgz"
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/wbuf/-/wbuf-1.7.3.tgz"
  dependencies:
    minimalistic-assert "^1.0.0"

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webidl-conversions/-/webidl-conversions-4.0.2.tgz"

webpack-cli@^5.1.4:
  version "5.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-cli/-/webpack-cli-5.1.4.tgz"
  dependencies:
    "@discoveryjs/json-ext" "^0.5.0"
    "@webpack-cli/configtest" "^2.1.1"
    "@webpack-cli/info" "^2.0.2"
    "@webpack-cli/serve" "^2.0.5"
    colorette "^2.0.14"
    commander "^10.0.1"
    cross-spawn "^7.0.3"
    envinfo "^7.7.3"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^3.1.1"
    rechoir "^0.8.0"
    webpack-merge "^5.7.3"

webpack-dev-middleware@^5.3.1:
  version "5.3.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-dev-middleware/-/webpack-dev-middleware-5.3.3.tgz"
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.3"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.6.0:
  version "4.15.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-dev-server/-/webpack-dev-server-4.15.1.tgz"
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.5"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.0.11"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    default-gateway "^6.0.3"
    express "^4.17.3"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.3"
    ipaddr.js "^2.0.1"
    launch-editor "^2.6.0"
    open "^8.0.9"
    p-retry "^4.5.0"
    rimraf "^3.0.2"
    schema-utils "^4.0.0"
    selfsigned "^2.1.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^5.3.1"
    ws "^8.13.0"

webpack-manifest-plugin@^4.0.2:
  version "4.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-manifest-plugin/-/webpack-manifest-plugin-4.1.1.tgz"
  dependencies:
    tapable "^2.0.0"
    webpack-sources "^2.2.0"

webpack-merge@^5.7.3:
  version "5.9.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-merge/-/webpack-merge-5.9.0.tgz"
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

webpack-sources@^1.4.3:
  version "1.4.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-sources/-/webpack-sources-1.4.3.tgz"
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack-sources@^2.2.0:
  version "2.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-sources/-/webpack-sources-2.3.1.tgz"
  dependencies:
    source-list-map "^2.0.1"
    source-map "^0.6.1"

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack-sources/-/webpack-sources-3.2.3.tgz"

webpack@^5.64.4:
  version "5.88.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/webpack/-/webpack-5.88.0.tgz"
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^1.0.0"
    "@webassemblyjs/ast" "^1.11.5"
    "@webassemblyjs/wasm-edit" "^1.11.5"
    "@webassemblyjs/wasm-parser" "^1.11.5"
    acorn "^8.7.1"
    acorn-import-assertions "^1.9.0"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.15.0"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.7"
    watchpack "^2.4.0"
    webpack-sources "^3.2.3"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/websocket-driver/-/websocket-driver-0.7.4.tgz"
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/websocket-extensions/-/websocket-extensions-0.1.4.tgz"

whatwg-fetch@^3.6.2:
  version "3.6.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/whatwg-fetch/-/whatwg-fetch-3.6.2.tgz"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/whatwg-url/-/whatwg-url-7.1.0.tgz"
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-typed-array@^1.1.9:
  version "1.1.9"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which-typed-array/-/which-typed-array-1.1.9.tgz"
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"
    is-typed-array "^1.1.10"

which@^1.3.1:
  version "1.3.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which/-/which-1.3.1.tgz"
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/which/-/which-2.0.2.tgz"
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/wildcard/-/wildcard-2.0.1.tgz"

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/word-wrap/-/word-wrap-1.2.3.tgz"

workbox-background-sync@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-background-sync/-/workbox-background-sync-6.4.1.tgz"
  dependencies:
    idb "^6.1.4"
    workbox-core "6.4.1"

workbox-broadcast-update@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-broadcast-update/-/workbox-broadcast-update-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"

workbox-build@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-build/-/workbox-build-6.4.1.tgz"
  dependencies:
    "@apideck/better-ajv-errors" "^0.2.7"
    "@babel/core" "^7.11.1"
    "@babel/preset-env" "^7.11.0"
    "@babel/runtime" "^7.11.2"
    "@rollup/plugin-babel" "^5.2.0"
    "@rollup/plugin-node-resolve" "^11.2.1"
    "@rollup/plugin-replace" "^2.4.1"
    "@surma/rollup-plugin-off-main-thread" "^2.2.3"
    ajv "^8.6.0"
    common-tags "^1.8.0"
    fast-json-stable-stringify "^2.1.0"
    fs-extra "^9.0.1"
    glob "^7.1.6"
    lodash "^4.17.20"
    pretty-bytes "^5.3.0"
    rollup "^2.43.1"
    rollup-plugin-terser "^7.0.0"
    source-map "^0.8.0-beta.0"
    source-map-url "^0.4.0"
    stringify-object "^3.3.0"
    strip-comments "^2.0.1"
    tempy "^0.6.0"
    upath "^1.2.0"
    workbox-background-sync "6.4.1"
    workbox-broadcast-update "6.4.1"
    workbox-cacheable-response "6.4.1"
    workbox-core "6.4.1"
    workbox-expiration "6.4.1"
    workbox-google-analytics "6.4.1"
    workbox-navigation-preload "6.4.1"
    workbox-precaching "6.4.1"
    workbox-range-requests "6.4.1"
    workbox-recipes "6.4.1"
    workbox-routing "6.4.1"
    workbox-strategies "6.4.1"
    workbox-streams "6.4.1"
    workbox-sw "6.4.1"
    workbox-window "6.4.1"

workbox-cacheable-response@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-cacheable-response/-/workbox-cacheable-response-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"

workbox-core@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-core/-/workbox-core-6.4.1.tgz"

workbox-expiration@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-expiration/-/workbox-expiration-6.4.1.tgz"
  dependencies:
    idb "^6.1.4"
    workbox-core "6.4.1"

workbox-google-analytics@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-google-analytics/-/workbox-google-analytics-6.4.1.tgz"
  dependencies:
    workbox-background-sync "6.4.1"
    workbox-core "6.4.1"
    workbox-routing "6.4.1"
    workbox-strategies "6.4.1"

workbox-navigation-preload@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-navigation-preload/-/workbox-navigation-preload-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"

workbox-precaching@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-precaching/-/workbox-precaching-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"
    workbox-routing "6.4.1"
    workbox-strategies "6.4.1"

workbox-range-requests@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-range-requests/-/workbox-range-requests-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"

workbox-recipes@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-recipes/-/workbox-recipes-6.4.1.tgz"
  dependencies:
    workbox-cacheable-response "6.4.1"
    workbox-core "6.4.1"
    workbox-expiration "6.4.1"
    workbox-precaching "6.4.1"
    workbox-routing "6.4.1"
    workbox-strategies "6.4.1"

workbox-routing@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-routing/-/workbox-routing-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"

workbox-strategies@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-strategies/-/workbox-strategies-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"

workbox-streams@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-streams/-/workbox-streams-6.4.1.tgz"
  dependencies:
    workbox-core "6.4.1"
    workbox-routing "6.4.1"

workbox-sw@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-sw/-/workbox-sw-6.4.1.tgz"

workbox-webpack-plugin@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-webpack-plugin/-/workbox-webpack-plugin-6.4.1.tgz"
  dependencies:
    fast-json-stable-stringify "^2.1.0"
    pretty-bytes "^5.4.1"
    source-map-url "^0.4.0"
    upath "^1.2.0"
    webpack-sources "^1.4.3"
    workbox-build "6.4.1"

workbox-window@6.4.1:
  version "6.4.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/workbox-window/-/workbox-window-6.4.1.tgz"
  dependencies:
    "@types/trusted-types" "^2.0.2"
    workbox-core "6.4.1"

wrappy@1:
  version "1.0.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/wrappy/-/wrappy-1.0.2.tgz"

ws@^8.13.0:
  version "8.13.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/ws/-/ws-8.13.0.tgz"

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yallist/-/yallist-3.1.1.tgz"

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yallist/-/yallist-4.0.0.tgz"

yaml@^1.10.0, yaml@^1.10.2, yaml@^1.7.2:
  version "1.10.2"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yaml/-/yaml-1.10.2.tgz"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yocto-queue/-/yocto-queue-0.1.0.tgz"

yup@*, yup@^1.0.2:
  version "1.2.0"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/yup/-/yup-1.2.0.tgz"
  dependencies:
    property-expr "^2.0.5"
    tiny-case "^1.0.3"
    toposort "^2.0.2"
    type-fest "^2.19.0"

zustand@^4.3.5:
  version "4.3.8"
  resolved "https://www.myget.org/F/redisoft-components/auth/b9777768-ec53-4f42-8f63-47563fbe1aaa/npm/zustand/-/zustand-4.3.8.tgz"
  dependencies:
    use-sync-external-store "1.2.0"
